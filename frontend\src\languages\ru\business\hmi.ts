export default {
  device: {
    configure: {
      selectConfigure: "Пожалуйста, выберите файл символов конфигурации!",
      loading: "Загрузка",
      operating: "В процессе операции",
      loadFailed: "Загрузка не удалась, причина:",
      getCustomDeviceFailed: "Не удалось получить пользовательское устройство",
      registerDataFailed: "Регистрация данных не удалась, причина:",
      variablesNotExist: "Некоторые переменные не существуют:",
      getDataError: "Ошибка получения данных",
      remoteSet: "Дистанционная настройка",
      remoteControlFailed: "Дистанционное управление не удалось, причина:",
      remoteControlSuccess: "Дистанционное управление успешно",
      noRemoteControlType: "Тип дистанционного управления не настроен",
      symbolChangeReload: "Изменение символа, перезагрузка",
      deviceChangeReload: "Изменение устройства, перезагрузка",
      deviceConnectReload: "Устройство подключено успешно, перезагрузка"
    },

    configureList: {
      searchPlaceholder: "Поиск по ключевым словам",
      deviceMonitor: "Мониторинг устройства",
      newProject: "Новый проект",
      addCustomComponent: "Добавить пользовательский компонент",
      newConfigure: "Новая конфигурация",
      renameProject: "Переименовать проект",
      deleteProject: "Удалить проект",
      editConfigure: "Редактировать конфигурацию",
      renameConfigure: "Переименовать конфигурацию",
      deleteConfigure: "Удалить конфигурацию",
      openFolder: "Открыть папку расположения",
      inputProjectName: "Пожалуйста, введите название проекта (не более 10 символов, без специальных символов)",
      inputConfigureName: "Пожалуйста, введите название конфигурации (не более 10 символов, без специальных символов)",
      confirm: "Подтвердить",
      cancel: "Отмена",
      invalidName: "Недопустимая длина или содержимое названия",
      projectAddSuccess: "Проект: {name} добавлен успешно",
      projectAddFailed: "Проект: {name} не удалось добавить, причина:",
      configureAddSuccess: "Конфигурация: {name} добавлена успешно",
      configureAddFailed: "Конфигурация: {name} не удалось добавить, причина:",
      renameSuccess: "Переименование успешно",
      renameFailed: "Переименование не удалось, причина:",
      confirmDelete: "Вы уверены, что хотите удалить?",
      confirmBatchDelete: "В этом проекте есть связанное содержимое конфигурации, вы уверены, что хотите удалить все?",
      deleteSuccess: "Удаление успешно",
      deleteFailed: "Удаление не удалось, причина:",
      remoteSet: "Подтверждение удаления"
    },
    configures: {
      customComponent: "Пользовательский компонент",
      selectDevice: "Пожалуйста, выберите связанное устройство!",
      edit: "Редактировать:"
    },
    customConfigure: {
      getCustomDeviceFailed: "Не удалось получить пользовательское устройство",
      saveDeviceFailed: "Не удалось сохранить символ устройства",
      saveSuccess: "Сохранение успешно",
      deleteDeviceFailed: "Не удалось удалить символ устройства",
      deleteSuccess: "Удаление успешно",
      tip: "Информация подсказки"
    },
    deviceList: {
      unnamed: "Безымянное устройство",
      connect: "Подключить",
      disconnect: "Отключить",
      edit: "Редактировать",
      delete: "Удалить",
      notFound: "Устройство не найдено",
      editWarn: "Пожалуйста, сначала отключите соединение перед редактированием",
      deleteWarn: "Пожалуйста, сначала отключите соединение перед удалением",
      connectSuccess: "Устройство {name}: подключение успешно",
      connectExist: "Устройство {name}: соединение уже существует",
      connectFailed: "Устройство {name}: подключение не удалось",
      connectFailedReason: "Причина неудачи подключения устройства: {reason}",
      disconnectSuccess: "Устройство {name}: отключено",
      disconnectedNotify: "Устройство {name} соединение разорвано",
      currentDisconnectedNotify: "Текущее устройство соединение разорвано",
      operateFailed: "Устройство {name}: операция не удалась",
      remove: "Удалить"
    },
    deviceSearch: {
      searchPlaceholder: "Поиск устройства"
    },
    editConfigure: {
      saveSuccess: "Сохранение успешно",
      saveFailed: "Сохранение не удалось, причина:",
      loadFailed: "Загрузка не удалась, причина:",
      getCustomDeviceFailed: "Не удалось получить пользовательское устройство",
      tip: "Информация подсказки"
    },
    remoteSet: {
      inputValue: "Введите значение",
      write: "Записать",
      cancel: "Отмена",
      setFailed: "Дистанционная настройка не удалась, причина:",
      operateSuccess: "Операция успешна",
      noSetType: "Тип дистанционной настройки не настроен"
    }
  },
  graph: {
    component: {
      electricSymbols: "Электрические символы",
      customComponents: "Пользовательские компоненты",
      basicComponents: "Базовые компоненты"
    },
    toolbar: {
      undo: "Отменить",
      redo: "Повторить",
      bringToFront: "На передний план",
      sendToBack: "На задний план",
      ratio: "Пропорциональное масштабирование",
      delete: "Удалить",
      save: "Сохранить"
    },
    contextMenu: {
      group: "Группировать",
      ungroup: "Разгруппировать",
      linkData: "Связать данные",
      equipmentSaddr: "Адрес устройства"
    },
    dialog: {
      dataConfig: "Конфигурация данных",
      tip: "Информация подсказки",
      selectOneGraph: "Пожалуйста, выберите один график"
    },
    message: {
      waitForCanvasInit: "Пожалуйста, дождитесь завершения инициализации холста",
      loadEquipmentFailed: "Не удалось загрузить символ",
      loadEquipmentError: "Не удалось загрузить устройство",
      equipmentLoaded: "Загрузка устройства завершена"
    },
    basic: {
      title: "Базовые компоненты",
      components: {
        line: "Линия",
        text: "Текст",
        rectangle: "Прямоугольник",
        circle: "Круг",
        ellipse: "Эллипс",
        triangle: "Треугольник",
        arc: "Дуга"
      }
    },
    selectEquipment: {
      sequence: "Номер",
      name: "Название",
      type: "Тип",
      symbol: "Символ",
      operation: "Операция",
      reference: "Ссылка"
    },
    setSAddr: {
      telemetry: "Телесигнализация/Телеизмерения",
      format: "Форматирование",
      factor: "Коэффициент",
      remoteControl: "Дистанционное управление",
      controlType: "Способ дистанционного управления",
      controlValue: "Значение дистанционного управления",
      remoteSet: "Дистанционная настройка",
      setType: "Способ дистанционной настройки",
      displayConfig: "Конфигурация отображения",
      addRow: "Добавить строку",
      sequence: "Номер",
      type: "Тип",
      originalValue: "Исходное значение",
      displayValue: "Отображаемое значение",
      operation: "Операция",
      text: "Текст",
      symbol: "Символ",
      selectSymbol: "Выбрать символ",
      confirm: "Подтвердить",
      cancel: "Отмена",
      confirmDelete: "Вы уверены, что хотите удалить?",
      tip: "Информация подсказки",
      selectControl: "Селективное управление",
      directControl: "Прямое управление",
      controlClose: "Управление замыканием",
      controlOpen: "Управление размыканием",
      cancelDelete: "Отменить удаление"
    },
    equipmentType: {
      CBR: "Выключатель",
      DIS: "Разъединитель",
      GDIS: "Заземляющий разъединитель",
      PTR2: "2-обмоточный трансформатор",
      PTR3: "3-обмоточный трансформатор",
      VTR: "Трансформатор напряжения",
      CTR: "Трансформатор тока",
      EFN: "Устройство заземления нейтрали",
      IFL: "Отходящая линия",
      EnergyConsumer: "Нагрузка",
      GND: "Заземление",
      Arrester: "Разрядник",
      Capacitor_P: "Параллельный конденсатор",
      Capacitor_S: "Последовательный конденсатор",
      Reactor_P: "Параллельный реактор",
      Reactor_S: "Последовательный реактор",
      Ascoil: "Дугогасящая катушка",
      Fuse: "Предохранитель",
      BAT: "Батарея",
      BSH: "Втулка",
      CAB: "Кабель",
      LIN: "Воздушная линия",
      GEN: "Генератор",
      GIL: "Газоизолированная линия",
      RRC: "Вращающийся реактивный элемент",
      TCF: "Тиристорный управляемый преобразователь частоты",
      TCR: "Тиристорный управляемый реактивный элемент",
      LTC: "Переключатель ответвлений",
      IND: "Индуктор"
    },
    equipmentName: {
      breaker_vertical: "Выключатель-вертикальный",
      breaker_horizontal: "Выключатель-горизонтальный",
      breaker_invalid_vertical: "Выключатель-недействительный-вертикальный",
      breaker_invalid_horizontal: "Выключатель-недействительный-горизонтальный",
      disconnector_vertical: "Разъединитель-вертикальный",
      disconnector_horizontal: "Разъединитель-горизонтальный",
      disconnector_invalid_vertical: "Разъединитель-недействительный-вертикальный",
      disconnector_invalid_horizontal: "Разъединитель-недействительный-горизонтальный",
      hv_fuse: "Высоковольтный предохранитель",
      station_transformer_2w: "Станционный трансформатор (двухобмоточный)",
      transformer_y_d_11: "Трансформатор (Y/△-11)",
      transformer_d_y_11: "Трансформатор (△/Y-11)",
      transformer_d_d: "Трансформатор (△/△)",
      transformer_y_y_11: "Трансформатор (Y/Y-11)",
      transformer_y_y_12_d_11: "Трансформатор (Y/Y-12/△-11)",
      transformer_y_d_11_d_11: "Трансформатор (Y/△-11/△-11)",
      transformer_y_y_v: "Трансформатор (Y/Y/V)",
      transformer_autotransformer: "Трансформатор (автотрансформатор)",
      voltage_transformer_2w: "Трансформатор напряжения (двухобмоточный)",
      voltage_transformer_3w: "Трансформатор напряжения (трехобмоточный)",
      voltage_transformer_4w: "Трансформатор напряжения (четырехобмоточный)",
      arrester: "Разрядник",
      capacitor_horizontal: "Конденсатор-горизонтальный",
      capacitor_vertical: "Конденсатор-вертикальный",
      reactor: "Реактор",
      split_reactor: "Расщепленный реактор",
      power_inductor: "Силовой индуктор",
      feeder: "Отходящая линия",
      ground: "Заземление",
      tap_changer: "Переключатель ответвлений",
      connection_point: "Точка подключения",
      transformer_y_y_12_d_11_new: "Трансформатор(Y/Y-12/△-11)(новый)",
      pt: "ТН",
      arrester_new: "Разрядник(новый)",
      disconnector_vertical_new: "Разъединитель-вертикальный(новый)",
      disconnector_horizontal_new: "Разъединитель-горизонтальный(новый)",
      arrester_new_vertical: "Разрядник(новый)-вертикальный",
      disconnector_vertical_left_new: "Разъединитель-вертикальный-левый(новый)"
    }
  },
  graphProperties: {
    blank: {
      propertySetting: "Настройка свойств"
    },
    graph: {
      canvasSetting: "Настройка холста",
      grid: "Сетка",
      backgroundColor: "Цвет фона"
    },
    group: {
      groupProperty: "Свойства группы",
      basic: "Основные",
      width: "Ширина",
      height: "Высота",
      x: "Позиция(X)",
      y: "Позиция(Y)",
      angle: "Угол поворота"
    },
    node: {
      nodeProperty: "Свойства узла",
      style: "Стиль",
      backgroundColor: "Цвет фона",
      borderWidth: "Ширина границы",
      borderColor: "Цвет границы",
      borderDasharray: "Стиль границы",
      rx: "Граница rx",
      ry: "Граница ry",
      position: "Позиция",
      width: "Ширина",
      height: "Высота",
      x: "Позиция(X)",
      y: "Позиция(Y)",
      property: "Свойство",
      angle: "Угол поворота",
      zIndex: "Уровень(z)",
      fontFamily: "Шрифт",
      fontColor: "Цвет шрифта",
      fontSize: "Размер шрифта",
      text: "Текст"
    },
    pathLine: {
      lineSetting: "Настройка линии",
      style: "Стиль",
      lineHeight: "Ширина",
      lineColor: "Цвет",
      borderDasharray: "Граница",
      position: "Позиция",
      width: "Ширина",
      height: "Высота",
      x: "Позиция(X)",
      y: "Позиция(Y)",
      property: "Свойство",
      angle: "Угол поворота",
      zIndex: "Уровень(z)"
    }
  },
  business: {
    hmi: {
      title: "Управление экранами",
      form: {
        add: "Добавить экран",
        edit: "Редактировать экран",
        view: "Просмотр экрана",
        name: "Название экрана",
        type: "Тип экрана",
        template: "Шаблон экрана",
        description: "Описание",
        cancel: "Отмена",
        confirm: "Подтвердить",
        validation: {
          name: "Пожалуйста, введите название экрана",
          type: "Пожалуйста, выберите тип экрана",
          template: "Пожалуйста, выберите шаблон экрана"
        }
      },
      columns: {
        name: "Название экрана",
        type: "Тип экрана",
        template: "Шаблон экрана",
        createTime: "Время создания",
        updateTime: "Время обновления",
        status: "Статус",
        operation: "Операция"
      },
      type: {
        device: "Экран устройства",
        process: "Технологический экран",
        alarm: "Экран аварийных сигналов",
        custom: "Пользовательский экран"
      },
      status: {
        draft: "Черновик",
        published: "Опубликовано",
        archived: "Архивировано"
      },
      editor: {
        title: "Редактор экрана",
        save: "Сохранить",
        preview: "Предварительный просмотр",
        publish: "Опубликовать",
        cancel: "Отмена",
        tools: {
          select: "Выбрать",
          rectangle: "Прямоугольник",
          circle: "Круг",
          line: "Линия",
          text: "Текст",
          image: "Изображение",
          device: "Устройство",
          alarm: "Аварийный сигнал",
          chart: "График"
        },
        properties: {
          title: "Свойства",
          position: "Позиция",
          size: "Размер",
          style: "Стиль",
          data: "Данные",
          event: "Событие"
        }
      },
      preview: {
        title: "Предварительный просмотр экрана",
        fullscreen: "Полный экран",
        exit: "Выход",
        zoom: {
          in: "Увеличить",
          out: "Уменьшить",
          fit: "По размеру"
        }
      },
      publish: {
        title: "Опубликовать экран",
        version: "Номер версии",
        description: "Описание публикации",
        cancel: "Отмена",
        confirm: "Подтвердить",
        validation: {
          version: "Пожалуйста, введите номер версии",
          description: "Пожалуйста, введите описание публикации"
        }
      },
      template: {
        title: "Шаблон экрана",
        add: "Добавить шаблон",
        edit: "Редактировать шаблон",
        delete: "Удалить шаблон",
        name: "Название шаблона",
        category: "Категория шаблона",
        description: "Описание",
        preview: "Предварительный просмотр",
        cancel: "Отмена",
        confirm: "Подтвердить",
        validation: {
          name: "Пожалуйста, введите название шаблона",
          category: "Пожалуйста, выберите категорию шаблона"
        }
      }
    }
  }
};
