<template>
  <div class="message-monitor">
    <bl-row just="space-between" height="28px" class="message-monitor-workbench">
      <div style="display: flex; align-items: center">
        <svg-icon icon="ant-design:monitor-outlined" style="margin-right: 8px; font-size: 16px" />
        <span>{{ t("device.messageMonitor.title") }}</span>
        <el-badge v-if="messageCount > 0" :value="messageCount" :max="999" class="message-count-badge" />
        <span v-if="isMonitoring" class="monitoring-status">
          <svg-icon icon="ant-design:dot-chart-outlined" class="pulse-icon" />
          {{ t("device.messageMonitor.monitoring") }}
        </span>
      </div>
      <div style="display: flex; align-items: center">
        <el-tooltip :content="isMonitoring ? t('device.messageMonitor.stop') : t('device.messageMonitor.start')" placement="bottom">
          <i class="iconfont toolBar-icon" @click="toggleMonitoring">
            <svg-icon
              :icon="isMonitoring ? 'ant-design:pause-circle-outlined' : 'ant-design:play-circle-outlined'"
              :style="{ fontSize: '20px', color: isMonitoring ? 'var(--el-color-danger)' : 'var(--el-color-success)' }"
            />
          </i>
        </el-tooltip>
        <el-tooltip :content="t('device.messageMonitor.clear')" placement="bottom">
          <i class="iconfont toolBar-icon" @click="clearMessages">
            <svg-icon icon="ant-design:delete-outlined" style="font-size: 20px; color: var(--el-color-info)" />
          </i>
        </el-tooltip>
        <el-tooltip :content="t('device.messageMonitor.export')" placement="bottom">
          <i class="iconfont toolBar-icon" @click="exportMessages">
            <svg-icon icon="ant-design:download-outlined" style="font-size: 20px; color: var(--el-color-info)" />
          </i>
        </el-tooltip>
        <el-tooltip :content="autoScroll ? t('device.messageMonitor.pauseScroll') : t('device.messageMonitor.resumeScroll')" placement="bottom">
          <i class="iconfont toolBar-icon" @click="toggleAutoScroll">
            <svg-icon
              :icon="autoScroll ? 'ant-design:pause-outlined' : 'ant-design:play-outlined'"
              :style="{ fontSize: '20px', color: autoScroll ? 'var(--el-color-warning)' : 'var(--el-color-success)' }"
            />
          </i>
        </el-tooltip>
        <el-tooltip :content="isCollapsed ? t('device.messageMonitor.expand') : t('device.messageMonitor.collapse')" placement="bottom">
          <i class="iconfont toolBar-icon" @click="toggleCollapse">
            <svg-icon :icon="isCollapsed ? 'eva:plus-outline' : 'eva:minus-outline'" style="font-size: 20px; color: var(--el-color-info)" />
          </i>
        </el-tooltip>
        <el-tooltip :content="t('device.messageMonitor.close')" placement="bottom">
          <i class="iconfont toolBar-icon" @click="closeMonitor">
            <svg-icon icon="ant-design:close-outlined" style="font-size: 20px; color: var(--el-color-info)" />
          </i>
        </el-tooltip>
      </div>
    </bl-row>

    <div v-if="!isCollapsed" class="message-monitor-content">
      <div class="message-list" ref="messageListRef">
        <div
          v-for="(message, index) in displayMessages"
          :key="index"
          class="message-item"
          :class="{
            'message-item-highlight': message.isNew,
            'message-send': message.type === 'send',
            'message-receive': message.type === 'receive'
          }"
        >
          <div class="message-header">
            <div class="message-timestamp-row">
              <svg-icon icon="ant-design:clock-circle-outlined" class="timestamp-icon" />
              {{ formatTimestamp(message.timestamp) }}
            </div>
            <div class="message-info-row">
              <div class="message-device">
                <svg-icon icon="ant-design:global-outlined" class="device-icon" />
                {{ message.direction || message.deviceIp || message.deviceId }}
              </div>
              <div class="message-tags">
                <el-tag size="small" :type="getMessageTypeColor(message.type)" effect="light" class="message-type-tag">
                  <svg-icon :icon="getMessageTypeIcon(message.type)" class="tag-icon" />
                  {{ getMessageTypeText(message.type) }}
                </el-tag>
                <el-tag size="small" type="info" effect="light">#{{ index + 1 }}</el-tag>
              </div>
            </div>
          </div>
          <div class="message-content">
            <div class="content-wrapper">
              <pre class="message-data">{{ formatMessageContent(message.data) }}</pre>
              <div class="message-actions">
                <el-button size="small" text @click="copyMessage(message)">
                  <svg-icon icon="ant-design:copy-outlined" />
                  {{ t("device.messageMonitor.copy") }}
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <div v-if="displayMessages.length === 0" class="no-messages">
          <el-empty :description="t('device.messageMonitor.noMessages')" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from "vue";
import { useI18n } from "vue-i18n";
import { ipc } from "@/api/request/ipcRenderer";
import { IECNotify } from "@/api";
import { deviceConnectApi } from "@/api/modules/biz/debug/deviceinfomenu";
import Message from "@/scripts/message";

const { t } = useI18n();

// 组件属性
const props = defineProps<{
  deviceId: string;
}>();

// 组件事件
const emit = defineEmits<{
  close: [];
}>();

// 组件状态
const isMonitoring = ref(false);
const isCollapsed = ref(false);
const autoScroll = ref(true); // 自动滚动状态
const messages = ref<
  Array<{
    timestamp: string;
    deviceId: string;
    deviceIp?: string;
    localIP?: string;
    direction?: string;
    data: any;
    type?: string;
    isNew: boolean;
  }>
>([]);

const messageListRef = ref<HTMLElement>();

// 计算属性
const messageCount = computed(() => messages.value.length);
const displayMessages = computed(() => {
  // 最多显示1000条消息，避免内存溢出
  return messages.value.slice(-1000);
});

// 格式化时间戳
const formatTimestamp = (timestamp: string) => {
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    fractionalSecondDigits: 3
  });
};

// 格式化消息内容
const formatMessageContent = (data: any) => {
  try {
    return JSON.stringify(data, null, 2);
  } catch (error) {
    return String(data);
  }
};

// 获取消息类型颜色
const getMessageTypeColor = (type?: string) => {
  switch (type) {
    case "send":
      return "danger"; // 发送消息用红色
    case "receive":
      return "success"; // 接收消息用绿色
    default:
      return "primary"; // 默认用蓝色
  }
};

// 获取消息类型图标
const getMessageTypeIcon = (type?: string) => {
  switch (type) {
    case "send":
      return "ant-design:arrow-up-outlined";
    case "receive":
      return "ant-design:arrow-down-outlined";
    default:
      return "ant-design:message-outlined";
  }
};

// 获取消息类型文本
const getMessageTypeText = (type?: string) => {
  switch (type) {
    case "send":
      return t("device.messageMonitor.send");
    case "receive":
      return t("device.messageMonitor.receive");
    default:
      return t("device.messageMonitor.message");
  }
};

// 切换监听状态
const toggleMonitoring = async () => {
  try {
    if (isMonitoring.value) {
      // 停止监听
      await deviceConnectApi.stopMessageMonitor(props.deviceId);
      isMonitoring.value = false;
      Message.info(t("device.messageMonitor.stopSuccess"));
    } else {
      // 开始监听
      await deviceConnectApi.startMessageMonitor(props.deviceId);
      isMonitoring.value = true;
      Message.success(t("device.messageMonitor.startSuccess"));
    }
  } catch (error) {
    console.error("Toggle message monitoring failed:", error);
    Message.error(t("device.messageMonitor.toggleFailed"));
  }
};

// 清空消息
const clearMessages = () => {
  messages.value = [];
  Message.success(t("device.messageMonitor.clearSuccess"));
};

// 导出消息
const exportMessages = () => {
  if (messages.value.length === 0) {
    Message.warning(t("device.messageMonitor.noMessagesToExport"));
    return;
  }

  try {
    const content = messages.value.map(msg => ({
      timestamp: msg.timestamp,
      deviceId: msg.deviceId,
      data: msg.data
    }));

    const blob = new Blob([JSON.stringify(content, null, 2)], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `message-monitor-${new Date().toISOString().slice(0, 19).replace(/:/g, "-")}.json`;

    // 添加下载完成的监听
    a.addEventListener("click", () => {
      // 延迟显示成功消息，给浏览器时间处理下载
      setTimeout(() => {
        Message.success(t("device.messageMonitor.exportSuccess"));
      }, 100);
    });

    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Export messages failed:", error);
    Message.error(t("device.messageMonitor.exportFailed"));
  }
};

// 复制单条消息
const copyMessage = async (message: any) => {
  try {
    const content = JSON.stringify(
      {
        timestamp: message.timestamp,
        deviceId: message.deviceId,
        data: message.data
      },
      null,
      2
    );
    await navigator.clipboard.writeText(content);
    Message.success(t("device.messageMonitor.copySuccess"));
  } catch (error) {
    Message.error(t("device.messageMonitor.copyFailed"));
  }
};

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 切换自动滚动
const toggleAutoScroll = () => {
  autoScroll.value = !autoScroll.value;
  if (autoScroll.value) {
    Message.success(t("device.messageMonitor.autoScrollEnabled"));
    // 立即滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
  } else {
    Message.info(t("device.messageMonitor.autoScrollDisabled"));
  }
};

// 关闭监视器
const closeMonitor = async () => {
  try {
    if (isMonitoring.value) {
      await deviceConnectApi.stopMessageMonitor(props.deviceId);
    }
    emit("close");
  } catch (error) {
    console.error("Close message monitor failed:", error);
    emit("close"); // 即使出错也关闭界面
  }
};

// 滚动到底部的方法
const scrollToBottom = () => {
  if (messageListRef.value) {
    const messageItems = messageListRef.value.querySelectorAll(".message-item");
    if (messageItems.length > 0) {
      const lastMessage = messageItems[messageItems.length - 1];
      lastMessage.scrollIntoView({ behavior: "smooth", block: "end" });
    }
  }
};

// 添加新消息
const addMessage = (notify: IECNotify) => {
  if (!isMonitoring.value) return;

  const messageData = notify.data as any;
  const originalMessage = messageData.message || messageData;

  // 提取消息类型和设备IP
  let messageType = "unknown";
  let deviceIp = notify.deviceId || "Unknown";

  // 从消息数据中提取type和IP信息
  if (originalMessage && typeof originalMessage === "object") {
    messageType = originalMessage.type || "unknown";
    // 尝试从不同字段获取IP地址
    deviceIp = originalMessage.ip || originalMessage.address || originalMessage.host || notify.deviceId || "Unknown";
  }

  const newMessage = {
    timestamp: messageData.timestamp || new Date().toISOString(),
    deviceId: notify.deviceId || "Unknown",
    deviceIp: deviceIp,
    localIP: messageData.localIP || "Unknown",
    direction: messageData.direction || `${messageData.localIP || "Unknown"} ↔ ${messageData.deviceIP || deviceIp}`,
    data: originalMessage,
    type: messageType,
    isNew: true
  };

  messages.value.push(newMessage);

  // 移除高亮效果
  setTimeout(() => {
    newMessage.isNew = false;
  }, 2000);

  // 只有在自动滚动开启时才滚动到底部
  if (autoScroll.value) {
    nextTick(() => {
      scrollToBottom();
    });
  }
};

// 监听报文消息
const handleNotify = (_event: unknown, notify: IECNotify) => {
  if (notify.type === "upadRpcMessage" && notify.deviceId === props.deviceId) {
    addMessage(notify);
  }
};

onMounted(() => {
  console.log("MessageMonitor component mounted, device ID:", props.deviceId);
  ipc.on("notify", handleNotify);
});

onUnmounted(() => {
  console.log("MessageMonitor component unmounted");
  ipc.removeListener("notify", handleNotify);
});
</script>

<style scoped lang="scss">
.message-monitor {
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  background-color: var(--el-bg-color);
  overflow: hidden; // 防止组件整体滚动

  .message-monitor-workbench {
    color: var(--el-text-color);
    background-color: var(--el-color-primary-light-9);
    border-bottom: 1px solid var(--el-border-color);
    padding: 0 10px;

    .message-count-badge {
      margin-left: 8px;
    }

    .monitoring-status {
      margin-left: 12px;
      display: flex;
      align-items: center;
      gap: 4px;
      color: var(--el-color-success);
      font-size: 12px;

      .pulse-icon {
        animation: pulse 1.5s infinite;
      }
    }

    .toolBar-icon {
      margin-left: 8px;
      cursor: pointer;
      padding: 2px;
      border-radius: 2px;

      &:hover {
        background-color: var(--el-color-primary-light-8);
      }
    }
  }

  .message-monitor-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; // 关键：允许flex子项收缩

    .message-list {
      flex: 1;
      overflow-y: scroll !important; // 使用scroll而不是auto，强制显示滚动条
      overflow-x: hidden;
      padding: 8px;
      min-height: 200px; // 设置最小高度，确保有滚动空间
      max-height: 100%; // 限制最大高度

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 12px !important; // 增加滚动条宽度，确保可见
        background: var(--el-fill-color-lighter);
      }

      &::-webkit-scrollbar-track {
        background: var(--el-fill-color-lighter);
        border-radius: 6px;
        margin: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--el-border-color-darker);
        border-radius: 6px;
        border: 2px solid var(--el-fill-color-lighter);

        &:hover {
          background: var(--el-color-primary-light-3);
        }

        &:active {
          background: var(--el-color-primary);
        }
      }

      // 确保在Firefox中也显示滚动条
      scrollbar-width: thin;
      scrollbar-color: var(--el-border-color-darker) var(--el-fill-color-lighter);

      .message-item {
        margin-bottom: 12px;
        padding: 12px;
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 8px;
        background-color: var(--el-bg-color-page);
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &:hover {
          border-color: var(--el-color-primary-light-5);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        &.message-item-highlight {
          border-color: var(--el-color-primary);
          background-color: var(--el-color-primary-light-9);
          box-shadow: 0 2px 12px rgba(64, 158, 255, 0.3);
          animation: highlight-pulse 2s ease-in-out;
        }

        // 发送消息样式
        &.message-send {
          border-left: 4px solid var(--el-color-danger);
          background-color: var(--el-color-danger-light-9);

          &:hover {
            border-color: var(--el-color-danger-light-3);
            box-shadow: 0 2px 8px rgba(245, 108, 108, 0.15);
          }

          &.message-item-highlight {
            border-color: var(--el-color-danger);
            background-color: var(--el-color-danger-light-8);
            box-shadow: 0 2px 12px rgba(245, 108, 108, 0.3);
          }
        }

        // 接收消息样式
        &.message-receive {
          border-left: 4px solid var(--el-color-success);
          background-color: var(--el-color-success-light-9);

          &:hover {
            border-color: var(--el-color-success-light-3);
            box-shadow: 0 2px 8px rgba(103, 194, 58, 0.15);
          }

          &.message-item-highlight {
            border-color: var(--el-color-success);
            background-color: var(--el-color-success-light-8);
            box-shadow: 0 2px 12px rgba(103, 194, 58, 0.3);
          }
        }

        .message-header {
          display: flex;
          flex-direction: column;
          gap: 6px;
          margin-bottom: 8px;

          .message-timestamp-row {
            display: flex;
            align-items: center;
            gap: 4px;
            color: var(--el-color-info);
            font-family: "JetBrains Mono", "Courier New", monospace;
            font-size: 11px;
            font-weight: 500;

            .timestamp-icon {
              font-size: 12px;
            }
          }

          .message-info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .message-device {
              display: flex;
              align-items: center;
              gap: 4px;
              color: var(--el-color-primary);
              font-weight: 500;
              font-size: 12px;

              .device-icon {
                font-size: 12px;
              }
            }

            .message-tags {
              display: flex;
              align-items: center;
              gap: 6px;

              .message-type-tag {
                font-weight: 500;
              }

              .el-tag {
                display: flex;
                align-items: center;
                gap: 2px;

                .tag-icon {
                  font-size: 10px;
                }
              }
            }
          }
        }

        .message-content {
          .content-wrapper {
            position: relative;

            .message-data {
              margin: 0;
              padding: 12px;
              font-size: 11px;
              font-family: "JetBrains Mono", "Courier New", monospace;
              color: var(--el-text-color-regular);
              background-color: var(--el-fill-color-extra-light);
              border: 1px solid var(--el-border-color-extra-light);
              border-radius: 6px;
              white-space: pre-wrap;
              word-break: break-all;
              max-height: 300px;
              overflow-y: auto;
              line-height: 1.4;

              &::-webkit-scrollbar {
                width: 4px;
              }

              &::-webkit-scrollbar-track {
                background: transparent;
              }

              &::-webkit-scrollbar-thumb {
                background: var(--el-border-color);
                border-radius: 2px;
              }
            }

            .message-actions {
              position: absolute;
              top: 8px;
              right: 8px;
              opacity: 0;
              transition: opacity 0.2s ease;

              .el-button {
                padding: 4px 8px;
                font-size: 10px;
                background-color: var(--el-bg-color);
                border: 1px solid var(--el-border-color);

                &:hover {
                  background-color: var(--el-color-primary-light-9);
                  border-color: var(--el-color-primary);
                  color: var(--el-color-primary);
                }
              }
            }

            &:hover .message-actions {
              opacity: 1;
            }
          }
        }
      }

      .no-messages {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
      }
    }
  }
}

// 高亮动画
@keyframes highlight-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.3);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 4px 20px rgba(64, 158, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.3);
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
