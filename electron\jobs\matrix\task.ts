import { logger } from "ee-core/log";
import { isChildJob, exit } from "ee-core/ps";
import { childMessage } from "ee-core/message";
import GlobalDeviceData from "../../data/debug/globalDeviceData";
import { FileWriteError, FileWriteInfo, FileWriteRequestData, FileWriteStatus } from "iec-upadrpc/dist/src/data";
import { formatByStep, isValid, removeLastCarriage } from "../../utils/common";
import IECCONSTANTS from "../../data/debug/iecConstants";
import { IpUtils } from "../../utils/ipUtils";
import { matrixConnectService } from "../../service/matrix/matrixconnect";
import { DeviceProgress, DeviceResult, MatrixTaskItem } from "../../interface/matrix/matrix";
import { SingleGlobalDeviceInfo } from "../../data/debug/singleGlobalDeviceInfo";
import { t } from "../../data/i18n/i18n";

// 进度计算参数
const STEP_WEIGHTS = {
  connect: 10,
  download: 60,
  import: 30,
};

/**
 * example - TimerJob
 * @class
 */
class TaskJob {
  params: any;
  private maxConcurrentTasks: number = 3; // 最大同时执行任务数

  constructor(params: any) {
    this.params = params;
    // 可以通过参数配置最大并发数
    if (params.maxConcurrentTasks && params.maxConcurrentTasks > 0) {
      this.maxConcurrentTasks = params.maxConcurrentTasks;
    }
  }

  /**
   * handle() method is necessary and will be automatically called
   */
  async handle(): Promise<void> {
    logger.info(
      `[TaskJob-process] ${t("matrixJob.logs.taskJobParams")}: `,
      this.params
    );
    const { jobId, deviceList } = this.params;
    // Execute the task
    const devices = JSON.parse(deviceList);
    if (Array.isArray(devices)) {
      // 使用并发控制执行任务
      await this.executeTasksWithConcurrencyLimit(jobId, devices);
    }

    if (isChildJob()) {
      exit();
    }
  }

  /**
   * 并发控制执行任务
   */
  private async executeTasksWithConcurrencyLimit(
    jobId: string,
    devices: MatrixTaskItem[]
  ): Promise<void> {
    logger.info(
      `[TaskJob] 开始执行任务，设备数量: ${devices.length}, 最大并发数: ${this.maxConcurrentTasks}`
    );

    const executing: Promise<void>[] = [];

    for (const device of devices) {
      // 如果当前执行的任务数达到最大并发数，等待其中一个完成
      if (executing.length >= this.maxConcurrentTasks) {
        await Promise.race(executing);
      }

      // 创建新任务
      const taskPromise = this.doTask(jobId, device).finally(() => {
        // 任务完成后从执行列表中移除
        const index = executing.indexOf(taskPromise);
        if (index > -1) {
          executing.splice(index, 1);
        }
      });

      executing.push(taskPromise);
    }

    // 等待所有剩余任务完成
    await Promise.all(executing);
    logger.info(`[TaskJob] 所有任务执行完成`);
  }

  /**
   * Pause the job
   */
  async pause(jobId: string): Promise<void> {
    logger.info(
      `[TaskJob-process] ${t("matrixJob.logs.pauseTaskJob")}: `,
      jobId
    );
  }

  /**
   * Resume the job
   */
  async resume(jobId: string, pid: number): Promise<void> {
    logger.info(
      `[TaskJob-process] ${t("matrixJob.logs.resumeTaskJob")}: `,
      jobId,
      ", pid: ",
      pid
    );
    // this.doTask(jobId);
  }

  /**
   * Run the task
   */
  async doTask(jobId, data: MatrixTaskItem) {
    logger.info(
      `===============${t("matrixJob.logs.doTaskStart")}:`,
      data.device.ip
    );
    let totalProgress = 0;

    totalProgress += STEP_WEIGHTS.connect;
    // 步骤1: 连接设备 (5%)
    await this.updateProgress(jobId, {
      deviceId: data.device.id,
      currentStep: "connect",
      totalProgress: totalProgress,
      status: "success",
      message: t("matrixJob.messages.connectDevice"),
    });

    // 在连接前清理可能存在的旧连接状态
    await this.cleanupDeviceConnection(data.device.id);

    // 使用重试机制连接设备
    const connection = await this.connectDeviceWithRetry(data.device, 3);
    const device = GlobalDeviceData.getInstance().getDeviceInfoGlobal(
      data.device.id
    );
    const client = device?.deviceClient;
    logger.info("connection", connection);
    if (connection.isSuccess()) {
      try {
        logger.info("=================", connection);
        if (data.device.downFile && data.downlist.length > 0) {
          await this.updateProgress(jobId, {
            deviceId: data.device.id,
            currentStep: "download",
            totalProgress: totalProgress,
            status: "success",
            message: t("matrixJob.messages.executeFileDownload"),
          });
          logger.info("=================downFile before");
          // 步骤2: 下载文件 (80%)
          const param: FileWriteRequestData = {
            fileItems: data.downlist,
            remoteParentPath: "/shr",
            verifyType: "None",
            cb: async (result: FileWriteInfo) => {
              // const data = { fileItem: result.currentfileItem, errorMsg: msg, status: result.status, progress: result.progress, taskid: uuid };
              totalProgress =
                STEP_WEIGHTS.connect +
                (STEP_WEIGHTS.download * result.progress.totalPercentage) / 100;
              await this.updateProgress(jobId, {
                deviceId: data.device.id,
                currentStep: "download",
                totalProgress: Number(totalProgress.toFixed(2)),
                status: "success",
                message:
                  t("matrixJob.messages.downloadingFile") +
                  "：" +
                  result?.currentfileItem?.filePath,
              });
            },
          };
          const result = await client?.writeFile(param);
          logger.info("=================downFile", result);
          logger.info("result", result);
          if (result?.status != FileWriteStatus.ALL_FILE_FINISH) {
            let msg = await this.getDownloadErrMsg(result, device);
            await this.updateResult(jobId, {
              deviceId: data.device.id,
              totalProgress: Number(totalProgress.toFixed(2)),
              status: "error",
              message:
                t("matrixJob.messages.downloadFileFailed") +
                "：" +
                String(msg) +
                "，" +
                result?.currentfileItem?.filePath,
            });
            return;
          } else {
            await this.updateResult(jobId, {
              deviceId: data.device.id,
              totalProgress: Number(totalProgress.toFixed(2)),
              status: "success",
              message: t("matrixJob.messages.fileDownloadCompleted"),
            });
          }
        } else {
          totalProgress += STEP_WEIGHTS.download;
          await this.updateResult(jobId, {
            deviceId: data.device.id,
            totalProgress: Number(totalProgress.toFixed(2)),
            status: "success",
            message: t("matrixJob.messages.fileDownloadCompleted"),
          });
        }
        logger.info("=================importParam before");
        // 步骤3: 导入定值 (5%)
        if (data.device.importParam && data.paramList.length > 0) {
          await this.updateProgress(jobId, {
            deviceId: data.device.id,
            currentStep: "import",
            totalProgress: totalProgress,
            status: "success",
            message: t("matrixJob.messages.executeParamImport"),
          });
          const objMap = device?.debugItemObjMap;

          let checkMsg = "";
          const reqestData = data.paramList;
          for (let element of reqestData) {
            logger.info(element.name, element.v);
            const item = objMap?.get(element.name);
            if (item) {
              if (
                !isValid(
                  String(element.v),
                  item?.type || "",
                  await this.getRealValue(
                    item?.type || "",
                    item?.s_min,
                    item?.step
                  ),
                  await this.getRealValue(
                    item?.type || "",
                    item?.s_max,
                    item?.step
                  ),
                  Number(item?.step)
                )
              ) {
                checkMsg +=
                  element.name +
                  ", " +
                  t("matrixJob.errors.description") +
                  item?.desc +
                  t("matrixJob.errors.errorReason") +
                  (await this.getRealValue(
                    String(item?.type),
                    element.v,
                    item?.step
                  )) +
                  t("matrixJob.errors.invalidValue") +
                  "\n";
              }
              element.v = String(
                await this.parseAddrToNum(item?.type || "", String(element.v))
              );
              element.grp = item?.grp || "";
              element.inf = item?.inf || "";
            } else {
              checkMsg +=
                element.name +
                ", " +
                t("matrixJob.errors.paramNotFound") +
                "\n";
            }

            // logger.info("element:", element);
          }
          // logger.info(reqestData);
          if (checkMsg.length > 0) {
            logger.info(checkMsg);
            await this.updateResult(jobId, {
              deviceId: data.device.id,
              totalProgress: Number(totalProgress.toFixed(2)),
              status: "error",
              message:
                t("matrixJob.messages.paramValidationFailed") +
                "，" +
                removeLastCarriage(checkMsg),
            });
            return;
          }
          await client?.setRequestTimeout(data.device?.paramTimeout || 30000);
          logger.info("setEditSgValue:", data.paramList);
          const response = await client?.setEditSgValue(
            reqestData,
            true,
            IECCONSTANTS.BATCH_COUNT
          );
          // logger.info("response:", response);
          logger.info("=================importParam response", response);
          if (response?.isSuccess()) {
            let msg = "";
            if (response.data.error == "itemError") {
              msg += t("matrixJob.errors.paramItemModifyError");
              const errdata = response.data.data;
              if (errdata && Array.isArray(errdata)) {
                errdata.forEach((element, index) => {
                  msg +=
                    data.paramList[index].name +
                    t("matrixJob.errors.errorReason") +
                    device?.getServiceErrMsgByCode(String(element)) +
                    "；\n";
                });
              }
            } else if (response.data.error == "confirmError") {
              msg += t("matrixJob.errors.paramConfirmError");
              msg +=
                device?.getServiceErrMsgByCode(String(response.data.data)) +
                "\n";
            }
            if (msg.length > 0) {
              totalProgress += STEP_WEIGHTS.import;
              await this.updateResult(jobId, {
                deviceId: data.device.id,
                totalProgress: Number(totalProgress.toFixed(2)),
                status: "error",
                message:
                  t("matrixJob.messages.paramImportFailed") +
                  "：" +
                  String(msg),
              });
              return;
            }
          }
        } else {
          totalProgress += STEP_WEIGHTS.import;
          await this.updateResult(jobId, {
            deviceId: data.device.id,
            totalProgress: Number(totalProgress.toFixed(2)),
            status: "success",
            message: t("matrixJob.messages.paramImportCompleted"),
          });
        }

        if (data.isReboot) {
          const response = await client?.deviceReboot();
          if (response?.isSuccess()) {
            logger.info(
              data.device.ip + t("matrixJob.messages.deviceRebootSuccess")
            );
          }
        }
        totalProgress = 100;
        await this.updateResult(jobId, {
          deviceId: data.device.id,
          totalProgress: Number(totalProgress.toFixed(2)),
          status: "success",
          message: t("matrixJob.messages.taskCompleted"),
        });

        logger.info(`=================${t("matrixJob.logs.allFinished")}`);
      } finally {
        if (client) {
          // 断开连接
          matrixConnectService.disconnectDevice(data.device.id);
        }
      }
      // postMessage({ type: "result", success: true });
    } else {
      await this.updateResult(jobId, {
        deviceId: data.device.id,
        totalProgress: totalProgress,
        status: "error",
        message:
          t("matrixJob.messages.deviceConnectionFailed") +
          "，" +
          connection.msg,
      });
      // postMessage({ type: "result", success: false });
    }
  }

  /**
   * 清理设备连接状态
   * 解决设备重启后连接状态不正确的问题
   */
  private async cleanupDeviceConnection(deviceId: string): Promise<void> {
    try {
      const globalDeviceData = GlobalDeviceData.getInstance();
      const deviceInfo = globalDeviceData.getDeviceInfoGlobal(deviceId);

      if (deviceInfo && deviceInfo.deviceClient) {
        logger.info(`[TaskJob] 清理设备 ${deviceId} 的旧连接状态`);

        try {
          // 尝试断开旧连接
          if (deviceInfo.deviceClient.isConnected()) {
            await deviceInfo.deviceClient.disconnect();
          }
        } catch (error) {
          logger.warn(`[TaskJob] 断开设备 ${deviceId} 旧连接时出错:`, error);
        }

        // 清理设备信息
        deviceInfo.deviceClient = undefined as any;
        deviceInfo.resetData();
      }
    } catch (error) {
      logger.warn(`[TaskJob] 清理设备 ${deviceId} 连接状态时出错:`, error);
    }
  }

  /**
   * 带重试机制的设备连接
   * 解决设备重启后连接失败的问题
   */
  private async connectDeviceWithRetry(
    device: any,
    maxRetries: number = 3
  ): Promise<any> {
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        logger.info(
          `[TaskJob] 尝试连接设备 ${device.ip}，第 ${attempt}/${maxRetries} 次`
        );

        const connection = await matrixConnectService.connectDeviceByRpc(
          JSON.stringify(device)
        );

        if (connection.isSuccess()) {
          logger.info(`[TaskJob] 设备 ${device.ip} 连接成功`);
          return connection;
        } else {
          lastError = connection;
          logger.warn(
            `[TaskJob] 设备 ${device.ip} 连接失败，第 ${attempt} 次尝试:`,
            connection.msg
          );
        }
      } catch (error) {
        lastError = error;
        logger.warn(
          `[TaskJob] 设备 ${device.ip} 连接异常，第 ${attempt} 次尝试:`,
          error
        );
      }

      // 如果不是最后一次尝试，等待一段时间后重试
      if (attempt < maxRetries) {
        const waitTime = attempt * 1000; // 递增等待时间：1s, 2s, 3s...
        logger.info(`[TaskJob] 等待 ${waitTime}ms 后重试连接设备 ${device.ip}`);
        await new Promise((resolve) => setTimeout(resolve, waitTime));

        // 在重试前再次清理连接状态
        await this.cleanupDeviceConnection(device.id);
      }
    }

    logger.error(
      `[TaskJob] 设备 ${device.ip} 连接失败，已尝试 ${maxRetries} 次`
    );
    return lastError;
  }

  // 数字转换为IP
  async getRealValue(type: string, value: any, step: any) {
    const result = type == "net" ? IpUtils.numberToIp(value) : value;
    return formatByStep(result, step);
  }

  // IP地址转换为数字
  async parseAddrToNum(type: string, value: string) {
    return type == "net" ? IpUtils.ipToNumber(value) : value;
  }

  async getDownloadErrMsg(
    result: FileWriteInfo | undefined,
    device: SingleGlobalDeviceInfo | undefined
  ) {
    let msg = "";
    if (result?.status == FileWriteStatus.ERROR) {
      if (result.errorCode === FileWriteError.SERVICE_ERROR) {
        const errdata = result?.errorData;
        logger.info(result);
        if (Number.isInteger(errdata)) {
          msg = device?.getServiceErrMsgByCode(String(errdata)) || "";
        }
      } else {
        const errdata = result?.errorData;
        if (errdata instanceof Error) {
          msg = errdata.message;
        }
      }
    }
    return msg;
  }
  // 更新进度工具函数
  async updateProgress(jobId, message: DeviceProgress) {
    const eventName = "job-progress-" + jobId;
    childMessage.send(eventName, {
      type: "progress",
      data: message,
    });
  }

  async updateResult(jobId, message: DeviceResult) {
    const eventName = "job-result-" + jobId;
    childMessage.send(eventName, {
      type: "result",
      data: message,
    });
  }
}
TaskJob.toString = () => "[class TaskJob]";

export default TaskJob;
