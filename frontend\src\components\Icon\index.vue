<template>
  <Icon :icon="name" />
</template>
<script lang="ts">
import { defineComponent } from "vue";
import { Icon, addCollection } from "@iconify/vue";

export default defineComponent({
  name: "Icons",
  components: { Icon },
  props: {
    name: String
  },
  async mounted() {
    try {
      // 动态导入图标集合，避免构建失败
      const ant = await import("@iconify/json/json/ant-design.json");
      addCollection(ant.default);
    } catch (error) {
      console.warn("加载 ant-design 图标集合失败:", error);
    }
  }
});
</script>

<style scoped></style>
