export default {
  // Общие операции
  common: {
    add: "Добавить",
    index: "Номер",
    delete: "Удалить",
    clear: "Очистить",
    import: "Импорт",
    export: "Экспорт",
    execute: "Выполнить",
    moveUp: "Переместить вверх",
    moveDown: "Переместить вниз",
    loading: "Загрузка...",
    success: "Успешно",
    failed: "Не удалось",
    confirm: "Подтвердить",
    cancel: "Отмена",
    yes: "Да",
    no: "Нет",
    operation: "Операция",
    tips: "Подсказки",
    title: "Подсказка"
  },

  // Поиск
  search: {
    placeholder: "Поиск функций"
  },

  // Список функций
  functionList: {
    unnamedDevice: "Безымянное устройство",
    batchDownload: {
      name: "Пакетное скачивание",
      desc: "Пакетное скачивание файлов и импорт параметров"
    },
    xmlFormatter: {
      name: "Форматирование XML",
      desc: "Быстрая иерархическая организация XML-данных"
    },
    jsonFormatter: {
      name: "Форматирование JSON",
      desc: "Интеллектуальное форматирование JSON-данных с поддержкой проверки синтаксиса"
    },
    radixConverter: {
      name: "Преобразование систем счисления",
      desc: "Поддержка взаимного преобразования двоичных, десятичных, шестнадцатеричных и других систем счисления"
    },
    temperatureConverter: {
      name: "Преобразование температуры",
      desc: "Интеллектуальное преобразование различных единиц температуры: Цельсий, Фаренгейт, Кельвин и др."
    },
    encryption: {
      name: "Шифрование/дешифрование текста",
      desc: "Быстрое шифрование и дешифрование текста на основе алгоритмов AES, RSA, Base64 и др."
    },
    packageProgram: {
      name: "Упаковка программы",
      desc: "Упаковка и экспорт файлов программы устройства с поддержкой пользовательского каталога сохранения и быстрого поиска папки"
    }
  },

  // Содержимое матрицы
  matrixContent: {
    loading: "Загрузка...",
    tabs: {
      deviceList: "Список устройств",
      downloadConfig: "Config скачивания",
      paramConfig: "Config параметров"
    }
  },

  // Шаги задачи
  taskSteps: {
    connect: "Подключение",
    download: "Скачивание",
    import: "Импорт",
    disconnect: "Отключение",
    complete: "Завершено"
  },

  // Сообщения задач
  messages: {
    connectDevice: "Подключение устройства",
    executeFileDownload: "Выполнение скачивания файла",
    downloadingFile: "Скачивание файла",
    downloadFileFailed: "Скачивание файла не удалось",
    fileDownloadCompleted: "Скачивание файла завершено",
    executeParamImport: "Выполнение импорта параметров",
    paramValidationFailed: "Проверка параметров не удалась",
    paramImportFailed: "Импорт параметров не удался",
    paramImportCompleted: "Импорт параметров завершен",
    taskCompleted: "Задача завершена",
    deviceConnectionFailed: "Подключение устройства не удалось",
    deviceRebootSuccess: "Перезагрузка устройства успешна"
  },

  // Список устройств
  deviceList: {
    title: "Список устройств",
    deviceListExcel: "Список устройств.xlsx",
    exportDeviceList: "Экспорт списка устройств",
    importDeviceList: "Импорт списка устройств",
    exportSuccess: "Экспорт списка устройств успешен",
    exportFail: "Экспорт списка устройств не удался",
    importSuccess: "Импорт списка устройств успешен",
    importFail: "Импорт списка устройств не удался",
    exportSuccessMsg: "Экспорт списка устройств успешен",
    exportFailMsg: "Экспорт списка устройств не удался",
    importSuccessMsg: "Импорт списка устройств успешен",
    importFailMsg: "Импорт списка устройств не удался: {msg}",
    deviceName: "Название устройства",
    deviceAddress: "Адрес устройства",
    devicePort: "Порт устройства",
    isEncrypted: "Зашифровано ли",
    encrypted: "Зашифровано",
    notEncrypted: "Не зашифровано",
    status: "Статус",
    operation: "Операция",
    reboot: "Перезагрузить",
    noReboot: "Не перезагружать",
    addDevice: "Добавить устройство",
    deleteDevice: "Удалить устройство",
    clearDevices: "Очистить устройства",
    deviceExists: "Это устройство уже существует",
    deviceDeleted: "Устройство {ip} удалено",
    downloadFile: "Скачать файл?",
    importParam: "Импортировать параметры?",
    connectTimeout: "Время ожидания подключения",
    paramTimeout: "Время ожидания изменения параметров",
    readTimeout: "Глобальное время ожидания запроса",
    progress: "Прогресс"
  },

  // Конфигурация скачивания
  downList: {
    title: "Config скачивания",
    deviceDirectory: "Каталог устройства",
    fileName: "Название файла",
    fileSize: "Размер файла",
    filePath: "Путь к файлу",
    lastModified: "Время последнего изменения",
    addFile: "Добавить файл для скачивания",
    addFolder: "Добавить папку для скачивания",
    fileExists: "Файл {path} уже существует, добавление не удалось!",
    fileDeleted: "Файл {path} удален",
    filesDeleted: "Файлы удалены",
    defaultExportFileName: "Список файлов для скачивания.xlsx",
    exportTitle: "Экспорт списка файлов для скачивания",
    importTitle: "Импорт списка файлов для скачивания",
    exportSuccess: "Экспорт списка файлов успешен",
    exportFailed: "Экспорт списка файлов не удался",
    importSuccess: "Импорт списка файлов успешен",
    importFailed: "Импорт списка файлов не удался",
    fileExistsMsg: "Файл {path} уже существует, добавление не удалось!"
  },

  // Конфигурация параметров
  paramList: {
    title: "Config параметров",
    paramGroup: "Группа параметров",
    groupName: "Название группы",
    paramName: "Название параметра",
    paramDesc: "Описание параметра",
    paramValue: "Значение параметра",
    minValue: "Мин. значение",
    maxValue: "Макс. значение",
    step: "Шаг",
    unit: "Единица измерения",
    searchParamName: "Название параметра",
    searchParamDesc: "Описание параметра",
    importSuccess: "Импорт параметров успешен",
    importFailed: "Импорт параметров не удался",
    exportSuccess: "Экспорт параметров успешен",
    exportFailed: "Экспорт параметров не удался",
    clearSuccess: "Очистка параметров успешна"
  },

  // Диалог прогресса
  progressDialog: {
    title: "Обработка",
    pleaseWait: "Пожалуйста, подождите..."
  },

  packageProgram: {
    saveDir: "Каталог сохранения",
    selectSaveDir: "Выбрать каталог сохранения",
    packageBtn: "Упаковать",
    locateDir: "Найти папку",
    delete: "Удалить",
    sequence: "Номер",
    fileName: "Название файла",
    fileSize: "Размер файла",
    filePath: "Путь к файлу",
    lastModified: "Время последнего изменения",
    operation: "Операция",
    saveDirEmpty: "Пожалуйста, сначала выберите каталог сохранения!",
    packageSuccess: "Упаковка программы завершена!",
    tip: "Подсказка",
    confirmButton: "Подтвердить",
    // Связанные с импортом и экспортом
    defaultExportFileName: "Список файлов упаковки программы.xlsx",
    exportTitle: "Экспорт списка файлов упаковки программы",
    importTitle: "Импорт списка файлов упаковки программы",
    exportSuccess: "Экспорт списка файлов успешен",
    exportFailed: "Экспорт списка файлов не удался",
    importSuccess: "Импорт списка файлов успешен",
    importFailed: "Импорт списка файлов не удался",
    fileExists: "Файл {path} уже существует, добавление не удалось!",
    selectDirSuccess: "Выбор каталога успешен: {dir}",
    locateDirSuccess: "Поиск каталога успешен: {dir}",
    addFileStart: "Открытие селектора файлов...",
    addFileSuccess: "Успешно добавлено {count} файлов/папок",
    addFileNone: "Не добавлено новых файлов/папок",
    deleteSuccess: "Успешно удалено {count} файлов/папок",
    clearSuccess: "Очищены все файлы/папки",
    moveUpSuccess: "Перемещение вверх: {name}",
    moveDownSuccess: "Перемещение вниз: {name}",
    noFileSelected: "Пожалуйста, сначала выберите файлы для упаковки!",
    noDeviceSelected: "Пожалуйста, сначала выберите устройство для упаковки!",
    packageFailed: "Упаковка не удалась: {msg}",
    zipPath: "Путь упаковки: {zipPath}",
    openFileButton: "Открыть файл"
  }
};
