/**
 * Связанное с матричными задачами - Русский
 */
export default {
  // Журналы задач
  logs: {
    taskJobParams: "Параметры TaskJob",
    pauseTaskJob: "Приостановить TaskJob, ID задачи",
    resumeTaskJob: "Возобновить TaskJob, ID задачи, ID процесса",
    doTaskStart: "Начать выполнение задачи",
    allFinished: "Все задачи завершены"
  },

  // Шаги задачи
  steps: {
    connect: "Подключение",
    download: "Скачивание",
    import: "Импорт"
  },

  // Сообщения задач
  messages: {
    connectDevice: "Подключение устройства",
    executeFileDownload: "Выполнение скачивания файла",
    downloadingFile: "Скачивание файла",
    downloadFileFailed: "Скачивание файла не удалось",
    fileDownloadCompleted: "Скачивание файла завершено",
    executeParamImport: "Выполнение импорта установленных значений",
    paramValidationFailed: "Проверка формата установленных значений не удалась",
    paramImportFailed: "Импорт установленных значений не удался",
    paramImportCompleted: "Импорт установленных значений завершен",
    taskCompleted: "Выполнение задачи завершено",
    deviceConnectionFailed: "Подключение устройства не удалось",
    deviceRebootSuccess: "Перезагрузка устройства успешна"
  },

  // Сообщения об ошибках
  errors: {
    paramItemModifyError: "Ошибка изменения элемента установленного значения, ошибочный элемент:",
    paramConfirmError: "Ошибка подтверждения установленного значения, причина ошибки:",
    paramNotFound: "Причина ошибки: соответствующее установленное значение не найдено",
    invalidValue: "Недопустимое значение",
    description: "Описание",
    errorReason: ", причина ошибки:"
  }
};
