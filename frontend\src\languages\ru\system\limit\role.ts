export default {
  limit: {
    role: {
      title: "Управление ролями",
      form: {
        add: "Добавить роль",
        edit: "Редактировать роль",
        view: "Просмотр роли",
        name: "Название роли",
        code: "Код роли",
        category: "Категория роли",
        org: "Принадлежащая организация",
        status: "Статус",
        sort: "Сортировка",
        description: "Описание",
        cancel: "Отме<PERSON>",
        confirm: "Подтвердить",
        validation: {
          name: "Пож<PERSON><PERSON>уйста, введите название роли",
          code: "Пожалуйста, введите код роли",
          category: "Пожалуйста, выберите категорию роли",
          org: "Пожалуйста, выберите принадлежащую организацию",
          status: "Пожалуйста, выберите статус"
        }
      },
      columns: {
        name: "Название роли",
        code: "Код роли",
        category: "Категория роли",
        org: "Принадлежащая организация",
        status: "Статус",
        sort: "Сортировка",
        operation: "Операция"
      },
      category: {
        system: "Системная роль",
        org: "Организационная роль"
      },
      status: {
        enable: "Включено",
        disable: "Отключено"
      },
      grantResource: {
        title: "Авторизованные ресурсы",
        warning: "Пожалуйста, выберите ресурсы для авторизации",
        firstLevel: "Меню первого уровня",
        menu: "Меню",
        buttonAuth: "Разрешения кнопок",
        cancel: "Отмена",
        confirm: "Подтвердить",
        selectDataScope: "Выбрать область данных",
        api: "Интерфейс",
        dataScope: "Область данных"
      },
      grantPermission: {
        title: "Авторизованные разрешения",
        warning: "Пожалуйста, выберите разрешения для авторизации",
        api: "Интерфейс",
        apiPlaceholder: "Пожалуйста, введите название интерфейса",
        dataScope: "Область данных",
        cancel: "Отмена",
        confirm: "Подтвердить"
      },
      dataScope: {
        selectOrg: "Выбрать организацию",
        orgList: "Список организаций",
        cancel: "Отмена",
        confirm: "Подтвердить"
      }
    }
  }
};
