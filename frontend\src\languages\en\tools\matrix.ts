export default {
  // 通用操作
  common: {
    add: "Add",
    index: "Index",
    delete: "Delete",
    clear: "Clear",
    import: "Import",
    export: "Export",
    execute: "Execute",
    moveUp: "Move Up",
    moveDown: "Move Down",
    loading: "Loading...",
    success: "Success",
    failed: "Failed",
    confirm: "Confirm",
    cancel: "Cancel",
    yes: "Yes",
    no: "No",
    operation: "Operation",
    tips: "Tips",
    title: "Prompt"
  },

  // 搜索相关
  search: {
    placeholder: "Search function"
  },

  // 功能列表相关
  functionList: {
    unnamedDevice: "Unnamed Device",
    batchDownload: {
      name: "Batch Download",
      desc: "Batch download of multiple device files and batch import of settings"
    },
    xmlFormatter: {
      name: "XML Formatter",
      desc: "Quickly organize XML data hierarchically"
    },
    jsonFormatter: {
      name: "JSON Formatter",
      desc: "Intelligent formatting of JSON data, supports syntax validation"
    },
    radixConverter: {
      name: "Radix Converter",
      desc: "Supports conversion between binary, decimal, hexadecimal, and other bases"
    },
    temperatureConverter: {
      name: "Temperature Converter",
      desc: "Intelligent conversion between Celsius, Fahrenheit, Kelvin, and other temperature units"
    },
    encryption: {
      name: "Text Encryption/Decryption",
      desc: "Quick text encryption and decryption based on AES, RSA, Base64, etc."
    },
    packageProgram: {
      name: "Program Packaging",
      desc: "Package device runtime files for export, support custom save directory and one-click folder location"
    }
  },

  // 矩阵内容相关
  matrixContent: {
    loading: "Loading...",
    tabs: {
      deviceList: "Devices",
      downloadConfig: "Files",
      paramConfig: "Params"
    }
  },

  // 任务步骤
  taskSteps: {
    connect: "Connect",
    download: "Download",
    import: "Import",
    disconnect: "Disconnect",
    complete: "Complete"
  },

  // 任务消息
  messages: {
    connectDevice: "Connect Device",
    executeFileDownload: "Execute File Download",
    downloadingFile: "Downloading File",
    downloadFileFailed: "File Download Failed",
    fileDownloadCompleted: "File Download Completed",
    executeParamImport: "Execute Parameter Import",
    paramValidationFailed: "Parameter Validation Failed",
    paramImportFailed: "Parameter Import Failed",
    paramImportCompleted: "Parameter Import Completed",
    taskCompleted: "Task Completed",
    deviceConnectionFailed: "Device Connection Failed",
    deviceRebootSuccess: "Device Reboot Successful"
  },

  // 装置列表
  deviceList: {
    title: "Devices",
    deviceListExcel: "Device List.xlsx",
    exportDeviceList: "Export Device List",
    importDeviceList: "Import Device List",
    exportSuccess: "Device list exported successfully",
    exportFail: "Failed to export device list",
    importSuccess: "Device list imported successfully",
    importFail: "Failed to import device list",
    exportSuccessMsg: "Device list exported successfully",
    exportFailMsg: "Device list export failed",
    importSuccessMsg: "Device list imported successfully",
    importFailMsg: "Device list import failed: {msg}",
    deviceName: "Device Name",
    deviceAddress: "Device Address",
    devicePort: "Device Port",
    isEncrypted: "Encrypted",
    encrypted: "Encrypted",
    notEncrypted: "Not Encrypted",
    status: "Status",
    operation: "Operation",
    reboot: "Reboot",
    noReboot: "No Reboot",
    addDevice: "Add Device",
    deleteDevice: "Delete Device",
    clearDevices: "Clear Devices",
    deviceExists: "Device already exists",
    deviceDeleted: "Device {ip} deleted",
    downloadFile: "Download file?",
    importParam: "Import parameter?",
    connectTimeout: "Connection Timeout",
    paramTimeout: "Parameter Modification Timeout",
    readTimeout: "Global Request Timeout",
    progress: "Progress"
  },

  // 下载文件配置
  downList: {
    title: "Download",
    deviceDirectory: "Device Directory",
    fileName: "File Name",
    fileSize: "File Size",
    filePath: "File Path",
    lastModified: "Last Modified",
    addFile: "Add file to download",
    addFolder: "Add folder to download",
    fileExists: "File {path} already exists, add failed!",
    fileDeleted: "File {path} deleted",
    filesDeleted: "Files deleted",
    defaultExportFileName: "Download File List.xlsx",
    exportTitle: "Export Download File List",
    importTitle: "Import Download File List",
    exportSuccess: "File list exported successfully",
    exportFailed: "File list export failed",
    importSuccess: "File list imported successfully",
    importFailed: "File list import failed",
    fileExistsMsg: "File {path} already exists, add failed!"
  },

  // 参数定值配置
  paramList: {
    title: "Parameters",
    paramGroup: "Setting Group",
    groupName: "Group Name",
    paramName: "Parameter Name",
    paramDesc: "Parameter Description",
    paramValue: "Parameter Value",
    minValue: "Min Value",
    maxValue: "Max Value",
    step: "Step",
    unit: "Unit",
    searchParamName: "Parameter Name",
    searchParamDesc: "Parameter Description",
    importSuccess: "Parameter import successful",
    importFailed: "Parameter import failed",
    exportSuccess: "Parameter export successful",
    exportFailed: "Parameter export failed",
    clearSuccess: "Parameter list cleared successfully"
  },

  // 进度对话框
  progressDialog: {
    title: "Processing",
    pleaseWait: "Please wait..."
  },

  packageProgram: {
    saveDir: "Save Directory",
    selectSaveDir: "Select Save Directory",
    packageBtn: "Package",
    locateDir: "Locate Folder",
    delete: "Delete",
    sequence: "No.",
    fileName: "File Name",
    fileSize: "File Size",
    filePath: "File Path",
    lastModified: "Last Modified",
    operation: "Operation",
    saveDirEmpty: "Please select a save directory first!",
    packageSuccess: "Program packaging completed!",
    tip: "Tip",
    confirmButton: "OK",
    // New import/export related
    defaultExportFileName: "Program Package File List.xlsx",
    exportTitle: "Export Program Package File List",
    importTitle: "Import Program Package File List",
    exportSuccess: "File list exported successfully",
    exportFailed: "File list export failed",
    importSuccess: "File list imported successfully",
    importFailed: "File list import failed",
    fileExists: "File {path} already exists, add failed!",
    selectDirSuccess: "Directory selected: {dir}",
    locateDirSuccess: "Located directory: {dir}",
    addFileStart: "Opening file selector...",
    addFileSuccess: "Successfully added {count} files/folders",
    addFileNone: "No new files/folders added",
    deleteSuccess: "Deleted {count} files/folders successfully",
    clearSuccess: "All files/folders cleared",
    moveUpSuccess: "Moved up: {name}",
    moveDownSuccess: "Moved down: {name}",
    noFileSelected: "Please select the files to be packaged!",
    noDeviceSelected: "Please select the devices to be packaged!",
    packageFailed: "Packaging failed: {msg}",
    openFileButton: "Open File",
    zipPath: "Zip Path: {zipPath}"
  }
};
