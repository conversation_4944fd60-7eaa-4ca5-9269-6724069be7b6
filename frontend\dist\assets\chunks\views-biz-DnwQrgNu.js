var pe=Object.defineProperty;var ue=(t,e,a)=>e in t?pe(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a;var m=(t,e,a)=>ue(t,typeof e!="symbol"?e+"":e,a);import{D as k,w,A as me,x as T,T as ge,y as fe,z,B as he,C as $,E as ve,F as Ce,G as Y,H as xe,S as Se,I as ye,J as be,K as De,L as Pe,M as Fe,N as Te,O as K}from"../vendor/charts-vendor-DS7xuoj-.js";import{cR as Ee,cS as Ae,cT as M,cU as te}from"../vendor/vendor-P-ltm-Yc.js";import{C as Ne,G as Re,e as we}from"../vendor/vue-vendor-EiOvILk3.js";import{v as ae}from"../vendor/i18n-vendor-BPpKJ4WV.js";import{w as Ie}from"./utils-DeqEhvvR.js";const ke={language:{title:"语言",zh:"简体中文",en:"英文",es:"西班牙语",fr:"法语",ru:"俄语",tooltip:"多语言"},about:{title:"关于",introduction:"简介",description:"基于 Vue3、TypeScript、Vite4、Pinia、Element-Plus、Electron 等最新技术栈开发的新一代可视化平台调试工具。",versionInfo:"版本信息",toolName:"工具名称",version:"版本号",machineCode:"机器码",loading:"加载中...",machineCodeError:"获取失败",copySuccess:"机器码已复制到剪贴板",copyError:"复制失败",versionFeatures:"版本特征",features:{visualTool:"包含可视化工具连接、装置信息查看、设定量、模拟量、状态量、遥信、遥测、遥控、报告、装置对时、定值导入导出、变量调试功能",configTool:"包含组态工具预览、新增、编辑、自定义图符、关联装置信息功能",themeTool:"包含主题定制、it小工具、装置配置导入导出功能"}},footer:{copyright:"{version}"},header:{minimize:"最小化",maximize:"最大化",restore:"还原",close:"关闭",company:{name:"思源电气",englishName:"Sieyuan"},collapse:{expand:"展开装置",fold:"收起装置",expandTool:"展开工具列表",foldTool:"收起工具列表"},breadcrumb:{home:"首页"},assemblySize:{title:"大小设置",default:"默认",large:"大型",small:"小型"},avatar:{profile:"个人中心",switchApp:"切换应用",logout:"退出登录",logoutConfirm:{title:"温馨提示",message:"您是否确认退出登录?",confirm:"确定",cancel:"取消"},logoutSuccess:"退出登录成功！"},changeModule:{title:"切换模块"},enginConfig:{configType:"配置类型",openDirectory:"打开文件目录",cancel:"取消",confirm:"确定",all:"全部",deviceList:"装置列表",configureList:"组态列表",exportSuccess:"导出配置成功",importSuccess:"导入配置成功",disconnectDeviceFirst:"请先断开已连接的装置",overrideConfirm:"已存在组态列表，是否覆盖?",warmTips:"温馨提示",importConfigFile:"导入配置文件"},userInfo:{title:"个人信息",cancel:"取消",confirm:"确认"},password:{title:"修改密码",cancel:"取消",confirm:"确认"},globalSetting:{title:"设置",tooltip:"设置"},moreInfo:{title:"更多",tooltip:"更多",items:{importConfig:"导入工程配置",printScreen:"截图",search:"菜单搜索",exportConfig:"导出工程配置",about:"关于",help:"帮助"},importConfig:{title:"导入工程配置",placeholder:"请选择要导入的配置文件路径"},exportConfig:{title:"导出工程配置",placeholder:"请选择导出目录"}},searchMenu:{placeholder:"菜单搜索：支持菜单名称、路径",empty:"暂无菜单"},theme:{title:"主题",tooltip:"主题"}},main:{maximize:{exit:"退出最大化"}},theme:{title:"布局设置",quickTheme:{title:"主题设置"},layoutSettings:{title:"布局设置"},layout:{title:"布局样式",columns:"分栏",classic:"经典",transverse:"横向",vertical:"纵向"},global:{title:"全局主题",primary:"主题颜色",dark:"暗黑模式",grey:"灰色模式",weak:"色弱模式",special:"特殊模式"},mode:{light:"浅色",dark:"深色"},interface:{title:"界面设置",watermark:"水印",breadcrumb:"面包屑",breadcrumbIcon:"面包屑图标",tabs:"标签栏",tabsIcon:"标签栏图标",footer:"页脚",drawerForm:"抽屉表单"},presetThemes:{title:"预设主题",default:{name:"默认主题",description:"经典蓝色主题"},dark:{name:"深色主题",description:"护眼深色模式"},techBlue:{name:"科技蓝",description:"现代科技感蓝色"},deepBlue:{name:"深海蓝",description:"深邃稳重蓝色"},nature:{name:"自然主题",description:"清新绿色系"},forestGreen:{name:"森林绿",description:"深沉森林绿色"},warm:{name:"温暖主题",description:"温暖橙色系"},sunsetOrange:{name:"日落橙",description:"温暖日落橙色"},elegant:{name:"优雅主题",description:"高贵紫色系"},lavender:{name:"薰衣草",description:"柔和薰衣草紫"},sakura:{name:"樱花粉",description:"浪漫樱花粉色"},rose:{name:"玫瑰红",description:"热情玫瑰红色"},lime:{name:"青柠绿",description:"活力青柠绿色"},skyBlue:{name:"天空蓝",description:"清澈天空蓝色"},eyeCare:{name:"护眼模式",description:"灰色护眼主题"}},colors:{techBlue:{name:"科技蓝",description:"现代科技感"},natureGreen:{name:"自然绿",description:"清新自然"},vibrantOrange:{name:"活力橙",description:"温暖活力"},elegantPurple:{name:"优雅紫",description:"高贵优雅"},romanticPink:{name:"浪漫粉",description:"温柔浪漫"},freshCyan:{name:"清新青",description:"清新淡雅"},brightYellow:{name:"明亮黄",description:"明亮活泼"},warmOrange:{name:"温暖橙",description:"温暖舒适"},limeGreen:{name:"青柠绿",description:"清新青柠"},deepBlue:{name:"深邃蓝",description:"深邃稳重"},golden:{name:"金色",description:"经典金色"},chinaRed:{name:"中国红",description:"传统红色"}}},tabs:{moreButton:{refresh:"刷新",closeCurrent:"关闭当前",closeLeft:"关闭左侧",closeRight:"关闭右侧",closeOthers:"关闭其它",closeAll:"关闭所有"}}},Le={confirm:"确认",cancel:"取消",save:"保存",delete:"删除",remove:"移除",edit:"编辑",add:"添加",search:"搜索",reset:"重置",export:"导出",import:"导入",upload:"上传",download:"下载",preview:"预览",print:"打印",refresh:"刷新",back:"返回",next:"下一步",submit:"提交",loading:"加载中...",success:"成功",error:"错误",warning:"警告",info:"信息",index:"序号",title:"标题",operation:"操作",execute:"执行",clear:"清空",moveUp:"上移",moveDown:"下移",status:{active:"激活",inactive:"未激活",enabled:"启用",disabled:"禁用",online:"在线",offline:"离线",pending:"待处理",completed:"已完成",failed:"失败"},time:{today:"今天",yesterday:"昨天",thisWeek:"本周",lastWeek:"上周",thisMonth:"本月",lastMonth:"上月",custom:"自定义范围"},pagination:{total:"总计",items:"条",page:"页",perPage:"每页",showing:"显示",to:"至",of:"共"},validation:{required:"此字段为必填项",email:"请输入有效的电子邮箱地址",phone:"请输入有效的电话号码",number:"请输入有效的数字",integer:"请输入有效的整数",min:"最小值为 {min}",max:"最大值为 {max}",length:"长度必须为 {length}",minLength:"最小长度为 {min}",maxLength:"最大长度为 {max}"},message:{saveSuccess:"保存成功",deleteSuccess:"删除成功",updateSuccess:"更新成功",operationSuccess:"操作成功",operationFailed:"操作失败",confirmDelete:"确定要删除吗？",noData:"暂无数据",loading:"加载中...",networkError:"网络错误，请重试",copySuccess:"复制成功"},languageSyncWarning:"语言同步到后端失败，但前端语言已成功切换",customFileSelector:{title:"选择文件和文件夹",searchPlaceholder:"搜索文件或文件夹...",selectedItems:"已选择的项目",clearAll:"清空全部",noItemsSelected:"暂未选择任何项目",cancel:"取消",confirm:"确认",loading:"加载中...",error:{loadFailed:"加载失败",accessDenied:"访问被拒绝",notFound:"路径不存在"}},test:{languageSwitch:{title:"语言切换测试",progressDialog:"进度对话框测试",showProgress:"显示进度对话框",temperatureConverter:"温度转换器测试",temperatureDesc:"以下温度单位名称应该在语言切换后自动更新：",reportNames:"报告名称测试",reportDesc:"以下报告相关的名称应该在语言切换后自动更新：",autoRefresh:"自动刷新",showHidden:"显示隐藏项",instructions:"测试说明",step1:"点击右上角的语言切换按钮",step2:"选择不同的语言（如英语、西班牙语、法语）",step3:"观察页面上的文本是否立即更新为新语言"},errorPageTest:{title:"错误页面按钮测试",description:"测试错误页面按钮在不同语言环境下的显示效果",languageSelector:"选择语言",buttonPreview:"按钮预览",errorPage:"错误页面",textLength:"文本长度",testLinks:"测试链接",goto404:"访问404页面",goto403:"访问403页面",goto500:"访问500页面",comparisonTable:"多语言对比表",language:"语言",buttonText:"按钮文本",preview:"预览"}}},ze={loading:{checking:"正在检查授权...",loading:"正在加载..."},auth:{invalid:"授权无效：{msg}",unknownError:"未知错误",checkFailed:"授权检查失败，请检查网络连接"}},Me={layout:ke,common:Le,app:ze},Ve={dataScope:{title:"数据范围选择器",selectOrg:"选择组织",orgList:"组织列表",cancel:"取消",confirm:"确定"},grantResource:{title:"授权资源",warning:"非超管角色不可被授权系统模块菜单资源",firstLevel:"一级目录",menu:"菜单",buttonAuth:"按钮授权",cancel:"取消",confirm:"确定",selectDataScope:"请选择数据范围"}},qe={dashboard:"仪表盘",system:"系统管理",user:"用户管理",role:"角色管理",menu:"菜单管理",changeModule:{title:"更改模块->",belongModule:"所属模块",requiredModule:"请选择所属模块"},debug:{title:"调试",description:"调试"},configure:{title:"组态",description:"组态"},tool:{title:"工具",description:"工具"},sysConfig:{title:"系统设置",description:"系统设置"}},Be={limit:{module:{title:"模块名称",icon:"图标",status:"状态",sort:"排序",description:"说明",createTime:"创建时间",operation:"操作",add:"新增模块",edit:"编辑模块",delete:"删除模块",deleteConfirm:"删除所选模块",deleteConfirmWithName:"删除【{name}】模块",form:{title:"请输入模块名称",status:"请选择状态",sort:"请输入排序",icon:"请选择图标"}},menu:{title:"菜单名称",icon:"菜单图标",type:"菜单类型",component:"组件名称",path:"路由地址",componentPath:"组件路径",sort:"排序",status:"状态",description:"说明",operation:"操作",add:"新增菜单",edit:"编辑菜单",delete:"删除菜单",deleteConfirm:"删除所选菜单",deleteConfirmWithName:"删除【{name}】菜单",form:{title:"请输入菜单名称",parent:"请选择上级菜单",type:"请选择菜单类型",path:"请输入路由地址",component:"请输入组件地址",sort:"请输入排序",icon:"请选择图标",status:"请选择状态",link:"请输入链接地址"}},button:{title:"按钮名称",code:"按钮编码",sort:"排序",description:"说明",operation:"操作",add:"新增按钮",edit:"编辑按钮",delete:"删除按钮",deleteConfirm:"删除所选按钮",deleteConfirmWithName:"删除【{name}】按钮",batch:{title:"批量新增按钮",shortName:"权限简称",codePrefix:"编码前缀",form:{shortName:"请输入权限简称",codePrefix:"请输入编码前缀"}},form:{title:"请输入按钮名称",code:"请输入按钮编码",sort:"请输入排序"}},role:{title:"角色名称",org:"所属组织",category:"角色类型",status:"状态",sort:"排序",description:"说明",createTime:"创建时间",operation:"操作",add:"新增角色",edit:"编辑角色",delete:"删除角色",deleteConfirm:"删除所选角色",deleteConfirmWithName:"删除【{name}】角色",grant:{resource:"授权资源",permission:"授权权限",dataScope:"数据范围"},form:{title:"请输入角色名称",org:"请选择所属组织",category:"请选择角色类型",status:"请选择状态"}},spa:{title:"单页名称",icon:"图标",type:"单页类型",path:"路由地址",component:"组件路径",sort:"排序",description:"说明",createTime:"创建时间",operation:"操作",add:"新增单页",edit:"编辑单页",delete:"删除单页",deleteConfirm:"删除所选单页",deleteConfirmWithName:"删除【{name}】单页",form:{title:"请输入单页名称",type:"请选择单页类型",path:"请输入路由地址",component:"请输入组件地址",sort:"请输入排序",icon:"请选择图标",link:"请填写链接地址,例:http://www.baidu.com"}}}},Oe={config:{title:"系统配置",paramTitle:"参数配置",systemName:"系统名称",systemVersion:"系统版本",waveToolPath:"第三方波形工具路径",waveToolPathPlaceholder:"请选择第三方波形工具路径",openDirectory:"打开文件目录",save:"保存",reset:"重置",saveSuccess:"保存成功",selectWaveTool:"选择波形分析工具",paramRefreshTime:"定值刷新间隔（ms）",reportRefreshTime:"报告刷新间隔（ms）",stateRefreshTime:"状态量刷新间隔（ms）",variRefreshTime:"变量刷新间隔（ms）",configTitle:"配置",configKey:"配置键",configValue:"配置值",remark:"备注",sortCode:"排序",operation:"操作",deleteConfirm:"删除【{key}】配置"}},_e={machineCode:"机器码",activationCode:"激活码",activationCodePlaceholder:"请输入激活码",reset:"重置",activate:"激活",success:{title:"激活成功",message:"系统已成功激活"},error:{unknown:"激活失败：{msg}",network:"激活失败，请检查网络连接"}},Ge={title:"单页管理",list:{title:"单页列表",add:"新增单页",deleteSelected:"删除选中",deleteConfirm:"是否确认删除单页 {title}？"},form:{title:"{opt}单页",basicSettings:"基本设置",functionSettings:"功能设置",name:"单页名称",type:"单页类型",icon:"图标",path:"路由地址",pathPlaceholder:"请填写路由地址,例:/home/<USER>",componentName:"组件名称",componentPath:"组件地址",linkPath:"链接地址",linkPathPlaceholder:"请填写链接地址,例:http://www.baidu.com",sort:"排序",description:"说明",isHome:"设置主页",isHide:"隐藏页面",isFull:"页面全屏",isAffix:"固定标签页",isKeepAlive:"路由缓存",cancel:"取消",confirm:"确定",nameRequired:"请输入单页名称",typeRequired:"请选择单页类型",pathRequired:"请输入路由地址",componentNameRequired:"请输入组件名称",componentPathRequired:"请输入组件地址",sortRequired:"请输入排序",iconRequired:"请选择图标"}},Ue={title:"系统登录",account:{title:"账号登录",username:"请输入用户名",password:"请输入密码",captcha:"请输入验证码",tenant:"请选择租户"},phone:{title:"手机号登录",phone:"请输入手机号码",smsCode:"请输入短信验证码",getCode:"获取验证码",machineVerify:"机器验证",captcha:"请输入验证码",sendSuccess:"验证码发送成功",sendFailed:"短信验证码发送失败"},button:{reset:"重置",login:"登录"},dialog:{cancel:"取消",confirm:"确定"}},We={title:"帮助中心",subtitle:"帮助与文档",catalog:"目录",searchPlaceholder:"搜索帮助内容...",loadFail:"加载帮助文档失败，请稍后重试。"},je={role:Ve,menu:qe,limit:Be,sys:Oe,activate:_e,spa:Ge,login:Ue,help:We},He={configure:{remoteSet:"遥设"},console:{title:"控制台",clear:"清空",selectAll:"全选",copy:"复制",copySuccess:"复制成功",noTextSelected:"没有选中的文本",copyFailed:"复制失败",clearSuccess:"清空控制台",collapse:"折叠",expand:"展开"},groupInfo:{title:"分组信息",table:{id:"序号",name:"名称",desc:"描述",fc:"FC",count:"数量"},messages:{fetchDataError:"获取数据时发生错误",fetchedData:"获取的数据："}},treeClickLog:"点击 treeClick : ",contentView:"内容视图",emptyDeviceId:"当前装置id为空",invalidResponseStructure:"无效的响应结构",formattedMenuDataLog:"格式化后的菜单数据 ===",allSettings:"全部定值",allEditSpSettings:"全部单区定值",allEditSgSettings:"全部多区定值",deviceTreeDataLog:"装置树数据",failedToLoadMenu:"加载装置菜单失败:",innerTabs:{contentView:"内容视图",fileUpload:"文件上传",fileDownload:"文件下载",deviceTime:"装置对时",deviceOperate:"装置操作",variableDebug:"变量调试",oneClickBackup:"一键备份",entryConfig:"词条配置",tabClickLog:"标签页点击："},devices:{notConnectedAlt:"装置未连接",pleaseConnect:"请先连接装置！"},list:{unnamedDevice:"未命名装置",connected:"连接",disconnected:"断开",connect:"连接",edit:"编辑",disconnect:"断开",remove:"删除",noDeviceFound:"没有找到设备",handleClickLog:"点击 handleListClick:",disconnectBeforeEdit:"请先断开连接再编辑",connectSuccess:"装置 {name}：连接成功",connectExist:"装置 {name}：连接已存在",connectFailed:"装置 {name}：连接失败",connectFailedReason:"装置连接失败原因：",disconnectedSuccess:"装置 {name}：已断开",disconnectedNotify:"装置 {name} 连接已断开",currentDisconnectedNotify:"当前装置连接已断开",operateFailed:"装置 {name}：操作失败",disconnectBeforeDelete:"请先断开连接再删除",dataLog:"数据:",ipPortExist:"该IP和端口已存在，请勿重复添加",messageMonitor:"报文监视",connectFirst:"请先连接装置",messageMonitorOpened:"装置 {name}：已打开报文监视"},messageMonitor:{title:"报文监视",start:"开始监视",stop:"停止监视",clear:"清空",export:"导出",expand:"展开",collapse:"折叠",close:"关闭",messageType:"报文",noMessages:"暂无报文数据",noMessagesToExport:"没有可导出的报文数据",startSuccess:"开始监视报文",stopSuccess:"停止监视报文",clearSuccess:"清空报文成功",exportSuccess:"导出报文成功",exportFailed:"导出报文失败",toggleFailed:"切换监视状态失败",pauseScroll:"暂停滚动",resumeScroll:"恢复滚动",monitoring:"监听中",copy:"复制",copySuccess:"消息已复制到剪贴板",copyFailed:"复制失败",autoScrollEnabled:"已开启自动滚动",autoScrollDisabled:"已暂停自动滚动",send:"发送",receive:"接收",message:"报文"},search:{placeholder:"搜索装置",ipPortExist:"该IP和端口已存在，请勿重复添加"},summaryPie:{other:"其他",title:"定值数量占比",subtext:"定值组定值"},deviceInfo:{title:"装置信息",export:"导出",exportTitle:"导出装置信息",exportLoading:"正在导出装置基本信息...",exportSuccess:"导出装置基本信息成功",exportFailed:"导出装置基本信息失败",getInfoFailed:"获取装置信息失败。失败原因：{msg}",getInfoFailedEmpty:"获取装置信息失败。失败原因：数据为空！",defaultFileName:"装置信息.xlsx",confirm:"确定",tip:"提示"},allParamSetting:{title:"全部定值",autoRefresh:"自动刷新",refresh:"刷新",confirm:"确认",import:"导入",export:"导出",groupTitle:"定值组：",allGroups:"全部",noDataToImport:"没有需要导入的数据",importSuccess:"定值导入成功",importFailed:"定值导入失败: {msg}",requestFailed:"请求失败，请稍后再试",queryFailed:"定值查询失败: {msg}",unsavedChanges:"存在未保存的修改，是否继续刷新？",confirmButton:"确定",cancelButton:"取消",alertTitle:"提示",errorTitle:"错误",noDataToConfirm:"没有需要确认的数据",confirmSuccess:"定值更新成功",confirmFailed:"定值更新失败: ",responseLog:"响应数据:",continueAutoRefresh:"继续启用自动刷新",settingGroup:"定值组",all:"全部",minValue:"最小值",maxValue:"最大值",step:"步长",unit:"单位",searchNamePlaceholder:"输入定值名称进行搜索",searchDescPlaceholder:"输入定值描述进行搜索",autoRefreshWarning:"自动刷新开启时不允许修改数据",invalidValue:"定值{name}的值{value}不在合法的区间内",exportFileName:"装置参数定值_全部定值.xlsx",selectPathLog:"选择路径: ",exportSuccess:"导出定值列表成功"},variable:{autoRefresh:"自动刷新",variableName:"变量名",inputVariableName:"请输入待添加的变量名",refresh:"刷新",add:"新增",confirm:"确认",import:"导入",export:"导出",delete:"删除",noDataToConfirm:"没有需要确认的数据",warning:"告警",variableModifiedSuccess:"变量修改成功",variableModifiedFailed:"变量修改失败，失败原因：",requestFailed:"请求失败，请稍后再试",error:"错误",success:"成功",variableAddSuccess:"变量添加成功",variableAddFailed:"变量添加失败，失败原因：",variableDeleteSuccess:"变量删除成功",variableDeleteFailed:"变量删除失败,失败原因:",exportSuccess:"导出装置调试变量信息成功",exportFailed:"导出装置调试变量信息失败,失败原因:",importSuccess:"导入装置调试变量信息成功",importFailed:"导入装置调试变量信息失败:",confirmRefresh:"存在未保存的修改，是否继续刷新？",confirmAutoRefresh:"存在未保存的修改，是否继续启用自动刷新？",pleaseInputVariableName:"请填写变量名",exportTitle:"导出装置调试变量",importTitle:"导入装置调试变量",defaultExportPath:"装置调试变量.xlsx",title:"变量调试",variableNamePlaceholder:"请输入待添加的变量名",batchDelete:"批量删除",modifySuccess:"变量修改成功",modifyFailed:"变量修改失败，失败原因：{msg}",alertTitle:"告警",successTitle:"提示",confirmButton:"确定",cancelButton:"取消",sequence:"序号",id:"ID",name:"名称",value:"值",type:"类型",description:"描述",address:"地址",operation:"操作",enterVariableName:"请输入待添加的变量名",responseLog:"响应数据:",addSuccess:"变量添加成功",addFailed:"变量添加失败，失败原因：",addFailedWithName:"变量{name}添加失败: {reason}",exportFileName:"装置调试变量.xlsx",selectPathLog:"选择路径:",exportSuccessLog:"导出装置调试变量信息成功，{path}",exportFailedLog:"导出装置调试变量信息失败,失败原因:",importFailedLog:"导入装置调试变量信息失败:",unsavedChanges:"存在未保存的修改，是否继续刷新？",continueAutoRefresh:"继续启用自动刷新",tip:"提示",sequenceNumber:"序号",autoRefreshEditForbidden:"自动刷新模式下禁止编辑",warningTitle:"警告",invalidNumber:"无效的数值: {value}",cancel:"取消"},backup:{sequence:"序号",title:"装置备份",savePath:"保存路径",setPath:"设置备份保存路径",setPathTitle:"设置路径",startBackup:"开始备份",cancelBackup:"取消备份",backup:"备份",backupType:"备份类型",progress:"进度",status:"状态",operation:"操作",backupTypes:{paramValue:"备份装置参数定值",faultInfo:"备份装置故障报告信息",cidConfigPrjLog:"备份cid/ccd/device_config/debug_info/prj/log",waveReport:"备份装置录波文件"},backupDesc:"备份内容说明",backupDescTypes:{paramValue:"导出装置定值（定值导出.xlsx）",faultInfo:"导出装置故障信息（事件/操作/故障/审计报告）",cidConfigPrjLog:"导出配置文件（CID/CCD、XML配置、日志文件）",waveReport:"导出装置录波文件（/wave/comtrade）"},locateFolder:"定位文件夹",backupSuccess:"备份成功",backupFailed:"备份失败",openFolderFailed:"打开文件夹失败",noTypeSelected:"请先选择备份类型",cancelSuccess:"取消成功",cancelFailed:"取消失败",noBackupToCancel:"当前没有正在进行的备份任务",noTaskIdFound:"未找到任务ID，无法取消",backupStatus:{starting:"开始备份",userCancelled:"用户取消",transferring:"传输中"},console:{pathNotSet:"未设置备份路径，无法开始备份",noTypeSelected:"未选择备份类型，无法开始备份",startBackup:"开始备份，类型：{types}, 路径：{path}",backupException:"备份异常：{error}",pathSelected:"已选择备份路径：{path}",pathNotSelected:"未选择备份路径",pathNotSetForLocate:"未设置备份路径，无法定位文件夹",folderOpened:"已打开备份文件夹：{path}",openFolderFailed:"打开备份文件夹失败：{error}",taskCompleted:"任务处理完成",taskCancelled:"任务已取消",typeError:"类型[{type}]发生错误：{error}",typeCompleted:"类型[{type}]备份完成",typeCancelled:"类型[{type}]已取消",typeFailed:"类型[{type}]失败",attemptCancel:"尝试取消备份任务",noTaskIdFound:"未找到任务ID，无法取消备份",cancelSuccess:"备份任务已取消",cancelFailed:"取消备份失败：{error}",cancelException:"取消备份异常：{error}",singleCancelSuccess:"类型[{type}]取消成功",singleCancelFailed:"类型[{type}]取消失败：{error}",singleCancelException:"类型[{type}]取消异常：{error}"}},operate:{title:"装置操作",manualWave:"手动录波",resetDevice:"装置复归",clearReport:"清除报告",clearWave:"清除录波",executing:"执行中...",selectOperation:"请选择操作",success:{manualWave:"手动录波成功",resetDevice:"装置复归成功",clearReport:"清除报告成功",clearWave:"清除录波成功"},fail:{manualWave:"手动录波失败，失败原因：",resetDevice:"装置复归失败，失败原因：",clearReport:"清除报告失败，失败原因：",clearWave:"清除录波失败，失败原因："}},time:{title:"装置对时",currentTime:"当前时间",deviceTime:"装置时间",selectDateTime:"选择日期时间",milliseconds:"毫秒",now:"此刻",read:"读取",write:"写入",readSuccess:"读取装置时间成功。",readFailed:"读取装置时间失败: {msg}",readFailedInvalidFormat:"读取装置时间失败: 无效的时间格式",readFailedDataError:"读取装置时间失败: 时间数据格式错误",writeSuccess:"写入装置时间成功。",writeFailed:"写入装置时间失败: {msg}",writeFailedInvalidFormat:"写入装置时间失败: 无效的时间格式",millisecondsRangeError:"毫秒的取值范围应在0-999之间",unknownError:"未知错误"},reportOperate:{title:"报告操作",date:"日期：",search:"查询",save:"保存",clearList:"清除列表",loading:"数据加载中",progress:{title:"进度信息",loading:"加载中",searching:"正在查询{type}"},table:{reportId:"报告编号",name:"名称",time:"时间",operationAddress:"操作地址",operationParam:"操作参数",value:"值",step:"步骤",source:"源",sourceType:"源类型",result:"结果"},messages:{selectDateRange:"请选择完整的时间范围",noDataToSave:"无数据可保存",saveSuccess:"保存成功",saveReport:"保存报告"}},reportGroup:{title:"报告组",date:"日期：",search:"查询",save:"保存",clearList:"清除列表",autoRefresh:"自动刷新",loading:"数据加载中",progress:{title:"进度信息",loading:"加载中",searching:"正在查询{type}"},table:{reportId:"报告编号",time:"时间",description:"描述"},contextMenu:{uploadWave:"召唤录波",getHistoryReport:"取历史报告",saveResult:"保存结果",clearContent:"清除页面内容"},messages:{selectDateRange:"请选择完整的时间范围",noDataToSave:"无数据可保存",noFileToUpload:"无文件可召唤",saveSuccess:"保存成功",saveReport:"保存报告",waveToolNotConfigured:"未配置第三方波形工具路径",waveFileUploading:"录波文件召唤中",waveFileUploadComplete:"录波文件召唤完成",waveFileUploadCompleteWithPath:"录波文件召唤完成，路径：{path}",openWaveFileConfirm:"是否通过第三方工具打开波形文件?",openWaveFileTitle:"温馨提示",confirm:"确定",cancel:"取消"},refresh:{stop:"停止刷新",start:"自动刷新"},hiddenItems:{show:"显示隐藏条目",hide:"不显示隐藏条目"}},fileDownload:{title:"文件下载",deviceDirectory:"装置目录",reboot:"重启",noReboot:"不重启",selectFile:"选择文件",addDownloadFile:"添加待下载的文件",addDownloadFolder:"添加待下载的文件夹",addDownloadFilesAndFolders:"添加文件和文件夹",downloadFile:"下载文件",cancelDownload:"取消下载",importList:"导入列表",exportList:"导出列表",batchDelete:"批量删除",clearList:"清空列表",download:"下载",delete:"删除",fileName:"文件名称",fileSize:"文件大小",filePath:"文件路径",lastModified:"最后修改时间",progress:"进度",status:"状态",operation:"操作",folder:"文件夹",waitingDownload:"等待下载",calculatingFileInfo:"计算文件信息",downloadPreparing:"下载准备",downloading:"下载中......",downloadComplete:"下载完成",downloadError:"下载出错：",userCancelled:"用户取消",allFilesComplete:"下载完成",fileExists:"文件{path}已经存在，添加失败！",selectValidFile:"请选择合法的文件进行下载操作",remotePathEmpty:"远程路径不能为空",noDownloadTask:"没有获取到下载任务无法取消",noDownloadInProgress:"当前没有正在进行的下载任务",noFilesSelected:"请选择要操作的文件",fileSizeZero:"文件 {fileName} 大小为0，无法下载",downloadCancelled:"文件{path}下载取消完成",downloadCancelledFailed:"文件{path}下载取消失败，失败原因：{msg}",fileDeleted:"文件{path}删除完成",exportSuccess:"导出下载文件列表成功",exportFailed:"导出下载文件列表失败",importSuccess:"导入下载文件列表成功",importFailed:"导入下载文件列表失败：{msg}",downloadList:"下载文件列表",exportTitle:"导出下载文件列表",importTitle:"导入下载文件列表",error:"错误",tip:"提示",confirm:"确定",sequence:"序号",confirmButton:"确定",cancelButton:"取消",alertTitle:"提示",errorTitle:"错误",successTitle:"成功",warningTitle:"警告",loading:"加载中",executing:"执行中...",noData:"暂无数据",selectDateRange:"请选择日期范围",search:"搜索",save:"保存",clear:"清空",refresh:"刷新",stop:"停止",start:"开始",show:"显示",hide:"隐藏",showHiddenItems:"显示隐藏条目",hideHiddenItems:"隐藏条目",continue:"继续",cancel:"取消",confirmImport:"确认导入",confirmExport:"确认导出",confirmDelete:"确认删除",confirmClear:"确认清空",confirmCancel:"确认取消",confirmContinue:"确认继续",confirmStop:"确认停止",confirmStart:"确认开始",confirmShow:"确认显示",confirmHide:"确认隐藏",confirmRefresh:"确认刷新",confirmSave:"确认保存",confirmSearch:"确认搜索",confirmClearList:"确认清空列表",confirmImportList:"确认导入列表",confirmExportList:"确认导出列表",confirmBatchDelete:"确认批量删除",confirmDownload:"确认下载",confirmCancelDownload:"确认取消下载",confirmDeleteFile:"确认删除文件",confirmClearFiles:"确认清空文件",confirmImportFiles:"确认导入文件",confirmExportFiles:"确认导出文件",confirmBatchDeleteFiles:"确认批量删除文件",confirmDownloadFiles:"确认下载文件",confirmCancelDownloadFiles:"确认取消下载文件",rename:"下载重命名",renamePlaceholder:"下载时重命名（可选）",renameCopyFailed:"重命名复制文件失败：",packageProgram:"程序打包",selectSaveDir:"选择保存目录",packageBtn:"打包",locateDir:"定位文件夹",saveDirEmpty:"请先选择保存目录！",packageSuccess:"程序打包完成！",packageFailed:"打包失败：{msg}",noFileSelected:"请先勾选需要打包的文件！",zipPath:"打包路径: {zipPath}",addToDownload:"添加到下载界面",fileAdded:"文件已添加到下载列表：{path}",rebootSuccess:"装置重启成功",rebootFailed:"装置重启失败：{msg}"},fileUpload:{serialNumber:"序号",title:"文件上传",importList:"导入列表",exportList:"导出列表",batchDelete:"批量删除",clearList:"清空列表",upload:"上传",cancelUpload:"取消上传",delete:"删除",sequence:"序号",fileName:"文件名称",fileSize:"文件大小",filePath:"文件路径",lastModified:"最后修改时间",progress:"进度",statusTitle:"状态",status:{waiting:"等待上传",preparing:"上传准备",uploading:"上传中......",completed:"上传完成",error:"上传出错：",cancelled:"用户取消"},operation:"操作",calculatingFileInfo:"计算文件信息",uploadPreparing:"上传准备",uploading:"上传中......",uploadComplete:"上传完成",uploadError:"上传出错：{errorMsg}",userCancelled:"用户取消",allFilesComplete:"上传完成",fileExists:"文件{path}已经存在，添加失败！",invalidFile:"请选择合法的文件进行上传操作",emptySavePath:"文件保存路径不能为空",fileUploadComplete:"文件{fileName}上传完成",selectPath:"选择路径",pathOptions:{shr:"/shr",configuration:"/shr/configuration",log:"/log",wave:"/wave",comtrade:"/wave/comtrade"},deviceDirectory:"装置目录",savePath:"保存路径",setPath:"设置路径",getFiles:"获取文件",uploadFiles:"上传文件",errors:{invalidFile:"请选择合法的文件进行上传操作",emptySavePath:"文件保存路径不能为空",noUploadTask:"没有获取到上传任务无法取消",noUploadInProgress:"当前没有正在进行的上传任务",noFilesSelected:"请选择要操作的文件",getFilesFailed:"获取装置目录文件失败",fileSizeZero:"文件 {fileName} 大小为0，无法上传"},messages:{uploadCompleted:"文件上传完成",uploadCancelled:"文件上传取消完成",clearListSuccess:"清空文件列表成功"}},info:{title:"装置信息",export:"导出",exportSuccess:"导出装置基本信息成功",exportFailed:"导出装置基本信息失败",exportTip:"提示",confirm:"确定",exportLoading:"正在导出装置基本信息...",getInfoFailed:"获取装置信息失败。失败原因：",dataEmpty:"数据为空！"},summary:{title:"装置分组总览",basicInfo:"基本信息",settingTotal:"定值总数",telemetry:"遥测",teleindication:"遥信",telecontrol:"遥控",driveOutput:"出口传动",settingRatio:"定值占比"},dict:{refresh:"刷新",confirm:"确认",import:"导入",export:"导出",sequence:"序号",shortAddress:"短地址",shortAddressTooltip:"输入待搜索的短地址",chinese:"中文",english:"英文",spanish:"西文",french:"法文",russian:"俄文",operation:"操作",confirmLog:"确认字典",importLog:"导入字典",exportLog:"导出字典",refreshLog:"刷新字典",newValueLog:"新值：",loadSuccess:"词条加载成功",loadFailed:"词条加载失败",saveSuccess:"词条保存成功",saveFailed:"词条保存失败",partialFailed:"部分词条保存失败",noChanges:"没有修改的词条",confirmMessage:"确定要保存修改的词条吗？"},allParamCompare:{title:"导入全部定值差异对比",cancel:"取消",confirm:"确认导入",groupName:"组名称",name:"名称",description:"描述",minValue:"最小值",maxValue:"最大值",step:"步长",unit:"单位",address:"地址",oldValue:"旧值",newValue:"新值",sequence:"序号",searchName:"输入定值名称进行搜索",searchDescription:"输入定值描述进行搜索",messages:{noSelection:"未选中任何数据",error:"错误"}},deviceForm:{title:{add:"添加装置",edit:"编辑装置"},name:"装置名称",ip:"IP地址",port:"端口",connectTimeout:"连接超时时间(毫秒)",readTimeout:"全局请求超时时间(毫秒)",paramTimeout:"定值修改超时时间(毫秒)",encrypted:"加密连接",advanced:{show:"展开高级选项",hide:"收起高级选项"},buttons:{cancel:"取消",confirm:"确定"},messages:{nameRequired:"请输入装置名称",nameTooLong:"装置名称不宜过长",invalidIp:"请输入有效的IP地址",invalidPort:"端口号必须在1-65535之间",timeoutTooShort:"超时时间不宜太短"}},paramCompare:{title:"导入定值差异对比",cancel:"取消",confirm:"确认导入",sequence:"序号",name:"名称",description:"描述",minValue:"最小值",maxValue:"最大值",step:"步长",unit:"单位",address:"地址",oldValue:"旧值",newValue:"新值",searchName:"输入定值名称进行搜索",searchDescription:"输入定值描述进行搜索",messages:{noSelection:"未选中任何数据",error:"错误"}},progress:{title:"进度信息",executing:"执行中..."},remoteYm:{title:"远程遥脉",sequence:"序号",shortAddress:"短地址",description:"描述",value:"值",operation:"操作",inputShortAddressFilter:"输入短地址过滤",inputDescriptionFilter:"输入描述过滤",invalidData:"数据{name}的值{value}不合法",error:"错误",success:"成功",executeSuccess:"执行成功",prompt:"提示",confirmButton:"确定",confirmExecute:"确认执行",confirm:"确认",executeButton:"执行",cancelButton:"取消"},remoteYt:{title:"远程遥调",sequence:"序号",directControl:"直控",selectControl:"选控",shortAddress:"短地址",description:"描述",value:"值",operation:"操作",inputShortAddressFilter:"输入短地址过滤",inputDescriptionFilter:"输入描述过滤",invalidData:"数据{name}的值{value}不合法",error:"错误",success:"成功",executeSuccess:"执行成功",prompt:"提示",confirm:"确定",errorInfo:"错误信息",executeFailed:"遥调执行失败，失败原因：{msg}",executeSuccessLog:"{desc}遥调执行成功",cancelSuccess:"取消成功",cancelFailed:"遥调取消失败，失败原因：{msg}",selectSuccess:"选择成功，是否执行？",confirmInfo:"确认信息",execute:"执行",cancel:"取消"},paramSetting:{title:"装置参数定值",autoRefresh:"自动刷新",refresh:"刷新",confirm:"确认",import:"导入",export:"导出",currentEditArea:"当前运行区",selectEditArea:"当前编辑区",noDataToImport:"没有需要导入的数据",noDataToConfirm:"没有需要确认的数据",importSuccess:"定值导入成功",importFailed:"定值导入失败",updateSuccess:"定值更新成功",updateFailed:"定值更新失败",requestFailed:"请求失败，请稍后再试",setEditArea:"设置",setEditAreaTitle:"设置编辑区",setEditAreaSuccess:"编辑区设置成功",modifiedWarning:"存在未保存的修改，是否继续刷新？",autoRefreshWarning:"存在未保存的修改，是否继续启用自动刷新？",autoRefreshDisabled:"自动刷新开启时不允许修改数据",invalidValue:"定值{name}的值{value}不在合法的区间内",exportSuccess:"导出装置参数定值成功",exportFailed:"导出装置参数定值失败",noDiffData:"未获取到差异数据",table:{index:"序号",name:"名称",description:"描述",value:"值",minValue:"最小值",maxValue:"最大值",step:"步长",address:"地址",unit:"单位",operation:"操作"},search:{namePlaceholder:"输入定值名称进行搜索",descPlaceholder:"输入定值描述进行搜索"}},remoteControl:{title:"遥控",sequence:"序号",shortAddress:"短地址",description:"描述",control:"控分/控合",type:"类型",operation:"操作",directControl:"直控",selectControl:"选控",controlClose:"控分",controlOpen:"控合",noCheck:"不检",syncCheck:"检同期",deadCheck:"检无压",confirmInfo:"确认信息",execute:"执行",cancel:"取消",confirm:"确定",success:"成功",failed:"失败",errorInfo:"错误信息",promptInfo:"提示信息",confirmSuccess:"选择成功，是否执行？",executeSuccess:"执行成功",cancelSuccess:"取消成功",executeFailed:"遥控执行失败，失败原因：",cancelFailed:"遥控取消失败，失败原因：",remoteExecuteSuccess:"遥控执行成功",remoteCancelSuccess:"遥控取消成功"},remoteDrive:{action:"动作",executeSuccess:"执行成功",executeFailed:"执行失败",prompt:"提示信息",error:"错误信息",confirm:"确定",shortAddress:"短地址",description:"描述",operation:"操作",enterToFilter:"输入短地址过滤",enterToFilterDesc:"输入描述过滤",actionSuccess:"动作执行成功",actionFailed:"动作执行失败",failureReason:"失败原因",sequence:"序号"},remoteSignal:{autoRefresh:"自动刷新",refresh:"刷新",export:"导出",sequence:"序号",name:"名称",description:"描述",value:"值",quality:"品质",searchName:"输入名称进行搜索",searchDesc:"输入描述进行搜索",searchValue:"输入值进行搜索",exportTitle:"导出装置遥信信息",exportSuccess:"导出装置遥信信息成功",exportFailed:"导出装置遥信信息失败",exportSuccessWithPath:"导出装置遥信信息成功，",exportFailedWithError:"导出装置遥信信息失败：",invalidData:"无效的数据：",errorInDataCallback:"数据回调处理错误：",errorFetchingData:"获取数据时出错："},remoteTelemetry:{autoRefresh:"自动刷新",refresh:"刷新",export:"导出",sequence:"序号",name:"名称",description:"描述",value:"值",unit:"单位",quality:"品质",searchName:"输入名称进行搜索",searchDesc:"输入描述进行搜索",searchValue:"输入值进行搜索",exportTitle:"导出装置状态量信息",exportSuccess:"导出装置状态量信息成功",exportFailed:"导出装置状态量信息失败",exportSuccessWithPath:"导出装置状态量信息成功，",exportFailedWithError:"导出装置状态量信息失败：",confirm:"确定",tip:"提示",exportFileName:"装置状态量信息",selectPathLog:"选择路径："},remote:{directControl:"直控",selectControl:"选控",serialNumber:"序号",shortAddress:"短地址",description:"描述",value:"值",operation:"操作",inputShortAddressFilter:"输入短地址过滤",inputDescriptionFilter:"输入描述过滤",invalidData:"数据{name}的值{value}不合法",error:"错误",success:"成功",executeSuccess:"执行成功",prompt:"提示信息",confirm:"确定",errorInfo:"错误信息",executeFailed:"遥调执行失败，失败原因：{msg}",executeSuccessLog:"{desc}遥调执行成功",cancelSuccess:"取消成功",cancelFailed:"遥调取消失败，失败原因：{msg}",selectSuccess:"选择成功，是否执行？",confirmInfo:"确认信息",execute:"执行",cancel:"取消"},report:{uploadWave:"召唤录波",searchHistory:"取历史报告",saveResult:"保存结果",clearContent:"清除页面内容",date:"日期",query:"查询",save:"保存",autoRefresh:"自动刷新",stopRefresh:"停止刷新",clearList:"清除列表",progressInfo:"进度信息",loading:"数据加载中",reportNo:"报告编号",time:"时间",description:"描述",noFileToUpload:"无文件可召唤",uploadSuccess:"录波文件召唤完成",uploadPath:"录波文件召唤完成，路径：",noDataToSave:"无数据可保存",saveSuccess:"保存成功",saveReport:"保存报告",openWaveConfirm:"是否通过第三方工具打开波形文件?",confirm:"确定",cancel:"取消",waveToolNotConfigured:"未配置第三方波形工具路径",pleaseSelectTimeRange:"请选择完整的时间范围",querying:"正在查询",reportNumber:"报告编号",operationAddress:"操作地址",operationParams:"操作参数",result:"结果",progress:"进度信息",loadingText:"加载中",selectCompleteTimeRange:"请选择完整的时间范围",fileUploading:"录波文件召唤中",fileUploadComplete:"文件召唤完成"},customMenu:{addMenu:"新建自定义菜单",editMenu:"编辑自定义菜单",deleteMenu:"删除自定义菜单",addReport:"新建自定义报告",editReport:"编辑自定义报告",deleteReport:"删除自定义报告",addPointGroup:"新建自定义组",editPointGroup:"编辑自定义组",deletePointGroup:"删除自定义组",selectedPoints:"已选点",selectFc:"选择FC",selectGroupType:"选择组类型",groupTypes:{ST:"遥信",MX:"遥测",SP:"单区定值",SG:"多区定值"},filterPlaceholder:"按名称/描述过滤",loadingData:"正在加载数据...",noDataForFc:"该FC下暂无数据",noDataForGroupType:"该组类型下暂无数据",pleaseSelectFc:"请先选择FC",pleaseSelectGroupType:"请先选择组类型",loadingGroupTypeData:"正在获取组类型数据...",loadingGroupTypes:"正在加载组类型的数据...",loadedGroupTypes:"已加载组类型",dataLoadComplete:"数据加载完成",dataLoadFailed:"数据加载失败",switchingToGroupType:"正在切换到",loadingGroupTypeDataSingle:"正在加载数据...",loadGroupTypeFailed:"加载数据失败",loadGroupTypeError:"数据时发生错误",inputGroupName:"请输入组名称",inputGroupDesc:"请输入组描述",selectGroupTypeFirst:"请先选择组类型",nameValidationFailed:"名称验证失败，请重试",nameConflictWithSystem:"名称与系统菜单重复，请使用其他名称",nameConflictWithCustom:"名称与现有自定义菜单重复，请使用其他名称",refreshData:"刷新数据",menuName:"组名",menuDesc:"描述",reportName:"报告名称",reportDesc:"描述",reportKeyword:"关键字",reportInherit:"继承报告",inputMenuName:"请输入组名",inputMenuDesc:"请输入描述",inputReportName:"请输入报告名称",inputReportDesc:"请输入描述",inputReportKeyword:"请输入关键字",selectReportInherit:"请选择继承报告",cancel:"取消",confirm:"确定",successAddMenu:"新建自定义菜单成功",successEditMenu:"编辑自定义菜单成功",successDeleteMenu:"删除自定义菜单成功",successAddReport:"新建自定义报告成功",successEditReport:"编辑自定义报告成功",successDeleteReport:"删除自定义报告成功",successDeletePointGroup:"删除自定义组成功",errorAction:"操作失败",errorDelete:"删除失败",confirmDeleteMenu:"确定要删除该自定义菜单吗？",confirmDeleteReport:"确定要删除该自定义报告吗？",confirmDeletePointGroup:"确定要删除该自定义组吗？",tip:"提示"},tree:{inputGroupName:"请输入组名称",expandAll:"全部展开",collapseAll:"全部收缩"}},$e={device:{configure:{selectConfigure:"请选择组态图符文件！",loading:"加载中",operating:"操作中",loadFailed:"加载失败，原因：",getCustomDeviceFailed:"获取自定义设备失败",registerDataFailed:"注册数据失败，原因：",variablesNotExist:"部分变量不存在，：",getDataError:"获取数据错误",remoteSet:"遥设",remoteControlFailed:"遥控失败，原因：",remoteControlSuccess:"遥控成功",noRemoteControlType:"未配置遥控类型",symbolChangeReload:"图符变动从新加载",deviceChangeReload:"设备变动从新加载",deviceConnectReload:"设备连接成功，从新加载"},configureList:{searchPlaceholder:"根据关键词查找",deviceMonitor:"设备监控",newProject:"新建项目",addCustomComponent:"新增自定义组件",newConfigure:"新建组态",renameProject:"重命名项目",deleteProject:"删除项目",editConfigure:"编辑组态",renameConfigure:"重命名组态",deleteConfigure:"删除组态",openFolder:"打开所在文件夹",inputProjectName:"请输入项目名称(10个字符以内，不得出现特殊字符)",inputConfigureName:"请输入组态名称(10个字符以内，不得出现特殊字符)",confirm:"确定",cancel:"取消",invalidName:"名称长度或内容不合法",projectAddSuccess:"项目：{name}新增成功",projectAddFailed:"项目：{name}新增失败，原因：",configureAddSuccess:"组态：{name}新增成功",configureAddFailed:"组态：{name}新增失败，原因：",renameSuccess:"重命名成功",renameFailed:"重命名失败，原因：",confirmDelete:"确定删除吗?",confirmBatchDelete:"该项目下有关联组态内容，确定批量删除吗?",deleteSuccess:"删除成功",deleteFailed:"删除失败，原因：",remoteSet:"删除确认"},configures:{customComponent:"自定义组件",selectDevice:"请选择关联装置！",edit:"编辑："},customConfigure:{getCustomDeviceFailed:"获取自定义设备失败",saveDeviceFailed:"保存设备图符失败",saveSuccess:"保存成功",deleteDeviceFailed:"删除设备图符失败",deleteSuccess:"删除成功",tip:"提示信息"},deviceList:{unnamed:"未命名装置",connect:"连接",disconnect:"断开",edit:"编辑",delete:"删除",notFound:"没有找到设备",editWarn:"请先断开连接再编辑",deleteWarn:"请先断开连接再删除",connectSuccess:"装置{name}：连接成功",connectExist:"装置{name}：连接已存在",connectFailed:"装置{name}：连接失败",connectFailedReason:"装置连接失败原因：{reason}",disconnectSuccess:"装置{name}：已断开",disconnectedNotify:"装置 {name} 连接已断开",currentDisconnectedNotify:"当前装置连接已断开",operateFailed:"装置{name}：操作失败",remove:"删除"},deviceSearch:{searchPlaceholder:"搜索装置"},editConfigure:{saveSuccess:"保存成功",saveFailed:"保存失败，原因：",loadFailed:"加载失败，原因：",getCustomDeviceFailed:"获取自定义设备失败",tip:"提示信息"},remoteSet:{inputValue:"输入值",write:"写入",cancel:"取消",setFailed:"遥设失败，原因：",operateSuccess:"操作成功",noSetType:"未配置遥设类型"}},graph:{component:{electricSymbols:"电气符号",customComponents:"自定义组件",basicComponents:"基础组件"},toolbar:{undo:"撤销",redo:"重做",bringToFront:"置前",sendToBack:"置后",ratio:"等比例缩放",delete:"删除",save:"保存"},contextMenu:{group:"组合",ungroup:"解组",linkData:"关联数据",equipmentSaddr:"设备地址"},dialog:{dataConfig:"数据配置",tip:"提示信息",selectOneGraph:"请选中一个图形"},message:{waitForCanvasInit:"请等待画布初始化完成",loadEquipmentFailed:"加载图符失败",loadEquipmentError:"加载设备失败",equipmentLoaded:"设备加载完成"},basic:{title:"基础组件",components:{line:"线",text:"文本",rectangle:"矩形",circle:"圆形",ellipse:"椭圆",triangle:"三角形",arc:"圆弧"}},selectEquipment:{sequence:"序号",name:"名称",type:"类型",symbol:"图符",operation:"操作",reference:"引用"},setSAddr:{telemetry:"遥信/遥测",format:"格式化",factor:"系数",remoteControl:"遥控",controlType:"遥控方式",controlValue:"遥控值",remoteSet:"遥设",setType:"遥设方式",displayConfig:"显示配置",addRow:"增加行",sequence:"序号",type:"类型",originalValue:"原始值",displayValue:"显示值",operation:"操作",text:"文本",symbol:"图符",selectSymbol:"选择图符",confirm:"确定",cancel:"取消",confirmDelete:"确定删除吗?",tip:"提示信息",selectControl:"选控",directControl:"直控",controlClose:"控合",controlOpen:"控分",cancelDelete:"取消删除"},equipmentType:{CBR:"断路器",DIS:"隔离刀闸",GDIS:"接地刀闸",PTR2:"2卷变",PTR3:"3卷变",VTR:"电压互感器",CTR:"电流互感器",EFN:"中性点接地装置",IFL:"出线",EnergyConsumer:"负荷",GND:"接地",Arrester:"避雷器",Capacitor_P:"并联电容器",Capacitor_S:"串联电容器",Reactor_P:"并联电抗器",Reactor_S:"串联电抗器",Ascoil:"消弧线圈",Fuse:"熔断器",BAT:"电池",BSH:"套管",CAB:"电缆",LIN:"架空线",GEN:"发电机",GIL:"电气绝缘线",RRC:"旋转无功元件",TCF:"晶闸管控制变频器",TCR:"晶闸管控制无功元件",LTC:"分接头",IND:"电感器"},equipmentName:{breaker_vertical:"断路器-竖",breaker_horizontal:"断路器-横",breaker_invalid_vertical:"断路器-无效-竖",breaker_invalid_horizontal:"断路器-无效-横",disconnector_vertical:"刀闸-竖",disconnector_horizontal:"刀闸-横",disconnector_invalid_vertical:"刀闸-无效竖",disconnector_invalid_horizontal:"刀闸-无效横",hv_fuse:"高压熔断器",station_transformer_2w:"站用变（两绕组）",transformer_y_d_11:"变压器（Y/△-11）",transformer_d_y_11:"变压器（△/Y-11）",transformer_d_d:"变压器（△/△）",transformer_y_y_11:"变压器（Y/Y-11）",transformer_y_y_12_d_11:"变压器（Y/Y-12/△-11）",transformer_y_d_11_d_11:"变压器（Y/△-11/△-11）",transformer_y_y_v:"变压器（Y/Y/V）",transformer_autotransformer:"变压器（自耦）",voltage_transformer_2w:"电压互感器（两绕组）",voltage_transformer_3w:"电压互感器（三绕组）",voltage_transformer_4w:"电压互感器（四绕组）",arrester:"避雷器",capacitor_horizontal:"电容-横",capacitor_vertical:"电容-竖",reactor:"电抗",split_reactor:"分裂电抗",power_inductor:"电力电感器",feeder:"出线",ground:"接地",tap_changer:"分接头",connection_point:"连接点",transformer_y_y_12_d_11_new:"变压器(Y/Y-12/△-11)(新)",pt:"PT",arrester_new:"避雷器(新)",disconnector_vertical_new:"刀闸-竖(新)",disconnector_horizontal_new:"刀闸-横(新)",arrester_new_vertical:"避雷器(新)-竖",disconnector_vertical_left_new:"刀闸-竖左(新)"}},graphProperties:{blank:{propertySetting:"属性设置"},graph:{canvasSetting:"画布设置",grid:"网格",backgroundColor:"背景色"},group:{groupProperty:"组合属性",basic:"基本",width:"宽度",height:"高度",x:"位置(X)",y:"位置(Y)",angle:"旋转角度"},node:{nodeProperty:"节点属性",style:"样式",backgroundColor:"背景色",borderWidth:"边框宽度",borderColor:"边框颜色",borderDasharray:"边框样式",rx:"边框rx",ry:"边框ry",position:"位置",width:"宽度",height:"高度",x:"位置(X)",y:"位置(Y)",property:"属性",angle:"旋转角度",zIndex:"层级(z)",fontFamily:"字体",fontColor:"字体颜色",fontSize:"字体大小",text:"文本"},pathLine:{lineSetting:"线设置",style:"样式",lineHeight:"宽度",lineColor:"颜色",borderDasharray:"边框",position:"位置",width:"宽度",height:"高度",x:"位置(X)",y:"位置(Y)",property:"属性",angle:"旋转角度",zIndex:"层级(z)"}},business:{hmi:{title:"画面管理",form:{add:"新增画面",edit:"编辑画面",view:"查看画面",name:"画面名称",type:"画面类型",template:"画面模板",description:"描述",cancel:"取消",confirm:"确定",validation:{name:"请输入画面名称",type:"请选择画面类型",template:"请选择画面模板"}},columns:{name:"画面名称",type:"画面类型",template:"画面模板",createTime:"创建时间",updateTime:"更新时间",status:"状态",operation:"操作"},type:{device:"设备画面",process:"工艺画面",alarm:"告警画面",custom:"自定义画面"},status:{draft:"草稿",published:"已发布",archived:"已归档"},editor:{title:"画面编辑",save:"保存",preview:"预览",publish:"发布",cancel:"取消",tools:{select:"选择",rectangle:"矩形",circle:"圆形",line:"线条",text:"文本",image:"图片",device:"设备",alarm:"告警",chart:"图表"},properties:{title:"属性",position:"位置",size:"大小",style:"样式",data:"数据",event:"事件"}},preview:{title:"画面预览",fullscreen:"全屏",exit:"退出",zoom:{in:"放大",out:"缩小",fit:"适应"}},publish:{title:"发布画面",version:"版本号",description:"发布说明",cancel:"取消",confirm:"确定",validation:{version:"请输入版本号",description:"请输入发布说明"}},template:{title:"画面模板",add:"新增模板",edit:"编辑模板",delete:"删除模板",name:"模板名称",category:"模板分类",description:"描述",preview:"预览",cancel:"取消",confirm:"确定",validation:{name:"请输入模板名称",category:"请选择模板分类"}}}}},Ye={common:{date:"日期",search:"搜索",save:"保存",clear:"清空",loading:"加载中...",reportNo:"报告编号",time:"时间",description:"描述",progress:"进度",selectDateRange:"请选择日期范围",noData:"没有数据",saveSuccess:"保存成功",saveFailed:"保存失败"},date:"日期",search:"搜索",filter:"过滤",save:"保存",clearList:"清除列表",loading:"加载中...",reportNumber:"报告编号",time:"时间",description:"描述",progress:"进度",loadingText:"加载中...",querying:"正在查询",selectCompleteTimeRange:"请选择完整的时间范围",noDataToSave:"无数据可保存",saveSuccess:"保存成功",saveReport:"保存报告",fileUploading:"文件上传中",fileUploadComplete:"文件上传完成",autoRefresh:"自动刷新",showHiddenItems:"显示隐藏条目",hideHiddenItems:"隐藏隐藏条目",name:"名称",operationAddress:"操作地址",operationParams:"操作参数",value:"值",step:"步骤",source:"源",sourceType:"源类型",result:"结果",searchType:"搜索类型",total:"总计 {num} 条",sameSearch:"相同内容搜索",sameFilter:"相同内容过滤",showHideTime:"显示/隐藏时间列",selectRowToOperate:"请先选择需要操作的行",trip:{autoRefresh:"自动刷新"},operate:{name:"名称",operateAddress:"操作地址",operateParam:"操作参数",value:"值",step:"步骤",source:"来源",sourceType:"来源类型",result:"结果"},group:{uploadWave:"上传波形",searchHistory:"搜索历史",saveResult:"保存结果",clearContent:"清空内容",contextMenu:{uploadWave:"上传波形",getHistoryReport:"获取历史报告",saveResult:"保存结果",clearContent:"清空内容"},date:"日期",search:"搜索",save:"保存",clearList:"清除列表",loading:"加载中...",table:{reportId:"报告编号",time:"时间",description:"描述"},progress:{title:"进度",searching:"正在查询 {type}",loading:"加载中..."},refresh:{start:"开始刷新",stop:"停止刷新"},hiddenItems:{show:"显示隐藏条目"},messages:{noFileToUpload:"没有可上传的文件",selectDateRange:"请选择日期范围",noDataToSave:"无数据可保存",saveReport:"保存报告",saveSuccess:"保存成功"}},exporting:"正在导出...",stopRefresh:"停止刷新",searchProgress:"正在查询 {type}",exportLogSuccess:"导出成功：{path}",exportLogFailed:"导出失败：{msg}",exportLogCancelled:"用户取消导出操作",entryID:"报告编号",module:"模块",msg:"消息",level:"级别",type:"类型",origin:"来源",user:"用户",pleaseSelectSavePath:"请选择保存路径...",items:"条"},Ke={device:He,hmi:$e,report:Ye},Je={common:{add:"新增",index:"序号",delete:"删除",clear:"清空",import:"导入",export:"导出",execute:"执行",moveUp:"上移",moveDown:"下移",loading:"加载中...",success:"成功",failed:"失败",confirm:"确定",cancel:"取消",yes:"是",no:"否",operation:"操作",tips:"提示",title:"提示"},search:{placeholder:"搜索功能"},functionList:{unnamedDevice:"未命名装置",batchDownload:{name:"批量下载",desc:"多装置文件批量下载与定值批量导入"},xmlFormatter:{name:"XML格式化",desc:"快速实现XML数据层级化整理"},jsonFormatter:{name:"JSON格式化",desc:"JSON 数据智能格式化，支持语法校验"},radixConverter:{name:"进制转换",desc:"支持二进制、十进制、十六进制等多进制数值互转"},temperatureConverter:{name:"温度转换",desc:"摄氏度、华氏度、开尔文等多种温度单位智能换算"},encryption:{name:"文本加解密",desc:"基于 AES、RSA、Base64 等算法的文本快速加密与解密"},packageProgram:{name:"程序打包",desc:"将装置运行程序文件打包导出，支持自定义保存目录和一键定位文件夹"}},matrixContent:{loading:"加载中...",tabs:{deviceList:"装置列表",downloadConfig:"下载配置",paramConfig:"定值配置"}},taskSteps:{connect:"连接",download:"下载",import:"导入",disconnect:"断开",complete:"完成"},messages:{connectDevice:"连接装置",executeFileDownload:"执行文件下载",downloadingFile:"下载文件中",downloadFileFailed:"下载文件失败",fileDownloadCompleted:"文件下载执行完成",executeParamImport:"执行定值导入",paramValidationFailed:"定值格式校验失败",paramImportFailed:"定值导入失败",paramImportCompleted:"定值导入执行完成",taskCompleted:"任务执行完成",deviceConnectionFailed:"装置连接失败",deviceRebootSuccess:"重启装置成功"},deviceList:{title:"装置列表",deviceListExcel:"装置列表.xlsx",exportDeviceList:"导出装置列表",importDeviceList:"导入装置列表",exportSuccess:"导出装置列表成功",exportFail:"导出装置列表失败",importSuccess:"导入装置列表成功",importFail:"导入装置列表失败",exportSuccessMsg:"导出装置列表成功",exportFailMsg:"导出装置列表失败",importSuccessMsg:"导入装置列表成功",importFailMsg:"导入装置列表失败: {msg}",deviceName:"装置名称",deviceAddress:"装置地址",devicePort:"装置端口",isEncrypted:"是否加密",encrypted:"已加密",notEncrypted:"未加密",status:"状态",operation:"操作",reboot:"重启",noReboot:"不重启",addDevice:"新增装置",deleteDevice:"删除装置",clearDevices:"清空装置",deviceExists:"该装置已存在",deviceDeleted:"装置{ip}已删除",downloadFile:"是否下载文件？",importParam:"是否导入参数？",connectTimeout:"连接超时时间",paramTimeout:"参数修改超时时间",readTimeout:"全局请求超时时间",progress:"进度"},downList:{title:"下载文件配置",deviceDirectory:"装置目录",fileName:"文件名称",fileSize:"文件大小",filePath:"文件路径",lastModified:"最后修改时间",addFile:"添加待下载的文件",addFolder:"添加待下载的文件夹",fileExists:"文件{path}已存在，添加失败！",fileDeleted:"文件{path}已删除",filesDeleted:"文件已删除",defaultExportFileName:"下载文件列表.xlsx",exportTitle:"导出下载文件列表",importTitle:"导入下载文件列表",exportSuccess:"文件列表导出成功",exportFailed:"文件列表导出失败",importSuccess:"文件列表导入成功",importFailed:"文件列表导入失败",fileExistsMsg:"文件{path}已存在，添加失败！"},paramList:{title:"参数定值配置",paramGroup:"定值组",groupName:"组名",paramName:"名称",paramDesc:"描述",paramValue:"值",minValue:"最小值",maxValue:"最大值",step:"步长",unit:"单位",searchParamName:"参数名",searchParamDesc:"参数描述",importSuccess:"参数定值导入成功",importFailed:"参数定值导入失败",exportSuccess:"参数定值导出成功",exportFailed:"参数定值导出失败",clearSuccess:"定值列表清空成功"},progressDialog:{title:"处理中",pleaseWait:"请稍候..."},packageProgram:{saveDir:"保存目录",selectSaveDir:"选择保存目录",packageBtn:"打包",locateDir:"定位文件夹",delete:"删除",sequence:"序号",fileName:"文件名称",fileSize:"文件大小",filePath:"文件路径",lastModified:"最后修改时间",operation:"操作",saveDirEmpty:"请先选择保存目录！",packageSuccess:"程序打包完成！",tip:"提示",confirmButton:"确定",defaultExportFileName:"程序打包文件清单.xlsx",exportTitle:"导出程序打包文件清单",importTitle:"导入程序打包文件清单",exportSuccess:"文件清单导出成功",exportFailed:"文件清单导出失败",importSuccess:"文件清单导入成功",importFailed:"文件清单导入失败",fileExists:"文件{path}已存在，添加失败！",selectDirSuccess:"选择目录成功：{dir}",locateDirSuccess:"定位目录成功：{dir}",addFileStart:"打开文件选择器...",addFileSuccess:"成功添加 {count} 个文件/文件夹",addFileNone:"未添加任何新文件/文件夹",deleteSuccess:"成功删除 {count} 个文件/文件夹",clearSuccess:"已清空所有文件/文件夹",moveUpSuccess:"上移：{name}",moveDownSuccess:"下移：{name}",noFileSelected:"请先勾选需要打包的文件！",noDeviceSelected:"请先选择需要打包的装置！",packageFailed:"打包失败：{msg}",zipPath:"打包路径: {zipPath}",openFileButton:"打开文件"}},Xe={search:{placeholder:"搜索设备",button:"搜索",success:"搜索成功"},device2:{search:{placeholder:"搜索装置",add:"添加装置",duplicate:"该IP和端口已存在，请勿重复添加"},list:{empty:"没有找到设备",unnamed:"未命名装置",status:{connected:"连接",disconnected:"断开"},contextMenu:{connect:"连接",edit:"编辑",disconnect:"断开",remove:"删除"},message:{disconnectFirst:"请先断开连接再编辑",disconnectFirstDelete:"请先断开连接再删除",connectSuccess:"装置{name}：连接成功",connectExists:"装置{name}：连接已存在",connectFailed:"装置{name}：连接失败",connectFailedReason:"装置连接失败原因：{reason}",disconnected:"装置{name}：已断开",operationFailed:"装置{name}：操作失败"}},report:{group:{openWaveConfirm:"是否通过第三方工具打开波形文件?",tips:"温馨提示",noWaveTool:"未配置第三方波形工具路径"},common:{selectRow:"请选中要操作的行"}},backup:{savePath:"保存路径",setPath:"请设置备份路径",setPathTitle:"设置备份路径",locateFolder:"定位文件夹",startBackup:"开始备份",cancelBackup:"取消备份",backup:"备份",sequence:"序号",backupType:"备份类型",backupDesc:"备份说明",progress:"进度",status:"状态",noTypeSelected:"请选择备份类型",backupSuccess:"备份成功",backupFailed:"备份失败",openFolderFailed:"打开文件夹失败",backupTypes:{paramValue:"参数值",faultInfo:"故障信息",cidConfigPrjLog:"CID配置工程日志",waveReport:"波形报告"},backupDescTypes:{paramValue:"备份装置的所有参数设定值",faultInfo:"备份装置的故障录波信息",cidConfigPrjLog:"备份CID配置文件和工程日志",waveReport:"备份波形分析报告文件"},backupStatus:{userCancelled:"用户取消",transferring:"传输中"},console:{pathNotSet:"未设置备份路径，无法开始备份",noTypeSelected:"未选择备份类型，无法开始备份",startBackup:"开始备份，类型：{types}, 路径：{path}",backupException:"备份异常：{error}",pathSelected:"已选择备份路径：{path}",pathNotSelected:"未选择备份路径",pathNotSetForLocate:"未设置备份路径，无法定位文件夹",folderOpened:"已打开备份文件夹：{path}",openFolderFailed:"打开备份文件夹失败：{error}",taskCompleted:"任务处理完成",taskCancelled:"任务已取消",typeError:"类型[{type}]发生错误：{error}",typeCompleted:"类型[{type}]备份完成",typeCancelled:"类型[{type}]已取消",typeFailed:"类型[{type}]失败"}},remoteControl:{directControl:"直控",selectControl:"选控"},messageMonitor:{title:"报文监视",start:"开始监视",stop:"停止监视",clear:"清空",export:"导出",expand:"展开",collapse:"折叠",close:"关闭",messageType:"报文",noMessages:"暂无报文数据",noMessagesToExport:"没有可导出的报文数据",startSuccess:"开始监视报文",stopSuccess:"停止监视报文",clearSuccess:"清空报文成功",exportSuccess:"导出报文成功",exportFailed:"导出报文失败",toggleFailed:"切换监视状态失败"}}},Qe={search:{placeholder:"根据关键词查找"},categories:{title:"📦IT工具",formatting:"📝格式化工具",xml:"🟡xml格式化",json:"🟡json格式化",conversion:"🔄转换工具",radix:"🟢进制转换",temperature:"🟢温度转换",encryption:"🔑加解密工具",textEncryption:"🔵文本加解密"},encryption:{title:"文本加解密",description:"使用加密算法（如AES、TripleDES、Rabbit或RC4）加密和解密文本明文",encrypt:"加密",inputText:"待加密文本:",inputPlaceholder:"请输入需要加密的文本内容...",key:"密钥:",keyPlaceholder:"请输入加密密钥",algorithm:"加密算法:",outputText:"加密后文本:",outputPlaceholder:"加密结果将显示在这里...",decrypt:"解密",decryptInputText:"待解密文本:",decryptInputPlaceholder:"请输入需要解密的密文...",decryptKey:"密钥:",decryptAlgorithm:"解密算法:",decryptOutputText:"解密后文本:",decryptError:"无法解密文本"},json:{title:"JSON格式化",description:"将JSON字符串修饰为友好的可读格式",sortKeys:"字段排序",indentSize:"缩进尺寸",inputLabel:"待格式化JSON",inputPlaceholder:"请粘贴你的JSON...",outputLabel:"格式化后JSON",invalid:"该文档不符合JSON规范，请检查"},xml:{title:"XML格式化",description:"将XML字符串修饰为友好的可读格式",collapseContent:"折叠内容 :",indentSize:"缩进尺寸:",inputLabel:"待格式化XML",inputPlaceholder:"请粘贴你的XML...",outputLabel:"格式化后XML",invalid:"该文档不符合XML规范，请检查"},temperature:{title:"温度转换",description:"开尔文、摄氏度、华氏度、兰金、德莱尔、牛顿、雷奥穆尔和罗默温度度数转换",kelvin:"开尔文",kelvinUnit:"K",celsius:"摄氏度",celsiusUnit:"°C",fahrenheit:"华氏度",fahrenheitUnit:"°F",rankine:"兰金",rankineUnit:"°R",delisle:"德莱尔",delisleUnit:"°De",newton:"牛顿",newtonUnit:"°N",reaumur:"雷奥穆尔",reaumurUnit:"°Ré",romer:"罗默",romerUnit:"°Rø"},radix:{title:"进制转换",description:"在不同的进制（十进制、十六进制、二进制、八进制、base64…）之间转换数字",inputLabel:"待转换数字",inputPlaceholder:"请输入数字(如: 100)",outputLabel:"转换结果",binary:"2进制(2)",binaryPlaceholder:"二进制结果...",octal:"8进制(8)",octalPlaceholder:"八进制结果...",decimal:"10进制(10)",decimalPlaceholder:"十进制结果...",hex:"16进制(16)",hexPlaceholder:"十六进制结果...",base64:"Base64(64)",base64Placeholder:"Base64结果...",customBase:"自定义进制",customBasePlaceholder:"Base {{base}} 结果..."},jsonViewer:{title:"JSON格式化",description:"将JSON字符串修饰为友好的可读格式",sortKeys:"字段排序",indentSize:"缩进尺寸",inputJson:"待格式化JSON",formattedJson:"格式化后JSON",placeholder:"请粘贴你的JSON...",validationError:"该文档不符合JSON规范.请检查"}},Ze={matrix:Je,debug:Xe,tools:Qe},et={checkCard:{default:"默认"},chooseModule:{title:"选择应用",noModule:"未找到模块!",setDefault:"设为默认",cancel:"取消",confirm:"确定"},closer:{title:"退出确认",message:"是否要退出？",confirm:"确 定",minimize:"最小化到托盘",cancel:"取 消"},codeHighLight:{noCode:"无"},cropUpload:{title:"图片裁剪",zoomIn:"放大",zoomOut:"缩小",rotateLeft:"向左旋转",rotateRight:"向右旋转",uploadImage:"点击上传图片",uploadTip:"请上传图片文件，建议不超过2M",cancel:"取消",confirm:"确认"},error:{forbidden:"抱歉，您无权访问该页面~🙅‍♂️🙅‍♀️",notFound:"抱歉，您访问的页面不存在~🤷‍♂️🤷‍♀️",serverError:"抱歉，您的网络不见了~🤦‍♂️🤦‍♀️",back:"返回上一页"},form:{input:{placeholder:"请填写{label}"},select:{placeholder:"请选择{label}"},button:{add:"新增",edit:"编辑",delete:"删除",view:"查看"},search:{inputPlaceholder:"请输入",selectPlaceholder:"请选择",rangeSeparator:"至",startPlaceholder:"开始时间",endPlaceholder:"结束时间"}},selectIcon:{title:"图标选择",placeholder:"请选择图标",searchPlaceholder:"搜索图标",noSearchResult:"未搜索到您要找的图标~",moreIcons:"更多图标",enterIconifyCode:"请输入您想要的图标iconify代码,如mdi:home-variant",iconifyAddress:"iconify地址",localIcons:"本地图标"},selector:{add:"添加",addCurrent:"添加当前",addSelected:"添加选中",delete:"删除",deleteCurrent:"删除当前",deleteSelected:"删除选中",cancel:"取消",confirm:"确定",selected:"已选择",maxSelect:"最多选择",singleSelectOnly:"只可选择一条",maxSelectLimit:"最多选择{count}条",person:"人"},upload:{view:"查看",edit:"编辑",delete:"删除",uploadImage:"请上传图片",uploadSuccess:"图片上传成功！",uploadFailed:"图片上传失败，请您重新上传！",invalidFormat:"上传图片不符合所需的格式！",fileSizeExceeded:"上传图片大小不能超过 {size}M！",maxFilesExceeded:"当前最多只能上传 {limit} 张图片，请移除后上传！",fileSizeZero:"文件 {fileName} 大小为0，无法上传！",tips:"温馨提示"},treeFilter:{searchPlaceholder:"输入关键字进行过滤",expandAll:"展开全部",collapseAll:"折叠全部",all:"全部"},proTable:{search:{reset:"重置",search:"搜索",expand:"展开",collapse:"收起"},pagination:{total:"共 {total} 条",pageSize:"条/页",goto:"前往",page:"页"},colSetting:{title:"列设置",fixedLeft:"是否显示",fixedRight:"是否排序",cancelFixed:"取消固定",reset:"恢复默认",confirm:"确定",cancel:"取消"},table:{empty:"暂无数据"}},basicComponent:{title:"基础组件",line:"线",text:"文本",rect:"矩形",circle:"圆形",ellipse:"椭圆",triangle:"三角形",arc:"圆弧"}},tt={equipmentList:{sequence:"序号",name:"名称",type:"类型",operation:"操作",preview:"预览",copy:"复制",delete:"删除",confirmDelete:"确定删除吗?",tip:"提示信息",error:"错误"},graphComponent:{deviceType:"设备类型",deviceName:"设备名称",save:"保存"},contextMenu:{group:"组合",ungroup:"解组",setStatus:"设置状态",copy:"复制",delete:"删除",rename:"重命名"},graphCreate:{needTwoDevices:"开关或刀闸需要选中两个设备图形",needCorrectStatus:"请为开关或刀闸设置正确的状态属性",needOneDevice:"请选中一个设备图形"},graphDefine:{waitCanvasInit:"请等待画布初始化完成",selectOneGraph:"请选择一个图符",tip:"提示"},setStatus:{open:"打开",close:"闭合",none:"无"},graphTools:{undo:"撤销",redo:"重做",front:"置前",back:"置后",delete:"删除",save:"保存",equipmentList:"设备列表"},graphEditor:{dataConfig:"数据配置",loadEquipmentFailed:"加载图符失败"}},at={more:{importPathNotExists:"导入路径不存在",exportPathNotExists:"导出路径不存在",selectCorrectConfigFile:"请选择正确的配置文件",exportProjectConfigException:"导出工程配置异常",importProjectConfigException:"导入工程配置异常"}},rt={...Me,...je,...Ke,...Ze,components:et,graphDefine:tt,services:at,Home:"首页"},ot={language:{title:"Language",zh:"Simplified Chinese",en:"English",es:"Spanish",fr:"French",ru:"Russian",tooltip:"Multilingual"},about:{title:"About",introduction:"Introduction",description:"A new generation of high-performance platform debugging tool developed with the latest technology stack such as Vue3, TypeScript, Vite4, Pinia, Element-Plus, Electron, etc.",versionInfo:"Version Info",toolName:"Tool Name",version:"Version",machineCode:"Machine Code",loading:"Loading...",machineCodeError:"Failed to get",copySuccess:"Machine code copied to clipboard",copyError:"Copy failed",versionFeatures:"Version Features",features:{visualTool:"Includes visual tool connection, device information viewing, settings, analog quantity, status quantity, remote signaling, remote measurement, remote control, report, device time synchronization, setting import/export, variable debugging functions",configTool:"Includes configuration tool preview, add, edit, custom symbol, associate device information functions",themeTool:"Includes theme customization, IT gadgets, device configuration import/export functions"}},footer:{copyright:"{version}"},header:{minimize:"Minimize",maximize:"Maximize",restore:"Restore",close:"Close",company:{name:"Sieyuan Electric",englishName:"Sieyuan"},collapse:{expand:"Expand Device",fold:"Collapse Device",expandTool:"Expand Tool List",foldTool:"Collapse Tool List"},breadcrumb:{home:"Home"},assemblySize:{title:"Size Setting",default:"Default",large:"Large",small:"Small"},avatar:{profile:"Profile",switchApp:"Switch App",logout:"Logout",logoutConfirm:{title:"Reminder",message:"Are you sure you want to logout?",confirm:"Confirm",cancel:"Cancel"},logoutSuccess:"Logout successful!"},changeModule:{title:"Switch Module"},enginConfig:{configType:"Config Type",openDirectory:"Open File Directory",cancel:"Cancel",confirm:"Confirm",all:"All",deviceList:"Device List",configureList:"Configuration List",exportSuccess:"Export configuration successful",importSuccess:"Import configuration successful",disconnectDeviceFirst:"Please disconnect the connected device first",overrideConfirm:"Configuration list already exists, overwrite?",warmTips:"Reminder",importConfigFile:"Import configuration file"},userInfo:{title:"User Info",cancel:"Cancel",confirm:"Confirm"},password:{title:"Change Password",cancel:"Cancel",confirm:"Confirm"},globalSetting:{title:"Settings",tooltip:"Settings"},moreInfo:{title:"More",tooltip:"More",items:{importConfig:"Import Config",printScreen:"Screenshot",search:"Search Menu",exportConfig:"Export Config",about:"About",help:"Help"},importConfig:{title:"Import Project Config",placeholder:"Please select the configuration file path to import"},exportConfig:{title:"Export Project Config",placeholder:"Please select the export directory"}},searchMenu:{placeholder:"Menu search: supports menu name, path",empty:"No menu"},theme:{title:"Theme",tooltip:"Theme"}},main:{maximize:{exit:"Exit Maximize"}},theme:{title:"Layout Settings",quickTheme:{title:"Theme Settings"},layoutSettings:{title:"Layout Settings"},layout:{title:"Layout Style",columns:"Columns",classic:"Classic",transverse:"Transverse",vertical:"Vertical"},global:{title:"Global Theme",primary:"Theme Color",dark:"Dark Mode",grey:"Grey Mode",weak:"Weak Mode",special:"Special Mode"},mode:{light:"Light",dark:"Dark"},interface:{title:"Interface Settings",watermark:"Watermark",breadcrumb:"Breadcrumb",breadcrumbIcon:"Breadcrumb Icon",tabs:"Tabs Bar",tabsIcon:"Tabs Bar Icon",footer:"Footer",drawerForm:"Drawer Form"},presetThemes:{title:"Preset Themes",default:{name:"Default Theme",description:"Classic blue theme"},dark:{name:"Dark Theme",description:"Eye protection dark mode"},techBlue:{name:"Tech Blue",description:"Modern tech blue"},deepBlue:{name:"Deep Blue",description:"Deep ocean blue"},nature:{name:"Nature Theme",description:"Fresh green"},forestGreen:{name:"Forest Green",description:"Deep forest green"},warm:{name:"Warm Theme",description:"Warm orange"},sunsetOrange:{name:"Sunset Orange",description:"Warm sunset orange"},elegant:{name:"Elegant Theme",description:"Noble purple"},lavender:{name:"Lavender",description:"Soft lavender purple"},sakura:{name:"Sakura Pink",description:"Romantic sakura pink"},rose:{name:"Rose Red",description:"Passionate rose red"},lime:{name:"Lime Green",description:"Vibrant lime green"},skyBlue:{name:"Sky Blue",description:"Clear sky blue"},eyeCare:{name:"Eye Care Mode",description:"Grey eye care theme"}},colors:{techBlue:{name:"Tech Blue",description:"Modern tech feel"},natureGreen:{name:"Nature Green",description:"Fresh and natural"},vibrantOrange:{name:"Vibrant Orange",description:"Warm and vibrant"},elegantPurple:{name:"Elegant Purple",description:"Noble and elegant"},romanticPink:{name:"Romantic Pink",description:"Gentle and romantic"},freshCyan:{name:"Fresh Cyan",description:"Fresh and elegant"},brightYellow:{name:"Bright Yellow",description:"Bright and lively"},warmOrange:{name:"Warm Orange",description:"Warm and comfortable"},limeGreen:{name:"Lime Green",description:"Fresh lime"},deepBlue:{name:"Deep Blue",description:"Deep and steady"},golden:{name:"Golden",description:"Classic gold"},chinaRed:{name:"China Red",description:"Traditional red"}}},tabs:{moreButton:{refresh:"Refresh",closeCurrent:"Close Current",closeLeft:"Close Left",closeRight:"Close Right",closeOthers:"Close Others",closeAll:"Close All"}}},it={confirm:"Confirm",cancel:"Cancel",save:"Save",delete:"Delete",remove:"Remove",edit:"Edit",add:"Add",search:"Search",reset:"Reset",export:"Export",import:"Import",upload:"Upload",download:"Download",preview:"Preview",print:"Print",refresh:"Refresh",back:"Back",next:"Next",submit:"Submit",loading:"Loading...",success:"Success",error:"Error",warning:"Warning",info:"Info",index:"Index",title:"Title",operation:"Operation",execute:"Execute",clear:"Clear",moveUp:"Move Up",moveDown:"Move Down",status:{active:"Active",inactive:"Inactive",enabled:"Enabled",disabled:"Disabled",online:"Online",offline:"Offline",pending:"Pending",completed:"Completed",failed:"Failed"},time:{today:"Today",yesterday:"Yesterday",thisWeek:"This Week",lastWeek:"Last Week",thisMonth:"This Month",lastMonth:"Last Month",custom:"Custom Range"},pagination:{total:"Total",items:"Items",page:"Page",perPage:"Per Page",showing:"Showing",to:"To",of:"Of"},validation:{required:"This field is required",email:"Please enter a valid email address",phone:"Please enter a valid phone number",number:"Please enter a valid number",integer:"Please enter a valid integer",min:"Minimum value is {min}",max:"Maximum value is {max}",length:"Length must be {length}",minLength:"Minimum length is {min}",maxLength:"Maximum length is {max}"},message:{saveSuccess:"Save successful",deleteSuccess:"Delete successful",updateSuccess:"Update successful",operationSuccess:"Operation successful",operationFailed:"Operation failed",confirmDelete:"Are you sure to delete?",noData:"No data",loading:"Loading...",networkError:"Network error, please try again",copySuccess:"Copy successful"},languageSyncWarning:"Language sync to backend failed, but frontend language changed successfully",customFileSelector:{title:"Select Files and Folders",searchPlaceholder:"Search files or folders...",selectedItems:"Selected Items",clearAll:"Clear All",noItemsSelected:"No items selected",cancel:"Cancel",confirm:"Confirm",loading:"Loading...",error:{loadFailed:"Load failed",accessDenied:"Access denied",notFound:"Path not found"}},test:{languageSwitch:{title:"Language Switch Test",progressDialog:"Progress Dialog Test",showProgress:"Show Progress Dialog",temperatureConverter:"Temperature Converter Test",temperatureDesc:"The following temperature unit names should automatically update after language switch:",reportNames:"Report Names Test",reportDesc:"The following report-related names should automatically update after language switch:",autoRefresh:"Auto Refresh",showHidden:"Show Hidden Items",instructions:"Test Instructions",step1:"Click the language switch button in the top right corner",step2:"Select a different language (such as English, Spanish, French)",step3:"Observe whether the text on the page immediately updates to the new language"},errorPageTest:{title:"Error Page Button Test",description:"Test the display effect of error page buttons in different language environments",languageSelector:"Select Language",buttonPreview:"Button Preview",errorPage:"Error Page",textLength:"Text Length",testLinks:"Test Links",goto404:"Go to 404 Page",goto403:"Go to 403 Page",goto500:"Go to 500 Page",comparisonTable:"Multi-language Comparison Table",language:"Language",buttonText:"Button Text",preview:"Preview"}}},nt={loading:{checking:"Checking authorization...",loading:"Loading..."},auth:{invalid:"Invalid authorization: {msg}",unknownError:"Unknown error",checkFailed:"Authorization check failed, please check network connection"}},st={layout:ot,common:it,app:nt},lt={dataScope:{title:"Data Scope Selector",selectOrg:"Select Organization",orgList:"Organization List",cancel:"Cancel",confirm:"Confirm"},grantResource:{title:"Grant Resource",warning:"Non-super admin roles cannot be granted system module menu resources",firstLevel:"First Level Directory",menu:"Menu",buttonAuth:"Button Authorization",cancel:"Cancel",confirm:"Confirm",selectDataScope:"Please select data scope"}},ct={dashboard:"Dashboard",system:"System Management",user:"User Management",role:"Role Management",menu:"Menu Management",changeModule:{title:"Change Module->",belongModule:"Belonging Module",requiredModule:"Please select belonging module"},debug:{title:"Debug",description:"Debug"},configure:{title:"Config",description:"Config"},tool:{title:"Tools",description:"Tools"},sysConfig:{title:"System",description:"System"}},dt={limit:{module:{title:"Module Name",icon:"Icon",status:"Status",sort:"Sort",description:"Description",createTime:"Created Time",operation:"Operation",add:"Add Module",edit:"Edit Module",delete:"Delete Module",deleteConfirm:"Delete selected modules",deleteConfirmWithName:"Delete module [{name}]",form:{title:"Please enter module name",status:"Please select status",sort:"Please enter sort order",icon:"Please select icon"}},menu:{title:"Menu Name",icon:"Menu Icon",type:"Menu Type",component:"Component Name",path:"Route Path",componentPath:"Component Path",sort:"Sort",status:"Status",description:"Description",operation:"Operation",add:"Add Menu",edit:"Edit Menu",delete:"Delete Menu",deleteConfirm:"Delete selected menus",deleteConfirmWithName:"Delete menu [{name}]",form:{title:"Please enter menu name",parent:"Please select parent menu",type:"Please select menu type",path:"Please enter route path",component:"Please enter component path",sort:"Please enter sort order",icon:"Please select icon",status:"Please select status",link:"Please enter link address"}},button:{title:"Button Name",code:"Button Code",sort:"Sort",description:"Description",operation:"Operation",add:"Add Button",edit:"Edit Button",delete:"Delete Button",deleteConfirm:"Delete selected buttons",deleteConfirmWithName:"Delete button [{name}]",batch:{title:"Batch Add Buttons",shortName:"Permission Short Name",codePrefix:"Code Prefix",form:{shortName:"Please enter permission short name",codePrefix:"Please enter code prefix"}},form:{title:"Please enter button name",code:"Please enter button code",sort:"Please enter sort order"}},role:{title:"Role Name",org:"Organization",category:"Role Type",status:"Status",sort:"Sort",description:"Description",createTime:"Created Time",operation:"Operation",add:"Add Role",edit:"Edit Role",delete:"Delete Role",deleteConfirm:"Delete selected roles",deleteConfirmWithName:"Delete role [{name}]",grant:{resource:"Grant Resource",permission:"Grant Permission",dataScope:"Data Scope"},form:{title:"Please enter role name",org:"Please select organization",category:"Please select role type",status:"Please select status"}},spa:{title:"SPA Name",icon:"Icon",type:"SPA Type",path:"Route Path",component:"Component Path",sort:"Sort",description:"Description",createTime:"Created Time",operation:"Operation",add:"Add SPA",edit:"Edit SPA",delete:"Delete SPA",deleteConfirm:"Delete selected SPAs",deleteConfirmWithName:"Delete SPA [{name}]",form:{title:"Please enter SPA name",type:"Please select SPA type",path:"Please enter route path",component:"Please enter component path",sort:"Please enter sort order",icon:"Please select icon",link:"Please enter link address, e.g.: http://www.baidu.com"}}}},pt={config:{title:"System Configuration",paramTitle:"Parameter Configuration",systemName:"System Name",systemVersion:"System Version",waveToolPath:"Third-party Waveform Tool Path",waveToolPathPlaceholder:"Please select third-party waveform tool path",openDirectory:"Open File Directory",save:"Save",reset:"Reset",saveSuccess:"Save successful",selectWaveTool:"Select Waveform Analysis Tool",paramRefreshTime:"Setting Refresh Interval (ms)",reportRefreshTime:"Report Refresh Interval (ms)",stateRefreshTime:"Status Refresh Interval (ms)",variRefreshTime:"Variable Refresh Interval (ms)",configTitle:"Configuration",configKey:"Config Key",configValue:"Config Value",remark:"Remark",sortCode:"Sort",operation:"Operation",deleteConfirm:"Delete configuration [{key}]"}},ut={machineCode:"Machine Code",activationCode:"Activation Code",activationCodePlaceholder:"Please enter activation code",reset:"Reset",activate:"Activate",success:{title:"Activation Successful",message:"System activated successfully"},error:{unknown:"Activation failed: {msg}",network:"Activation failed, please check network connection"}},mt={title:"SPA Management",list:{title:"SPA List",add:"Add SPA",deleteSelected:"Delete Selected",deleteConfirm:"Are you sure you want to delete SPA {title}?"},form:{title:"{opt} SPA",basicSettings:"Basic Settings",functionSettings:"Function Settings",name:"SPA Name",type:"SPA Type",icon:"Icon",path:"Route Path",pathPlaceholder:"Please enter route path, e.g.: /home/<USER>",componentName:"Component Name",componentPath:"Component Path",linkPath:"Link Address",linkPathPlaceholder:"Please enter link address, e.g.: http://www.baidu.com",sort:"Sort",description:"Description",isHome:"Set as Home Page",isHide:"Hide Page",isFull:"Full Screen Page",isAffix:"Pin Tab",isKeepAlive:"Route Cache",cancel:"Cancel",confirm:"Confirm",nameRequired:"Please enter SPA name",typeRequired:"Please select SPA type",pathRequired:"Please enter route path",componentNameRequired:"Please enter component name",componentPathRequired:"Please enter component path",sortRequired:"Please enter sort order",iconRequired:"Please select icon"}},gt={title:"System Login",account:{title:"Account Login",username:"Please enter username",password:"Please enter password",captcha:"Please enter captcha",tenant:"Please select tenant"},phone:{title:"Phone Login",phone:"Please enter phone number",smsCode:"Please enter SMS code",getCode:"Get Code",machineVerify:"Machine Verification",captcha:"Please enter captcha",sendSuccess:"Verification code sent successfully",sendFailed:"Failed to send SMS verification code"},button:{reset:"Reset",login:"Login"},dialog:{cancel:"Cancel",confirm:"Confirm"}},ft={title:"Help Center",subtitle:"Help & Documentation",catalog:"Catalog",searchPlaceholder:"Search help content...",loadFail:"Failed to load help document, please try again later."},ht={role:lt,menu:ct,limit:dt,sys:pt,activate:ut,spa:mt,login:gt,help:ft},vt={configure:{remoteSet:"Remote Set"},console:{title:"Console",clear:"Clear",selectAll:"Select All",copy:"Copy",copySuccess:"Copy successful",noTextSelected:"No text selected",copyFailed:"Copy failed",clearSuccess:"Console cleared",collapse:"Collapse",expand:"Expand"},groupInfo:{title:"Group Info",table:{id:"Index",name:"Name",desc:"Description",fc:"FC",count:"Count"},messages:{fetchDataError:"Error occurred while fetching data",fetchedData:"Fetched data:"}},treeClickLog:"treeClick clicked : ",contentView:"Content View",emptyDeviceId:"Current device id is empty",invalidResponseStructure:"Invalid response structure",formattedMenuDataLog:"Formatted menu data ===",allSettings:"All Settings",allEditSpSettings:"All Single Area Settings",allEditSgSettings:"All Multi Area Settings",deviceTreeDataLog:"Device tree data",failedToLoadMenu:"Failed to load device menu:",innerTabs:{contentView:"Content",fileUpload:"Upload",fileDownload:"Download",deviceTime:"Time Sync",deviceOperate:"Operation",variableDebug:"Debug",oneClickBackup:"Backup",entryConfig:"Config",tabClickLog:"Tab clicked:"},devices:{notConnectedAlt:"Device not connected",pleaseConnect:"Please connect the device first!"},list:{unnamedDevice:"Unnamed Device",connected:"Connected",disconnected:"Disconnected",connect:"Connect",edit:"Edit",disconnect:"Disconnect",remove:"Delete",noDeviceFound:"No device found",handleClickLog:"handleListClick clicked:",disconnectBeforeEdit:"Please disconnect before editing",connectSuccess:"Device {name}: Connected successfully",connectExist:"Device {name}: Connection already exists",connectFailed:"Device {name}: Connection failed",connectFailedReason:"Device connection failed reason:",disconnectedSuccess:"Device {name}: Disconnected",disconnectedNotify:"Device {name} connection disconnected",currentDisconnectedNotify:"Current device connection disconnected",operateFailed:"Device {name}: Operation failed",disconnectBeforeDelete:"Please disconnect before deleting",dataLog:"Data:",ipPortExist:"This IP and port already exist, please do not add again",messageMonitor:"Message Monitor",connectFirst:"Please connect device first",messageMonitorOpened:"Device {name}: Message monitor opened"},messageMonitor:{title:"Message Monitor",start:"Start Monitoring",stop:"Stop Monitoring",clear:"Clear",export:"Export",expand:"Expand",collapse:"Collapse",close:"Close",messageType:"Message",noMessages:"No message data",noMessagesToExport:"No message data to export",startSuccess:"Started monitoring messages",stopSuccess:"Stopped monitoring messages",clearSuccess:"Messages cleared successfully",exportSuccess:"Messages exported successfully",toggleFailed:"Failed to toggle monitoring status",pauseScroll:"Pause Scroll",resumeScroll:"Resume Scroll",monitoring:"Monitoring",copy:"Copy",copySuccess:"Message copied to clipboard",copyFailed:"Copy failed",autoScrollEnabled:"Auto scroll enabled",autoScrollDisabled:"Auto scroll paused",send:"Send",receive:"Receive",message:"Message"},search:{placeholder:"Search device",ipPortExist:"This IP and port already exist, please do not add again"},summaryPie:{other:"Other",title:"Setting Count Ratio",subtext:"Setting Group Value"},deviceInfo:{title:"Device Info",export:"Export",exportTitle:"Export Device Info",exportLoading:"Exporting device basic info...",exportSuccess:"Device basic info exported successfully",exportFailed:"Failed to export device basic info",getInfoFailed:"Failed to get device info. Reason: {msg}",getInfoFailedEmpty:"Failed to get device info. Reason: Data is empty!",defaultFileName:"Device Info.xlsx",confirm:"Confirm",tip:"Tip"},allParamSetting:{title:"All Settings",autoRefresh:"Auto Refresh",refresh:"Refresh",confirm:"Confirm",import:"Import",export:"Export",groupTitle:"Setting Group:",allGroups:"All",noDataToImport:"No data to import",importSuccess:"Settings imported successfully",importFailed:"Failed to import settings: {msg}",requestFailed:"Request failed, please try again later",queryFailed:"Failed to query settings: {msg}",unsavedChanges:"There are unsaved changes, continue to refresh?",confirmButton:"Confirm",cancelButton:"Cancel",alertTitle:"Tip",errorTitle:"Error",noDataToConfirm:"No data to confirm",confirmSuccess:"Settings updated successfully",confirmFailed:"Failed to update settings: ",responseLog:"Response data:",continueAutoRefresh:"Continue to enable auto refresh",settingGroup:"Setting Group",all:"All",minValue:"Min Value",maxValue:"Max Value",step:"Step",unit:"Unit",searchNamePlaceholder:"Enter setting name to search",searchDescPlaceholder:"Enter setting description to search",autoRefreshWarning:"Auto refresh is enabled, modification is not allowed",invalidValue:"The value {value} of setting {name} is not within the valid range",exportFileName:"Device Parameter Settings_All Settings.xlsx",selectPathLog:"Select path: ",exportSuccess:"Export setting list successfully"},variable:{autoRefresh:"Auto Refresh",variableName:"Variable Name",inputVariableName:"Please enter the variable name to add",refresh:"Refresh",add:"Add",confirm:"Confirm",import:"Import",export:"Export",delete:"Delete",noDataToConfirm:"No data to confirm",warning:"Warning",variableModifiedSuccess:"Variable modified successfully",variableModifiedFailed:"Failed to modify variable, reason:",requestFailed:"Request failed, please try again later",error:"Error",success:"Success",variableAddSuccess:"Variable added successfully",variableAddFailed:"Failed to add variable, reason:",variableDeleteSuccess:"Variable deleted successfully",variableDeleteFailed:"Failed to delete variable, reason:",exportSuccess:"Device debug variable info exported successfully",exportFailed:"Failed to export device debug variable info, reason:",importSuccess:"Device debug variable info imported successfully",importFailed:"Failed to import device debug variable info:",confirmRefresh:"There are unsaved changes, continue to refresh?",confirmAutoRefresh:"There are unsaved changes, continue to enable auto refresh?",pleaseInputVariableName:"Please enter the variable name",exportTitle:"Export Device Debug Variable",importTitle:"Import Device Debug Variable",defaultExportPath:"Device Debug Variable.xlsx",title:"Variable Debug",variableNamePlaceholder:"Please enter the variable name to add",batchDelete:"Batch Delete",modifySuccess:"Variable modified successfully",modifyFailed:"Failed to modify variable, reason: {msg}",alertTitle:"Warning",successTitle:"Tip",confirmButton:"Confirm",cancelButton:"Cancel",sequence:"Index",id:"ID",name:"Name",value:"Value",type:"Type",description:"Description",address:"Address",operation:"Operation",enterVariableName:"Please enter the variable name to add",responseLog:"Response data:",addSuccess:"Variable added successfully",addFailed:"Failed to add variable, reason:",addFailedWithName:"Failed to add variable {name}: {reason}",exportFileName:"Device Debug Variable.xlsx",selectPathLog:"Select path:",exportSuccessLog:"Device debug variable info exported successfully, {path}",exportFailedLog:"Failed to export device debug variable info, reason:",importFailedLog:"Failed to import device debug variable info:",unsavedChanges:"There are unsaved changes, continue to refresh?",continueAutoRefresh:"Continue to enable auto refresh",tip:"Tip",sequenceNumber:"Index",autoRefreshEditForbidden:"Editing is forbidden in auto refresh mode",warningTitle:"Warning",invalidNumber:"Invalid number: {value}",cancel:"Cancel"},backup:{sequence:"Index",title:"Device Backup",savePath:"Save Path",setPath:"Set Backup Save Path",setPathTitle:"Set Path",startBackup:"Start Backup",cancelBackup:"Cancel Backup",backup:"Backup",backupType:"Backup Type",progress:"Progress",status:"Status",operation:"Operation",backupTypes:{paramValue:"Device Parameter Values",faultInfo:"Device Fault Reports",cidConfigPrjLog:"CID/CCD/Device Config/Debug Info/PRJ/Log",waveReport:"Device Wave Files"},backupDesc:"Backup Content Description",backupDescTypes:{paramValue:"Export device parameter values (param export.xlsx)",faultInfo:"Export device fault info (event/operation/fault/audit reports)",cidConfigPrjLog:"Export configuration files (CID/CCD, XML config, log files)",waveReport:"Export device wave files (/wave/comtrade)"},locateFolder:"Locate Folder",backupSuccess:"Backup Successful",openFolderFailed:"Failed to open folder",backupFailed:"Backup Failed",noTypeSelected:"Please select backup type first",cancelSuccess:"Cancel successful",cancelFailed:"Cancel failed",noBackupToCancel:"No backup task is currently running",noTaskIdFound:"Task ID not found, unable to cancel",backupStatus:{starting:"Starting backup",userCancelled:"User cancelled",transferring:"Transferring"},console:{pathNotSet:"Backup path not set, unable to start backup",noTypeSelected:"No backup type selected, unable to start backup",startBackup:"Starting backup, types: {types}, path: {path}",backupException:"Backup exception: {error}",pathSelected:"Backup path selected: {path}",pathNotSelected:"Backup path not selected",pathNotSetForLocate:"Backup path not set, unable to locate folder",folderOpened:"Backup folder opened: {path}",openFolderFailed:"Failed to open backup folder: {error}",taskCompleted:"Task completed",taskCancelled:"Task cancelled",typeError:"Type [{type}] error: {error}",typeCompleted:"Type [{type}] backup completed",typeCancelled:"Type [{type}] cancelled",typeFailed:"Type [{type}] failed",attemptCancel:"Attempting to cancel backup task",noTaskIdFound:"Task ID not found, unable to cancel backup",cancelSuccess:"Backup task cancelled",cancelFailed:"Failed to cancel backup: {error}",cancelException:"Cancel backup exception: {error}",singleCancelSuccess:"Type [{type}] cancelled successfully",singleCancelFailed:"Type [{type}] cancel failed: {error}",singleCancelException:"Type [{type}] cancel exception: {error}"}},operate:{title:"Device Operation",manualWave:"Manual Wave Recording",resetDevice:"Device Reset",clearReport:"Clear Report",clearWave:"Clear Wave Recording",executing:"Executing...",selectOperation:"Please select operation",success:{manualWave:"Manual Wave Recording successful",resetDevice:"Device Reset successful",clearReport:"Clear Report successful",clearWave:"Clear Wave Recording successful"},fail:{manualWave:"Failed to record manual wave, reason:",resetDevice:"Failed to reset device, reason:",clearReport:"Failed to clear report, reason:",clearWave:"Failed to clear wave recording, reason:"}},time:{title:"Device Time Sync",currentTime:"Current Time",deviceTime:"Device Time",selectDateTime:"Select Date and Time",milliseconds:"Milliseconds",now:"Now",read:"Read",write:"Write",readSuccess:"Device time read successfully.",readFailed:"Failed to read device time: {msg}",readFailedInvalidFormat:"Failed to read device time: Invalid time format",readFailedDataError:"Failed to read device time: Time data format error",writeSuccess:"Device time written successfully.",writeFailed:"Failed to write device time: {msg}",writeFailedInvalidFormat:"Failed to write device time: Invalid time format",millisecondsRangeError:"Milliseconds value range should be between 0-999",unknownError:"Unknown error"},reportOperate:{title:"Report Operation",date:"Date:",search:"Search",save:"Save",clearList:"Clear List",loading:"Data loading",progress:{title:"Progress Information",loading:"Loading",searching:"Searching {type}"},table:{reportId:"Report ID",name:"Name",time:"Time",operationAddress:"Operation Address",operationParam:"Operation Parameter",value:"Value",step:"Step",source:"Source",sourceType:"Source Type",result:"Result"},messages:{selectDateRange:"Please select complete date range",noDataToSave:"No data to save",saveSuccess:"Save successful",saveReport:"Save Report"}},reportGroup:{title:"Report Group",date:"Date:",search:"Search",save:"Save",clearList:"Clear List",autoRefresh:"Auto Refresh",loading:"Data loading",progress:{title:"Progress Information",loading:"Loading",searching:"Searching {type}"},table:{reportId:"Report ID",time:"Time",description:"Description"},contextMenu:{uploadWave:"Upload Wave",getHistoryReport:"Get History Report",saveResult:"Save Result",clearContent:"Clear Content"},messages:{selectDateRange:"Please select complete date range",noDataToSave:"No data to save",noFileToUpload:"No file to upload",saveSuccess:"Save successful",saveReport:"Save Report",waveToolNotConfigured:"Wave tool not configured",waveFileUploading:"Wave file uploading",waveFileUploadComplete:"Wave file upload complete",waveFileUploadCompleteWithPath:"Wave file upload complete, path: {path}",openWaveFileConfirm:"Open wave file with third-party tool?",openWaveFileTitle:"Reminder",confirm:"Confirm",cancel:"Cancel"},refresh:{stop:"Stop Refresh",start:"Auto Refresh"},hiddenItems:{show:"Show Hidden Items",hide:"Hide Hidden Items"}},fileDownload:{title:"File Download",deviceDirectory:"Device Directory",reboot:"Reboot",noReboot:"No Reboot",selectFile:"Select File",addDownloadFile:"Add Download File",addDownloadFolder:"Add Download Folder",addDownloadFilesAndFolders:"Add Files and Folders",downloadFile:"Download File",cancelDownload:"Cancel Download",importList:"Import List",exportList:"Export List",batchDelete:"Batch Delete",clearList:"Clear List",download:"Download",delete:"Delete",fileName:"File Name",fileSize:"File Size",filePath:"File Path",lastModified:"Last Modified",progress:"Progress",status:"Status",operation:"Operation",folder:"Folder",waitingDownload:"Waiting Download",calculatingFileInfo:"Calculating File Info",downloadPreparing:"Download Preparing",downloading:"Downloading......",downloadComplete:"Download Complete",downloadError:"Download Error:",userCancelled:"User Cancelled",allFilesComplete:"Download Complete",fileExists:"File {path} already exists, add failed!",selectValidFile:"Please select a valid file for download operation",remotePathEmpty:"Remote path cannot be empty",noDownloadTask:"No download task obtained, cannot cancel",noDownloadInProgress:"No download task is currently in progress",noFilesSelected:"Please select files to operate",fileSizeZero:"File {fileName} size is 0, cannot download",downloadCancelled:"Download {path} cancelled complete",downloadCancelledFailed:"Download {path} cancelled failed, reason: {msg}",fileDeleted:"File {path} deleted complete",exportSuccess:"Export download file list successful",exportFailed:"Export download file list failed",importSuccess:"Import download file list successful",importFailed:"Import download file list failed: {msg}",downloadList:"Download File List",exportTitle:"Export Download File List",importTitle:"Import Download File List",error:"Error",tip:"Tip",confirm:"Confirm",sequence:"Index",confirmButton:"Confirm",cancelButton:"Cancel",alertTitle:"Tip",errorTitle:"Error",successTitle:"Success",warningTitle:"Warning",loading:"Loading",executing:"Executing...",noData:"No Data",selectDateRange:"Please select date range",search:"Search",save:"Save",clear:"Clear",refresh:"Refresh",stop:"Stop",start:"Start",show:"Show",hide:"Hide",showHiddenItems:"Show Hidden Items",hideHiddenItems:"Hide Items",continue:"Continue",cancel:"Cancel",confirmImport:"Confirm Import",confirmExport:"Confirm Export",confirmDelete:"Confirm Delete",confirmClear:"Confirm Clear",confirmCancel:"Confirm Cancel",confirmContinue:"Confirm Continue",confirmStop:"Confirm Stop",confirmStart:"Confirm Start",confirmShow:"Confirm Show",confirmHide:"Confirm Hide",confirmRefresh:"Confirm Refresh",confirmSave:"Confirm Save",confirmSearch:"Confirm Search",confirmClearList:"Confirm Clear List",confirmImportList:"Confirm Import List",confirmExportList:"Confirm Export List",confirmBatchDelete:"Confirm Batch Delete",confirmDownload:"Confirm Download",confirmCancelDownload:"Confirm Cancel Download",confirmDeleteFile:"Confirm Delete File",confirmClearFiles:"Confirm Clear Files",confirmImportFiles:"Confirm Import Files",confirmExportFiles:"Confirm Export Files",confirmBatchDeleteFiles:"Confirm Batch Delete Files",confirmDownloadFiles:"Confirm Download Files",confirmCancelDownloadFiles:"Confirm Cancel Download Files",rename:"Rename for Download",renamePlaceholder:"Rename when downloading (optional)",renameCopyFailed:"Failed to copy file for rename:",packageProgram:"Program Packaging",selectSaveDir:"Select Save Directory",packageBtn:"Package",locateDir:"Locate Folder",saveDirEmpty:"Please select a save directory first!",packageSuccess:"Program packaging completed!",packageFailed:"Packaging failed: {msg}",noFileSelected:"Please select the files to be packaged!",zipPath:"Zip Path: {zipPath}",addToDownload:"Add to Download",fileAdded:"File added to download list: {path}",rebootSuccess:"Device reboot successful",rebootFailed:"Device reboot failed: {msg}"},fileUpload:{serialNumber:"Index",title:"File Upload",importList:"Import List",exportList:"Export List",batchDelete:"Batch Delete",clearList:"Clear List",upload:"Upload",cancelUpload:"Cancel Upload",delete:"Delete",sequence:"Index",fileName:"File Name",fileSize:"File Size",filePath:"File Path",lastModified:"Last Modified",progress:"Progress",statusTitle:"Status",status:{waiting:"Waiting Upload",preparing:"Upload Preparing",uploading:"Uploading......",completed:"Upload Complete",error:"Upload Error:",cancelled:"User Cancelled"},operation:"Operation",calculatingFileInfo:"Calculating File Info",uploadPreparing:"Upload Preparing",uploading:"Uploading......",uploadComplete:"Upload Complete",uploadError:"Upload Error: {errorMsg}",userCancelled:"User Cancelled",allFilesComplete:"Upload Complete",fileExists:"File {path} already exists, add failed!",invalidFile:"Please select a valid file for upload operation",emptySavePath:"File save path cannot be empty",fileUploadComplete:"File {fileName} upload complete",selectPath:"Select Path",pathOptions:{shr:"/shr",configuration:"/shr/configuration",log:"/log",wave:"/wave",comtrade:"/wave/comtrade"},deviceDirectory:"Device Directory",savePath:"Save Path",setPath:"Set Path",getFiles:"Get Files",uploadFiles:"Upload Files",errors:{invalidFile:"Please select a valid file for upload operation",emptySavePath:"File save path cannot be empty",noUploadTask:"No upload task obtained, cannot cancel",noUploadInProgress:"No upload task is currently in progress",noFilesSelected:"Please select files to operate",getFilesFailed:"Failed to get device directory files",fileSizeZero:"File {fileName} size is 0, cannot upload"},messages:{uploadCompleted:"File upload complete",uploadCancelled:"File upload cancelled complete",clearListSuccess:"Clear file list successful"}},info:{title:"Device Info",export:"Export",exportSuccess:"Export device basic info successful",exportFailed:"Export device basic info failed",exportTip:"Tip",confirm:"Confirm",exportLoading:"Exporting device basic info...",getInfoFailed:"Failed to get device info. Reason: ",dataEmpty:"Data is empty!"},summary:{title:"Device Group Overview",basicInfo:"Basic Info",settingTotal:"Total Settings",telemetry:"Telemetry",teleindication:"Teleindication",telecontrol:"Telecontrol",driveOutput:"Drive Output",settingRatio:"Setting Ratio"},dict:{refresh:"Refresh",confirm:"Confirm",import:"Import",export:"Export",sequence:"Index",shortAddress:"Short Address",shortAddressTooltip:"Enter short address to search",chinese:"Chinese",english:"English",spanish:"Spanish",french:"French",operation:"Operation",confirmLog:"Confirm Dictionary",importLog:"Import Dictionary",exportLog:"Export Dictionary",refreshLog:"Refresh Dictionary",newValueLog:"New Value:"},allParamCompare:{title:"Import All Setting Value Difference Comparison",cancel:"Cancel",confirm:"Confirm Import",groupName:"Group Name",name:"Name",description:"Description",minValue:"Min Value",maxValue:"Max Value",step:"Step",unit:"Unit",address:"Address",oldValue:"Old Value",newValue:"New Value",sequence:"Index",searchName:"Enter setting name to search",searchDescription:"Enter setting description to search",messages:{noSelection:"No data selected",error:"Error"}},deviceForm:{title:{add:"Add Device",edit:"Edit Device"},name:"Device Name",ip:"IP Address",port:"Port",connectTimeout:"Connect Timeout (Milliseconds)",readTimeout:"Global Request Timeout (Milliseconds)",paramTimeout:"Setting Value Modify Timeout (Milliseconds)",encrypted:"Encrypted Connection",advanced:{show:"Expand Advanced Options",hide:"Hide Advanced Options"},buttons:{cancel:"Cancel",confirm:"Confirm"},messages:{nameRequired:"Please enter device name",nameTooLong:"Device name should not be too long",invalidIp:"Please enter a valid IP address",invalidPort:"Port must be between 1-65535",timeoutTooShort:"Timeout should not be too short"}},paramCompare:{title:"Import Setting Value Difference Comparison",cancel:"Cancel",confirm:"Confirm Import",sequence:"Index",name:"Name",description:"Description",minValue:"Min Value",maxValue:"Max Value",step:"Step",unit:"Unit",address:"Address",oldValue:"Old Value",newValue:"New Value",searchName:"Enter setting name to search",searchDescription:"Enter setting description to search",messages:{noSelection:"No data selected",error:"Error"}},progress:{title:"Progress Information",executing:"Executing..."},remoteYm:{title:"Remote Telemetry",sequence:"Index",shortAddress:"Short Address",description:"Description",value:"Value",operation:"Operation",inputShortAddressFilter:"Enter short address filter",inputDescriptionFilter:"Enter description filter",invalidData:"Data {name} value {value} is not valid",error:"Error",success:"Success",executeSuccess:"Execute successful",prompt:"Prompt",confirmButton:"Confirm",confirmExecute:"Confirm Execute",confirm:"Confirm",executeButton:"Execute",cancelButton:"Cancel"},remoteYt:{title:"Remote Telecontrol",sequence:"Index",directControl:"Direct Control",selectControl:"Select Control",shortAddress:"Short Address",description:"Description",value:"Value",operation:"Operation",inputShortAddressFilter:"Enter short address filter",inputDescriptionFilter:"Enter description filter",invalidData:"Data {name} value {value} is not valid",error:"Error",success:"Success",executeSuccess:"Execute successful",prompt:"Prompt",confirm:"Confirm",errorInfo:"Error Information",executeFailed:"Remote telecontrol execute failed, reason: {msg}",executeSuccessLog:"{desc} Remote telecontrol execute successful",cancelSuccess:"Cancel successful",cancelFailed:"Remote telecontrol cancel failed, reason: {msg}",selectSuccess:"Select successful, execute?",confirmInfo:"Confirm Information",execute:"Execute",cancel:"Cancel"},paramSetting:{title:"Device Setting Value",autoRefresh:"Auto Refresh",refresh:"Refresh",confirm:"Confirm",import:"Import",export:"Export",currentEditArea:"Current Running Area",selectEditArea:"Current Editing Area",noDataToImport:"No data to import",noDataToConfirm:"No data to confirm",importSuccess:"Setting value import successful",importFailed:"Setting value import failed",updateSuccess:"Setting value update successful",updateFailed:"Setting value update failed",requestFailed:"Request failed, please try again later",setEditArea:"Set",setEditAreaTitle:"Set Editing Area",setEditAreaSuccess:"Editing area set successful",modifiedWarning:"There are unsaved changes, continue to refresh?",autoRefreshWarning:"There are unsaved changes, continue to enable auto refresh?",autoRefreshDisabled:"Auto refresh is enabled, modification is not allowed",invalidValue:"Setting value {name} {value} is not valid",exportSuccess:"Export device setting value successful",exportFailed:"Export device setting value failed",noDiffData:"No difference data obtained",table:{index:"Index",name:"Name",description:"Description",value:"Value",minValue:"Min Value",maxValue:"Max Value",step:"Step",address:"Address",unit:"Unit",operation:"Operation"},search:{namePlaceholder:"Enter setting name to search",descPlaceholder:"Enter setting description to search"}},remoteControl:{title:"Remote Control",sequence:"Index",shortAddress:"Short Address",description:"Description",control:"Control Split/Control Combine",type:"Type",operation:"Operation",directControl:"Direct Control",selectControl:"Select Control",controlClose:"Control Split",controlOpen:"Control Combine",noCheck:"No Check",syncCheck:"Check Sync",deadCheck:"Check No Pressure",confirmInfo:"Confirm Information",execute:"Execute",cancel:"Cancel",confirm:"Confirm",success:"Success",failed:"Failed",errorInfo:"Error Information",promptInfo:"Prompt Information",confirmSuccess:"Select successful, execute?",executeSuccess:"Execute successful",cancelSuccess:"Cancel successful",executeFailed:"Remote control execute failed, reason:",cancelFailed:"Remote control cancel failed, reason:",remoteExecuteSuccess:"Remote control execute successful",remoteCancelSuccess:"Remote control cancel successful"},remoteDrive:{action:"Action",executeSuccess:"Execute successful",executeFailed:"Execute failed",prompt:"Prompt Information",error:"Error Information",confirm:"Confirm",shortAddress:"Short Address",description:"Description",operation:"Operation",enterToFilter:"Enter short address filter",enterToFilterDesc:"Enter description filter",actionSuccess:"Action execute successful",actionFailed:"Action execute failed",failureReason:"Failure Reason",sequence:"Index"},remoteSignal:{autoRefresh:"Auto Refresh",refresh:"Refresh",export:"Export",sequence:"Index",name:"Name",description:"Description",value:"Value",quality:"Quality",searchName:"Enter name to search",searchDesc:"Enter description to search",searchValue:"Enter value to search",exportTitle:"Export Device Teleindication Info",exportSuccess:"Export device teleindication info successful",exportFailed:"Export device teleindication info failed",exportSuccessWithPath:"Export device teleindication info successful,",exportFailedWithError:"Export device teleindication info failed:",invalidData:"Invalid data:",errorInDataCallback:"Data callback processing error:",errorFetchingData:"Data fetching error:"},remoteTelemetry:{autoRefresh:"Auto Refresh",refresh:"Refresh",export:"Export",sequence:"Index",name:"Name",description:"Description",value:"Value",unit:"Unit",quality:"Quality",searchName:"Enter name to search",searchDesc:"Enter description to search",searchValue:"Enter value to search",exportTitle:"Export Device State Info",exportSuccess:"Export device state info successful",exportFailed:"Export device state info failed",exportSuccessWithPath:"Export device state info successful,",exportFailedWithError:"Export device state info failed:",confirm:"Confirm",tip:"Tip",exportFileName:"Device State Info",selectPathLog:"Select path:"},remote:{directControl:"Direct Control",selectControl:"Select Control",serialNumber:"Index",shortAddress:"Short Address",description:"Description",value:"Value",operation:"Operation",inputShortAddressFilter:"Enter short address filter",inputDescriptionFilter:"Enter description filter",invalidData:"Data {name} value {value} is not valid",error:"Error",success:"Success",executeSuccess:"Execute successful",prompt:"Prompt Information",confirm:"Confirm",errorInfo:"Error Information",executeFailed:"Remote telecontrol execute failed, reason: {msg}",executeSuccessLog:"{desc} Remote telecontrol execute successful",cancelSuccess:"Cancel successful",cancelFailed:"Remote telecontrol cancel failed, reason: {msg}",selectSuccess:"Select successful, execute?",confirmInfo:"Confirm Information",execute:"Execute",cancel:"Cancel"},report:{uploadWave:"Upload Wave",searchHistory:"Get History Report",saveResult:"Save Result",clearContent:"Clear Content",date:"Date",query:"Query",save:"Save",autoRefresh:"Auto Refresh",stopRefresh:"Stop Refresh",clearList:"Clear List",progressInfo:"Progress Information",loading:"Data loading",reportNo:"Report Index",time:"Time",description:"Description",noFileToUpload:"No file to upload",uploadSuccess:"Wave file upload complete",uploadPath:"Wave file upload complete, path:",noDataToSave:"No data to save",saveSuccess:"Save successful",saveReport:"Save Report",openWaveConfirm:"Open wave file with third-party tool?",confirm:"Confirm",cancel:"Cancel",waveToolNotConfigured:"Wave tool not configured",pleaseSelectTimeRange:"Please select complete time range",querying:"Querying",reportNumber:"Report Index",operationAddress:"Operation Address",operationParams:"Operation Parameters",result:"Result",progress:"Progress Information",loadingText:"Loading",selectCompleteTimeRange:"Please select complete time range",fileUploading:"Wave file upload in progress",fileUploadComplete:"File upload complete"},customMenu:{addMenu:"Add Custom Menu",editMenu:"Edit Custom Menu",deleteMenu:"Delete Custom Menu",addReport:"Add Custom Report",editReport:"Edit Custom Report",deleteReport:"Delete Custom Report",addPointGroup:"Add Custom Point Group",editPointGroup:"Edit Custom Point Group",deletePointGroup:"Delete Custom Point Group",selectedPoints:"Selected Points",selectFc:"Select FC",selectGroupType:"Select Group Type",groupTypes:{ST:"Remote Signal",MX:"Remote Measurement",SP:"Single Zone Setting",SG:"Multi Zone Setting"},filterPlaceholder:"Filter by name/description",loadingData:"Loading data...",noDataForFc:"No data for this FC",noDataForGroupType:"No data for this group type",pleaseSelectFc:"Please select FC first",pleaseSelectGroupType:"Please select group type first",loadingGroupTypeData:"Loading group type data...",loadingGroupTypes:"Loading group types data...",loadedGroupTypes:"Loaded group types",dataLoadComplete:"Data loading complete",dataLoadFailed:"Data loading failed",switchingToGroupType:"Switching to",loadingGroupTypeDataSingle:"Loading data...",loadGroupTypeFailed:"Failed to load data",loadGroupTypeError:"Error occurred while loading data",inputGroupName:"Please enter group name",inputGroupDesc:"Please enter group description",selectGroupTypeFirst:"Please select group type first",nameValidationFailed:"Name validation failed, please try again",nameConflictWithSystem:"Name conflicts with system menu, please use another name",nameConflictWithCustom:"Name conflicts with existing custom menu, please use another name",menuName:"Group Name",menuDesc:"Description",reportName:"Report Name",reportDesc:"Description",reportKeyword:"Keyword",reportInherit:"Inherit Report",inputMenuName:"Please enter group name",inputMenuDesc:"Please enter description",inputReportName:"Please enter report name",inputReportDesc:"Please enter description",inputReportKeyword:"Please enter keyword",selectReportInherit:"Please select inherit report",cancel:"Cancel",confirm:"Confirm",successAddMenu:"Custom menu added successfully",successEditMenu:"Custom menu edited successfully",successDeleteMenu:"Custom menu deleted successfully",successAddReport:"Custom report added successfully",successEditReport:"Custom report edited successfully",successDeleteReport:"Custom report deleted successfully",successDeletePointGroup:"Custom point group deleted successfully",errorAction:"Operation failed",errorDelete:"Delete failed",confirmDeleteMenu:"Are you sure you want to delete this custom menu?",confirmDeleteReport:"Are you sure you want to delete this custom report?",confirmDeletePointGroup:"Are you sure you want to delete this custom point group?",tip:"Tip"},tree:{inputGroupName:"Please enter group name",expandAll:"Expand All",collapseAll:"Collapse All"}},Ct={device:{configure:{selectConfigure:"Please select a configuration symbol file!",loading:"Loading",operating:"Operating",loadFailed:"Loading failed, reason:",getCustomDeviceFailed:"Failed to get custom device",registerDataFailed:"Data registration failed, reason:",variablesNotExist:"Some variables do not exist:",getDataError:"Data retrieval error",remoteSet:"Remote Set",remoteControlFailed:"Remote control failed, reason:",remoteControlSuccess:"Remote control succeeded",noRemoteControlType:"Remote control type not configured",symbolChangeReload:"Symbol changed, reloading",deviceChangeReload:"Device changed, reloading",deviceConnectReload:"Device connected successfully, reloading"},configureList:{searchPlaceholder:"Search by keyword",deviceMonitor:"Device Monitor",newProject:"New Project",addCustomComponent:"Add Custom Component",newConfigure:"New Configuration",renameProject:"Rename Project",deleteProject:"Delete Project",editConfigure:"Edit Configuration",renameConfigure:"Rename Configuration",deleteConfigure:"Delete Configuration",openFolder:"Open Folder",inputProjectName:"Please enter project name (within 10 characters, no special characters)",inputConfigureName:"Please enter configuration name (within 10 characters, no special characters)",confirm:"Confirm",cancel:"Cancel",invalidName:"Invalid name length or content",projectAddSuccess:"Project: {name} added successfully",projectAddFailed:"Project: {name} addition failed, reason:",configureAddSuccess:"Configuration: {name} added successfully",configureAddFailed:"Configuration: {name} addition failed, reason:",renameSuccess:"Rename successful",renameFailed:"Rename failed, reason:",confirmDelete:"Are you sure to delete?",confirmBatchDelete:"This project has associated configuration content, are you sure to batch delete?",deleteSuccess:"Delete successful",deleteFailed:"Delete failed, reason:",remoteSet:"Delete Confirmation"},configures:{customComponent:"Custom Component",selectDevice:"Please select associated device!",edit:"Edit:"},customConfigure:{getCustomDeviceFailed:"Failed to get custom device",saveDeviceFailed:"Failed to save device symbol",saveSuccess:"Save successful",deleteDeviceFailed:"Failed to delete device symbol",deleteSuccess:"Delete successful",tip:"Tip"},deviceList:{unnamed:"Unnamed Device",connect:"Connect",disconnect:"Disconnect",edit:"Edit",delete:"Delete",notFound:"No device found",editWarn:"Please disconnect before editing",deleteWarn:"Please disconnect before deleting",connectSuccess:"Device {name}: Connected successfully",connectExist:"Device {name}: Connection already exists",connectFailed:"Device {name}: Connection failed",connectFailedReason:"Device connection failed reason: {reason}",disconnectSuccess:"Device {name}: Disconnected",disconnectedNotify:"Device {name} connection disconnected",currentDisconnectedNotify:"Current device connection disconnected",operateFailed:"Device {name}: Operation failed",remove:"Remove"},deviceSearch:{searchPlaceholder:"Search device"},editConfigure:{saveSuccess:"Save successful",saveFailed:"Save failed, reason:",loadFailed:"Loading failed, reason:",getCustomDeviceFailed:"Failed to get custom device",tip:"Tip"},remoteSet:{inputValue:"Input Value",write:"Write",cancel:"Cancel",setFailed:"Remote set failed, reason:",operateSuccess:"Operation successful",noSetType:"Remote set type not configured"}},graph:{component:{electricSymbols:"Electric Symbols",customComponents:"Custom Components",basicComponents:"Basic Components"},toolbar:{undo:"Undo",redo:"Redo",bringToFront:"Bring to Front",sendToBack:"Send to Back",ratio:"Proportional Scaling",delete:"Delete",save:"Save"},contextMenu:{group:"Group",ungroup:"Ungroup",linkData:"Link Data",equipmentSaddr:"Device Address"},dialog:{dataConfig:"Data Configuration",tip:"Tip",selectOneGraph:"Please select a symbol"},message:{waitForCanvasInit:"Please wait for canvas initialization to complete",loadEquipmentFailed:"Failed to load symbol",loadEquipmentError:"Failed to load device",equipmentLoaded:"Device loaded"},basic:{title:"Basic Components",components:{line:"Line",text:"Text",rectangle:"Rectangle",circle:"Circle",ellipse:"Ellipse",triangle:"Triangle",arc:"Arc"}},selectEquipment:{sequence:"Index",name:"Name",type:"Type",symbol:"Symbol",operation:"Operation",reference:"Reference"},setSAddr:{telemetry:"Remote Sensing/Remote Measurement",format:"Formatting",factor:"Factor",remoteControl:"Remote Control",controlType:"Remote Control Method",controlValue:"Remote Control Value",remoteSet:"Remote Set",setType:"Remote Set Method",displayConfig:"Display Configuration",addRow:"Add Row",sequence:"Index",type:"Type",originalValue:"Original Value",displayValue:"Display Value",operation:"Operation",text:"Text",symbol:"Symbol",selectSymbol:"Select Symbol",confirm:"Confirm",cancel:"Cancel",confirmDelete:"Are you sure to delete?",tip:"Tip",selectControl:"Select Control",directControl:"Direct Control",controlClose:"Control Close",controlOpen:"Control Open",cancelDelete:"Cancel Delete"},equipmentType:{CBR:"Circuit Breaker",DIS:"Isolation Disconnector",GDIS:"Ground Disconnector",PTR2:"2-Winding Transformer",PTR3:"3-Winding Transformer",VTR:"Voltage Transformer",CTR:"Current Transformer",EFN:"Neutral Grounding Device",IFL:"Outlet",EnergyConsumer:"Load",GND:"Ground",Arrester:"Surge Arrester",Capacitor_P:"Parallel Capacitor",Capacitor_S:"Series Capacitor",Reactor_P:"Parallel Reactor",Reactor_S:"Series Reactor",Ascoil:"Arc Suppression Coil",Fuse:"Fuse",BAT:"Battery",BSH:"Capacitor",CAB:"Cable",LIN:"Overhead Line",GEN:"Generator",GIL:"Electrical Insulation Line",RRC:"Rotating Reactive Component",TCF:"Thyristor Controlled Frequency Converter",TCR:"Thyristor Controlled Reactive Component",LTC:"Tap Changer",IND:"Inductor"},equipmentName:{breaker_vertical:"Circuit Breaker-Vertical",breaker_horizontal:"Circuit Breaker-Horizontal",breaker_invalid_vertical:"Circuit Breaker-Invalid-Vertical",breaker_invalid_horizontal:"Circuit Breaker-Invalid-Horizontal",disconnector_vertical:"Disconnector-Vertical",disconnector_horizontal:"Disconnector-Horizontal",disconnector_invalid_vertical:"Disconnector-Invalid-Vertical",disconnector_invalid_horizontal:"Disconnector-Invalid-Horizontal",hv_fuse:"High Voltage Fuse",station_transformer_2w:"Station Transformer (Two Winding)",transformer_y_d_11:"Transformer (Y/△-11)",transformer_d_y_11:"Transformer (△/Y-11)",transformer_d_d:"Transformer (△/△)",transformer_y_y_11:"Transformer (Y/Y-11)",transformer_y_y_12_d_11:"Transformer (Y/Y-12/△-11)",transformer_y_d_11_d_11:"Transformer (Y/△-11/△-11)",transformer_y_y_v:"Transformer (Y/Y/V)",transformer_autotransformer:"Transformer (Autotransformer)",voltage_transformer_2w:"Voltage Transformer (Two Winding)",voltage_transformer_3w:"Voltage Transformer (Three Winding)",voltage_transformer_4w:"Voltage Transformer (Four Winding)",arrester:"Surge Arrester",capacitor_horizontal:"Capacitor-Horizontal",capacitor_vertical:"Capacitor-Vertical",reactor:"Reactor",split_reactor:"Split Reactor",power_inductor:"Power Inductor",feeder:"Outlet",ground:"Ground",tap_changer:"Tap Changer",connection_point:"Connection Point",transformer_y_y_12_d_11_new:"Transformer (Y/Y-12/△-11)(New)",pt:"PT",arrester_new:"Surge Arrester(New)",disconnector_vertical_new:"Disconnector-Vertical(New)",disconnector_horizontal_new:"Disconnector-Horizontal(New)",arrester_new_vertical:"Surge Arrester(New)-Vertical",disconnector_vertical_left_new:"Disconnector-Vertical Left(New)"}},graphProperties:{blank:{propertySetting:"Property Setting"},graph:{canvasSetting:"Canvas Setting",grid:"Grid",backgroundColor:"Background Color"},group:{groupProperty:"Group Property",basic:"Basic",width:"Width",height:"Height",x:"Position(X)",y:"Position(Y)",angle:"Rotation Angle"},node:{nodeProperty:"Node Property",style:"Style",backgroundColor:"Background Color",borderWidth:"Border Width",borderColor:"Border Color",borderDasharray:"Border Style",rx:"Border rx",ry:"Border ry",position:"Position",width:"Width",height:"Height",x:"Position(X)",y:"Position(Y)",property:"Property",angle:"Rotation Angle",zIndex:"Level(z)",fontFamily:"Font",fontColor:"Font Color",fontSize:"Font Size",text:"Text"},pathLine:{lineSetting:"Line Setting",style:"Style",lineHeight:"Width",lineColor:"Color",borderDasharray:"Border",position:"Position",width:"Width",height:"Height",x:"Position(X)",y:"Position(Y)",property:"Property",angle:"Rotation Angle",zIndex:"Level(z)"}},business:{hmi:{title:"Screen Management",form:{add:"Add Screen",edit:"Edit Screen",view:"View Screen",name:"Screen Name",type:"Screen Type",template:"Screen Template",description:"Description",cancel:"Cancel",confirm:"Confirm",validation:{name:"Please enter screen name",type:"Please select screen type",template:"Please select screen template"}},columns:{name:"Screen Name",type:"Screen Type",template:"Screen Template",createTime:"Create Time",updateTime:"Update Time",status:"Status",operation:"Operation"},type:{device:"Device Screen",process:"Process Screen",alarm:"Alarm Screen",custom:"Custom Screen"},status:{draft:"Draft",published:"Published",archived:"Archived"},editor:{title:"Screen Editing",save:"Save",preview:"Preview",publish:"Publish",cancel:"Cancel",tools:{select:"Select",rectangle:"Rectangle",circle:"Circle",line:"Line",text:"Text",image:"Image",device:"Device",alarm:"Alarm",chart:"Chart"},properties:{title:"Property",position:"Position",size:"Size",style:"Style",data:"Data",event:"Event"}},preview:{title:"Screen Preview",fullscreen:"Full Screen",exit:"Exit",zoom:{in:"Zoom In",out:"Zoom Out",fit:"Fit"}},publish:{title:"Publish Screen",version:"Version Number",description:"Publish Description",cancel:"Cancel",confirm:"Confirm",validation:{version:"Please enter version number",description:"Please enter publish description"}},template:{title:"Screen Template",add:"Add Template",edit:"Edit Template",delete:"Delete Template",name:"Template Name",category:"Template Category",description:"Description",preview:"Preview",cancel:"Cancel",confirm:"Confirm",validation:{name:"Please enter template name",category:"Please select template category"}}}}},xt={common:{date:"Date",search:"Search",save:"Save",clear:"Clear",loading:"Loading...",reportNo:"Report Index",time:"Time",description:"Description",progress:"Progress",selectDateRange:"Please select date range",noData:"No data",saveSuccess:"Save successful",saveFailed:"Save failed"},date:"Date",search:"Search",filter:"Filter",save:"Save",clearList:"Clear List",loading:"Loading...",reportNumber:"Report Index",time:"Time",description:"Description",progress:"Progress",loadingText:"Loading...",querying:"Querying",selectCompleteTimeRange:"Please select complete time range",noDataToSave:"No data to save",saveSuccess:"Save successful",saveReport:"Save Report",fileUploading:"File uploading",fileUploadComplete:"File upload complete",autoRefresh:"Auto Refresh",showHiddenItems:"Show Hidden Items",hideHiddenItems:"Hide Hidden Items",name:"Name",operationAddress:"Operation Address",operationParams:"Operation Parameters",value:"Value",step:"Step",source:"Source",sourceType:"Source Type",result:"Result",searchType:"Search Type",total:"Total {num} items",sameSearch:"Same content search",sameFilter:"Same content filter",showHideTime:"Show/Hide Time Column",selectRowToOperate:"Please select the row to operate first",trip:{autoRefresh:"Auto Refresh"},operate:{name:"Name",operateAddress:"Operation Address",operateParam:"Operation Parameter",value:"Value",step:"Step",source:"Source",sourceType:"Source Type",result:"Result"},group:{uploadWave:"Upload Wave",searchHistory:"Search History",saveResult:"Save Result",clearContent:"Clear Content",contextMenu:{uploadWave:"Upload Wave",getHistoryReport:"Get History Report",saveResult:"Save Result",clearContent:"Clear Content"},date:"Date",search:"Search",save:"Save",clearList:"Clear List",loading:"Loading...",table:{reportId:"Report ID",time:"Time",description:"Description"},progress:{title:"Progress",searching:"Searching {type}",loading:"Loading..."},refresh:{start:"Start Refresh",stop:"Stop Refresh"},hiddenItems:{show:"Show Hidden Items"},messages:{noFileToUpload:"No file to upload",selectDateRange:"Please select date range",noDataToSave:"No data to save",saveReport:"Save Report",saveSuccess:"Save successful"}},entryID:"Index",module:"Module Name",msg:"Content",level:"Level",type:"Type",origin:"Origin",user:"User Name",exporting:"Exporting...",stopRefresh:"Stop Refresh",searchProgress:"Searching {type}",pleaseSelectSavePath:"Please select save path...",exportLogSuccess:"Export succeeded: {path}",exportLogFailed:"Export failed: {msg}",exportLogCancelled:"User cancelled export operation",items:"items"},St={device:vt,hmi:Ct,report:xt},yt={common:{add:"Add",index:"Index",delete:"Delete",clear:"Clear",import:"Import",export:"Export",execute:"Execute",moveUp:"Move Up",moveDown:"Move Down",loading:"Loading...",success:"Success",failed:"Failed",confirm:"Confirm",cancel:"Cancel",yes:"Yes",no:"No",operation:"Operation",tips:"Tips",title:"Prompt"},search:{placeholder:"Search function"},functionList:{unnamedDevice:"Unnamed Device",batchDownload:{name:"Batch Download",desc:"Batch download of multiple device files and batch import of settings"},xmlFormatter:{name:"XML Formatter",desc:"Quickly organize XML data hierarchically"},jsonFormatter:{name:"JSON Formatter",desc:"Intelligent formatting of JSON data, supports syntax validation"},radixConverter:{name:"Radix Converter",desc:"Supports conversion between binary, decimal, hexadecimal, and other bases"},temperatureConverter:{name:"Temperature Converter",desc:"Intelligent conversion between Celsius, Fahrenheit, Kelvin, and other temperature units"},encryption:{name:"Text Encryption/Decryption",desc:"Quick text encryption and decryption based on AES, RSA, Base64, etc."},packageProgram:{name:"Program Packaging",desc:"Package device runtime files for export, support custom save directory and one-click folder location"}},matrixContent:{loading:"Loading...",tabs:{deviceList:"Devices",downloadConfig:"Files",paramConfig:"Params"}},taskSteps:{connect:"Connect",download:"Download",import:"Import",disconnect:"Disconnect",complete:"Complete"},messages:{connectDevice:"Connect Device",executeFileDownload:"Execute File Download",downloadingFile:"Downloading File",downloadFileFailed:"File Download Failed",fileDownloadCompleted:"File Download Completed",executeParamImport:"Execute Parameter Import",paramValidationFailed:"Parameter Validation Failed",paramImportFailed:"Parameter Import Failed",paramImportCompleted:"Parameter Import Completed",taskCompleted:"Task Completed",deviceConnectionFailed:"Device Connection Failed",deviceRebootSuccess:"Device Reboot Successful"},deviceList:{title:"Devices",deviceListExcel:"Device List.xlsx",exportDeviceList:"Export Device List",importDeviceList:"Import Device List",exportSuccess:"Device list exported successfully",exportFail:"Failed to export device list",importSuccess:"Device list imported successfully",importFail:"Failed to import device list",exportSuccessMsg:"Device list exported successfully",exportFailMsg:"Device list export failed",importSuccessMsg:"Device list imported successfully",importFailMsg:"Device list import failed: {msg}",deviceName:"Device Name",deviceAddress:"Device Address",devicePort:"Device Port",isEncrypted:"Encrypted",encrypted:"Encrypted",notEncrypted:"Not Encrypted",status:"Status",operation:"Operation",reboot:"Reboot",noReboot:"No Reboot",addDevice:"Add Device",deleteDevice:"Delete Device",clearDevices:"Clear Devices",deviceExists:"Device already exists",deviceDeleted:"Device {ip} deleted",downloadFile:"Download file?",importParam:"Import parameter?",connectTimeout:"Connection Timeout",paramTimeout:"Parameter Modification Timeout",readTimeout:"Global Request Timeout",progress:"Progress"},downList:{title:"Download",deviceDirectory:"Device Directory",fileName:"File Name",fileSize:"File Size",filePath:"File Path",lastModified:"Last Modified",addFile:"Add file to download",addFolder:"Add folder to download",fileExists:"File {path} already exists, add failed!",fileDeleted:"File {path} deleted",filesDeleted:"Files deleted",defaultExportFileName:"Download File List.xlsx",exportTitle:"Export Download File List",importTitle:"Import Download File List",exportSuccess:"File list exported successfully",exportFailed:"File list export failed",importSuccess:"File list imported successfully",importFailed:"File list import failed",fileExistsMsg:"File {path} already exists, add failed!"},paramList:{title:"Parameters",paramGroup:"Setting Group",groupName:"Group Name",paramName:"Parameter Name",paramDesc:"Parameter Description",paramValue:"Parameter Value",minValue:"Min Value",maxValue:"Max Value",step:"Step",unit:"Unit",searchParamName:"Parameter Name",searchParamDesc:"Parameter Description",importSuccess:"Parameter import successful",importFailed:"Parameter import failed",exportSuccess:"Parameter export successful",exportFailed:"Parameter export failed",clearSuccess:"Parameter list cleared successfully"},progressDialog:{title:"Processing",pleaseWait:"Please wait..."},packageProgram:{saveDir:"Save Directory",selectSaveDir:"Select Save Directory",packageBtn:"Package",locateDir:"Locate Folder",delete:"Delete",sequence:"No.",fileName:"File Name",fileSize:"File Size",filePath:"File Path",lastModified:"Last Modified",operation:"Operation",saveDirEmpty:"Please select a save directory first!",packageSuccess:"Program packaging completed!",tip:"Tip",confirmButton:"OK",defaultExportFileName:"Program Package File List.xlsx",exportTitle:"Export Program Package File List",importTitle:"Import Program Package File List",exportSuccess:"File list exported successfully",exportFailed:"File list export failed",importSuccess:"File list imported successfully",importFailed:"File list import failed",fileExists:"File {path} already exists, add failed!",selectDirSuccess:"Directory selected: {dir}",locateDirSuccess:"Located directory: {dir}",addFileStart:"Opening file selector...",addFileSuccess:"Successfully added {count} files/folders",addFileNone:"No new files/folders added",deleteSuccess:"Deleted {count} files/folders successfully",clearSuccess:"All files/folders cleared",moveUpSuccess:"Moved up: {name}",moveDownSuccess:"Moved down: {name}",noFileSelected:"Please select the files to be packaged!",noDeviceSelected:"Please select the devices to be packaged!",packageFailed:"Packaging failed: {msg}",openFileButton:"Open File",zipPath:"Zip Path: {zipPath}"}},bt={search:{placeholder:"Search device",button:"Search",success:"Search successful"},device2:{search:{placeholder:"Search device",add:"Add Device",duplicate:"This IP and port already exist, please do not add again"},list:{empty:"No device found",unnamed:"Unnamed Device",status:{connected:"Connected",disconnected:"Disconnected"},contextMenu:{connect:"Connect",edit:"Edit",disconnect:"Disconnect",remove:"Delete"},message:{disconnectFirst:"Please disconnect before editing",disconnectFirstDelete:"Please disconnect before deleting",connectSuccess:"Device {name}: Connected successfully",connectExists:"Device {name}: Connection already exists",connectFailed:"Device {name}: Connection failed",connectFailedReason:"Device connection failed reason: {reason}",disconnected:"Device {name}: Disconnected",operationFailed:"Device {name}: Operation failed"}},report:{group:{openWaveConfirm:"Open wave file with third-party tool?",tips:"Reminder",noWaveTool:"Wave tool not configured"},common:{selectRow:"Please select the row to operate"}},backup:{savePath:"Save Path",setPath:"Please set backup path",setPathTitle:"Set backup path",locateFolder:"Locate Folder",startBackup:"Start Backup",cancelBackup:"Cancel Backup",backup:"Backup",sequence:"Sequence",backupType:"Backup Type",backupDesc:"Backup Description",progress:"Progress",status:"Status",noTypeSelected:"Please select backup type",backupSuccess:"Backup successful",backupFailed:"Backup failed",openFolderFailed:"Failed to open folder",backupTypes:{paramValue:"Parameter Values",faultInfo:"Fault Information",cidConfigPrjLog:"CID Config Project Log",waveReport:"Wave Report"},backupDescTypes:{paramValue:"Backup all parameter setting values of the device",faultInfo:"Backup fault recording information of the device",cidConfigPrjLog:"Backup CID configuration files and project logs",waveReport:"Backup waveform analysis report files"},backupStatus:{userCancelled:"User Cancelled",transferring:"Transferring"},console:{pathNotSet:"Backup path not set, unable to start backup",noTypeSelected:"No backup type selected, unable to start backup",startBackup:"Start backup, types: {types}, path: {path}",backupException:"Backup exception: {error}",pathSelected:"Backup path selected: {path}",pathNotSelected:"No backup path selected",pathNotSetForLocate:"Backup path not set, unable to locate folder",folderOpened:"Backup folder opened: {path}",openFolderFailed:"Failed to open backup folder: {error}",taskCompleted:"Task completed",taskCancelled:"Task cancelled",typeError:"Type [{type}] error: {error}",typeCompleted:"Type [{type}] backup completed",typeCancelled:"Type [{type}] cancelled",typeFailed:"Type [{type}] failed"}},remoteControl:{directControl:"Direct Control",selectControl:"Select Control"},messageMonitor:{title:"Message Monitor",start:"Start Monitoring",stop:"Stop Monitoring",clear:"Clear",export:"Export",expand:"Expand",collapse:"Collapse",close:"Close",messageType:"Message",noMessages:"No message data",noMessagesToExport:"No message data to export",startSuccess:"Started monitoring messages",stopSuccess:"Stopped monitoring messages",clearSuccess:"Messages cleared successfully",exportSuccess:"Messages exported successfully",exportFailed:"Failed to export messages",toggleFailed:"Failed to toggle monitoring status"}}},Dt={search:{placeholder:"Search by keyword"},categories:{title:"📦IT Tools",formatting:"📝Formatting Tools",xml:"🟡XML Formatter",json:"🟡JSON Formatter",conversion:"🔄Conversion Tools",radix:"🟢Radix Converter",temperature:"🟢Temperature Converter",encryption:"🔑Encryption Tools",textEncryption:"🔵Text Encryption/Decryption"},encryption:{title:"Text Encryption/Decryption",description:"Encrypt and decrypt plaintext using encryption algorithms such as AES, TripleDES, Rabbit, or RC4",encrypt:"Encrypt",inputText:"Text to encrypt:",inputPlaceholder:"Enter the text you want to encrypt...",key:"Key:",keyPlaceholder:"Enter encryption key",algorithm:"Encryption Algorithm:",outputText:"Encrypted Text:",outputPlaceholder:"Encrypted result will appear here...",decrypt:"Decrypt",decryptInputText:"Text to decrypt:",decryptInputPlaceholder:"Enter the encrypted text to decrypt...",decryptKey:"Key:",decryptAlgorithm:"Decryption Algorithm:",decryptOutputText:"Decrypted Text:",decryptError:"Unable to decrypt text"},json:{title:"JSON Formatter",description:"Format JSON strings into a friendly readable format",sortKeys:"Sort Keys",indentSize:"Indent Size",inputLabel:"JSON to format",inputPlaceholder:"Paste your JSON...",outputLabel:"Formatted JSON",invalid:"This document does not conform to JSON specification, please check"},xml:{title:"XML Formatter",description:"Format XML strings into a friendly readable format",collapseContent:"Collapse Content:",indentSize:"Indent Size:",inputLabel:"Input XML",inputPlaceholder:"Paste your XML...",outputLabel:"Formatted XML",invalid:"This document does not conform to XML specification, please check"},temperature:{title:"Temperature Converter",description:"Convert between Kelvin, Celsius, Fahrenheit, Rankine, Delisle, Newton, Réaumur, and Rømer temperature degrees",kelvin:"Kelvin",kelvinUnit:"K",celsius:"Celsius",celsiusUnit:"°C",fahrenheit:"Fahrenheit",fahrenheitUnit:"°F",rankine:"Rankine",rankineUnit:"°R",delisle:"Delisle",delisleUnit:"°De",newton:"Newton",newtonUnit:"°N",reaumur:"Réaumur",reaumurUnit:"°Ré",romer:"Rømer",romerUnit:"°Rø"},radix:{title:"Radix Converter",description:"Convert numbers between different bases (decimal, hexadecimal, binary, octal, base64, etc.)",inputLabel:"Number to convert",inputPlaceholder:"Please enter a number (e.g.: 100)",outputLabel:"Conversion Results",binary:"Binary (2)",binaryPlaceholder:"Binary result...",octal:"Octal (8)",octalPlaceholder:"Octal result...",decimal:"Decimal (10)",decimalPlaceholder:"Decimal result...",hex:"Hexadecimal (16)",hexPlaceholder:"Hexadecimal result...",base64:"Base64 (64)",base64Placeholder:"Base64 result...",customBase:"Custom Base",customBasePlaceholder:"Base {{base}} result..."},jsonViewer:{title:"JSON Formatter",description:"Format JSON strings into a friendly readable format",sortKeys:"Sort Keys",indentSize:"Indent Size",inputJson:"JSON to format",formattedJson:"Formatted JSON",placeholder:"Paste your JSON...",validationError:"This document does not conform to JSON specification. Please check"}},Pt={matrix:yt,debug:bt,tools:Dt},Ft={checkCard:{default:"Default"},chooseModule:{title:"Select Application",noModule:"No module found!",setDefault:"Set as Default",cancel:"Cancel",confirm:"Confirm"},closer:{title:"Exit Confirmation",message:"Are you sure you want to exit?",confirm:"Confirm",minimize:"Minimize to Tray",cancel:"Cancel"},codeHighLight:{noCode:"None"},cropUpload:{title:"Image Crop",zoomIn:"Zoom In",zoomOut:"Zoom Out",rotateLeft:"Rotate Left",rotateRight:"Rotate Right",uploadImage:"Click to Upload Image",uploadTip:"Please upload an image file, recommended not to exceed 2M",cancel:"Cancel",confirm:"Confirm"},error:{forbidden:"Sorry, you do not have permission to access this page~🙅‍♂️🙅‍♀️",notFound:"Sorry, the page you visited does not exist~🤷‍♂️🤷‍♀️",serverError:"Sorry, your network is lost~🤦‍♂️🤦‍♀️",back:"Go Back"},form:{input:{placeholder:"Please fill in {label}"},select:{placeholder:"Please select {label}"},button:{add:"Add",edit:"Edit",delete:"Delete",view:"View"},search:{inputPlaceholder:"Please enter",selectPlaceholder:"Please select",rangeSeparator:"to",startPlaceholder:"Start time",endPlaceholder:"End time"}},selectIcon:{title:"Select Icon",placeholder:"Please select an icon",searchPlaceholder:"Search icon",noSearchResult:"No icon found~",moreIcons:"More icons",enterIconifyCode:"Please enter the iconify code you want, e.g. mdi:home-variant",iconifyAddress:"Iconify Address",localIcons:"Local Icons"},selector:{add:"Add",addCurrent:"Add Current",addSelected:"Add Selected",delete:"Delete",deleteCurrent:"Delete Current",deleteSelected:"Delete Selected",cancel:"Cancel",confirm:"Confirm",selected:"Selected",maxSelect:"Max Select",singleSelectOnly:"Only one can be selected",maxSelectLimit:"Up to {count} can be selected",person:"Person"},upload:{view:"View",edit:"Edit",delete:"Delete",uploadImage:"Please upload an image",uploadSuccess:"Image uploaded successfully!",uploadFailed:"Image upload failed, please re-upload!",invalidFormat:"The uploaded image does not meet the required format!",fileSizeExceeded:"The uploaded image size cannot exceed {size}M!",maxFilesExceeded:"You can upload up to {limit} images, please remove some before uploading!",fileSizeZero:"File {fileName} size is 0, cannot upload!",tips:"Tips"},treeFilter:{searchPlaceholder:"Enter keyword to filter",expandAll:"Expand All",collapseAll:"Collapse All",all:"All"},proTable:{search:{reset:"Reset",search:"Search",expand:"Expand",collapse:"Collapse"},pagination:{total:"Total {total}",pageSize:"per page",goto:"Go to",page:"Page"},colSetting:{title:"Column Settings",fixedLeft:"Show",fixedRight:"Sortable",cancelFixed:"Cancel Fixed",reset:"Restore Default",confirm:"Confirm",cancel:"Cancel"},table:{empty:"No data"}},basicComponent:{title:"Basic Components",line:"Line",text:"Text",rect:"Rectangle",circle:"Circle",ellipse:"Ellipse",triangle:"Triangle",arc:"Arc"}},Tt={equipmentList:{sequence:"Index",name:"Name",type:"Type",operation:"Operation",preview:"Preview",copy:"Copy",delete:"Delete",confirmDelete:"Are you sure to delete?",tip:"Tip",error:"Error"},graphComponent:{deviceType:"Device Type",deviceName:"Device Name",save:"Save"},contextMenu:{group:"Group",ungroup:"Ungroup",setStatus:"Set Status",copy:"Copy",delete:"Delete",rename:"Rename"},graphCreate:{needTwoDevices:"Switch or disconnector requires two device symbols selected",needCorrectStatus:"Please set the correct status property for the switch or disconnector",needOneDevice:"Please select a device symbol"},graphDefine:{waitCanvasInit:"Please wait for canvas initialization to complete",selectOneGraph:"Please select a symbol",tip:"Tip"},setStatus:{open:"Open",close:"Close",none:"None"},graphTools:{undo:"Undo",redo:"Redo",front:"Bring to Front",back:"Send to Back",delete:"Delete",save:"Save",equipmentList:"Equipment List"},graphEditor:{dataConfig:"Data Configuration",loadEquipmentFailed:"Failed to load symbol"}},Et={more:{importPathNotExists:"Import path does not exist",exportPathNotExists:"Export path does not exist",selectCorrectConfigFile:"Please select the correct configuration file",exportProjectConfigException:"Export project configuration exception",importProjectConfigException:"Import project configuration exception"}},At={...st,...ht,...St,...Pt,components:Ft,graphDefine:Tt,services:Et,Home:"Home"},Nt={language:{title:"Idioma",zh:"Chino",en:"Inglés",es:"Español",fr:"Francés",ru:"Ruso",tooltip:"Idioma"},about:{title:"Acerca de",introduction:"Introducción",description:"Una herramienta de depuración de plataforma de última generación basada en Vue3, TypeScript, Vite4, Pinia, Element-Plus, Electron, etc. desarrollado con las últimas tecnologías.",versionInfo:"Información de Versión",toolName:"Nombre de la Herramienta",version:"Número de Versión",machineCode:"Código de Máquina",loading:"Cargando...",machineCodeError:"Error al obtener",copySuccess:"Código de máquina copiado al portapapeles",copyError:"Error al copiar",versionFeatures:"Características de la Versión",features:{visualTool:"Incluye herramientas de visualización, ver información de dispositivo, configurar, simular, estado, señal remota, medir, controlar, reportar, sincronizar dispositivo, importar y exportar valor, función de depuración de variable",configTool:"Incluye herramientas de configuración para vista previa, agregar, editar, símbolo personalizado, función de asociación de información de dispositivo",themeTool:"Incluye personalización de tema, herramienta pequeña, función de importar y exportar configuración"}},footer:{copyright:"{version}"},header:{minimize:"Minimizar",maximize:"Maximizar",restore:"Restaurar",close:"Cerrar",company:{name:"Sieyuan Electric",englishName:"Sieyuan"},collapse:{expand:"Expandir Dispositivo",fold:"Colapsar Dispositivo",expandTool:"Expandir Lista de Herramientas",foldTool:"Colapsar Lista de Herramientas"},breadcrumb:{home:"Inicio"},assemblySize:{title:"Configuración de Tamaño",default:"Predeterminado",large:"Grande",small:"Pequeño"},avatar:{profile:"Perfil",switchApp:"Cambiar Aplicación",logout:"Cerrar Sesión",logoutConfirm:{title:"Aviso",message:"¿Está seguro de que desea cerrar sesión?",confirm:"Confirmar",cancel:"Cancelar"},logoutSuccess:"¡Cierre de sesión exitoso!"},changeModule:{title:"Cambiar Módulo"},enginConfig:{configType:"Tipo de Configuración",openDirectory:"Abrir Directorio de Archivos",cancel:"Cancelar",confirm:"Confirmar",all:"Todo",deviceList:"Lista de Dispositivos",configureList:"Lista de Configuración",exportSuccess:"Exportación de configuración exitosa",importSuccess:"Importación de configuración exitosa",disconnectDeviceFirst:"Por favor, desconecte primero el dispositivo conectado",overrideConfirm:"La lista de configuración ya existe, ¿desea sobrescribir?",warmTips:"Aviso",importConfigFile:"Importar archivo de configuración"},userInfo:{title:"Información del Usuario",cancel:"Cancelar",confirm:"Confirmar"},password:{title:"Cambiar Contraseña",cancel:"Cancelar",confirm:"Confirmar"},globalSetting:{title:"Config",tooltip:"Config"},moreInfo:{title:"Más",tooltip:"Más",items:{importConfig:"Importar Config",printScreen:"Captura",search:"Buscar Menú",exportConfig:"Exportar Config",about:"Acerca de",help:"Ayuda"},importConfig:{title:"Importar Configuración del Proyecto",placeholder:"Por favor, seleccione la ruta del archivo de configuración a importar"},exportConfig:{title:"Exportar Configuración del Proyecto",placeholder:"Por favor, seleccione el directorio de exportación"}},searchMenu:{placeholder:"Buscar en el menú: admite nombre y ruta del menú",empty:"Sin menú"},theme:{title:"Tema",tooltip:"Tema"}},main:{maximize:{exit:"Salir de Maximizar"}},theme:{title:"Configuración de Diseño",quickTheme:{title:"Configuración de Tema"},layoutSettings:{title:"Configuración de Diseño"},layout:{title:"Estilo de Diseño",columns:"Columnas",classic:"Clásico",transverse:"Transversal",vertical:"Vertical"},global:{title:"Tema Global",primary:"Color del Tema",dark:"Modo Oscuro",grey:"Modo Gris",weak:"Modo Débil",special:"Modo Especial"},mode:{light:"Claro",dark:"Oscuro"},interface:{title:"Configuración de Interfaz",watermark:"Marca de Agua",breadcrumb:"Miga de Pan",breadcrumbIcon:"Icono de Miga de Pan",tabs:"Barra de Pestañas",tabsIcon:"Icono de Barra de Pestañas",footer:"Pie de Página",drawerForm:"Formulario de Cajón"},presetThemes:{title:"Temas Preestablecidos",default:{name:"Tema Predeterminado",description:"Tema azul clásico"},dark:{name:"Tema Oscuro",description:"Modo oscuro para protección ocular"},techBlue:{name:"Azul Tech",description:"Azul tecnológico moderno"},deepBlue:{name:"Azul Profundo",description:"Azul océano profundo"},nature:{name:"Tema Natural",description:"Verde fresco"},forestGreen:{name:"Verde Bosque",description:"Verde bosque profundo"},warm:{name:"Tema Cálido",description:"Naranja cálido"},sunsetOrange:{name:"Naranja Atardecer",description:"Naranja atardecer cálido"},elegant:{name:"Tema Elegante",description:"Púrpura noble"},lavender:{name:"Lavanda",description:"Morado lavanda suave"},sakura:{name:"Rosa Sakura",description:"Rosa sakura romántico"},rose:{name:"Rojo Rosa",description:"Rojo rosa apasionado"},lime:{name:"Verde Lima",description:"Verde lima vibrante"},skyBlue:{name:"Azul Cielo",description:"Azul cielo claro"},eyeCare:{name:"Modo de Cuidado Ocular",description:"Tema gris para cuidado ocular"}},colors:{techBlue:{name:"Azul Tecnológico",description:"Sensación tecnológica moderna"},natureGreen:{name:"Verde Natural",description:"Fresco y natural"},vibrantOrange:{name:"Naranja Vibrante",description:"Cálido y vibrante"},elegantPurple:{name:"Púrpura Elegante",description:"Noble y elegante"},romanticPink:{name:"Rosa Romántico",description:"Suave y romántico"},freshCyan:{name:"Cian Fresco",description:"Fresco y elegante"},brightYellow:{name:"Amarillo Brillante",description:"Brillante y animado"},warmOrange:{name:"Naranja Cálido",description:"Cálido y cómodo"},limeGreen:{name:"Verde Lima",description:"Lima fresca"},deepBlue:{name:"Azul Profundo",description:"Profundo y estable"},golden:{name:"Dorado",description:"Oro clásico"},chinaRed:{name:"Rojo Chino",description:"Rojo tradicional"}}},tabs:{moreButton:{refresh:"Actualizar",closeCurrent:"Cerrar Actual",closeLeft:"Cerrar Izquierda",closeRight:"Cerrar Derecha",closeOthers:"Cerrar Otros",closeAll:"Cerrar Todo"}}},Rt={confirm:"Confirmar",cancel:"Cancelar",save:"Guardar",delete:"Eliminar",remove:"Quitar",edit:"Editar",add:"Agregar",search:"Buscar",reset:"Restablecer",export:"Exportar",import:"Importar",upload:"Subir",download:"Descargar",preview:"Vista previa",print:"Imprimir",refresh:"Actualizar",back:"Atrás",next:"Siguiente",submit:"Enviar",loading:"Cargando...",success:"Éxito",error:"Error",warning:"Advertencia",info:"Información",index:"Índice",title:"Título",operation:"Operación",execute:"Ejecutar",clear:"Limpiar",moveUp:"Subir",moveDown:"Bajar",status:{active:"Activado",inactive:"No activado",enabled:"Habilitado",disabled:"Deshabilitado",online:"En línea",offline:"Fuera de línea",pending:"Pendiente",completed:"Completado",failed:"Fallido"},time:{today:"Hoy",yesterday:"Ayer",thisWeek:"Esta semana",lastWeek:"La semana pasada",thisMonth:"Este mes",lastMonth:"El mes pasado",custom:"Rango personalizado"},pagination:{total:"Total",items:"Elementos",page:"Página",perPage:"Por página",showing:"Mostrando",to:"A",of:"De"},validation:{required:"Este campo es obligatorio",email:"Por favor, ingrese una dirección de correo electrónico válida",phone:"Por favor, ingrese un número de teléfono válido",number:"Por favor, ingrese un número válido",integer:"Por favor, ingrese un número entero válido",min:"El valor mínimo es {min}",max:"El valor máximo es {max}",length:"La longitud debe ser {length}",minLength:"La longitud mínima es {min}",maxLength:"La longitud máxima es {max}"},message:{saveSuccess:"Guardado exitosamente",deleteSuccess:"Eliminado exitosamente",updateSuccess:"Actualizado exitosamente",operationSuccess:"Operación exitosa",operationFailed:"Operación fallida",confirmDelete:"¿Está seguro de que desea eliminar?",noData:"Sin datos",loading:"Cargando...",networkError:"Error de red, por favor intente de nuevo",copySuccess:"Copiado exitosamente"},customFileSelector:{title:"Seleccionar archivos y carpetas",searchPlaceholder:"Buscar archivos o carpetas...",selectedItems:"Elementos seleccionados",clearAll:"Limpiar todo",noItemsSelected:"Ningún elemento seleccionado",cancel:"Cancelar",confirm:"Confirmar",loading:"Cargando...",error:{loadFailed:"Error al cargar",accessDenied:"Acceso denegado",notFound:"Ruta no encontrada"}},languageSyncWarning:"Error al sincronizar idioma con el backend, pero el idioma del frontend se cambió exitosamente",test:{languageSwitch:{title:"Prueba de cambio de idioma",progressDialog:"Prueba de diálogo de progreso",showProgress:"Mostrar diálogo de progreso",temperatureConverter:"Prueba de conversor de temperatura",temperatureDesc:"Los siguientes nombres de unidades de temperatura deberían actualizarse automáticamente después del cambio de idioma:",reportNames:"Prueba de nombres de informes",reportDesc:"Los siguientes nombres relacionados con informes deberían actualizarse automáticamente después del cambio de idioma:",autoRefresh:"Actualización automática",showHidden:"Mostrar elementos ocultos",instructions:"Instrucciones de prueba",step1:"Haga clic en el botón de cambio de idioma en la esquina superior derecha",step2:"Seleccione un idioma diferente (como inglés, español, francés)",step3:"Observe si el texto en la página se actualiza inmediatamente al nuevo idioma"}}},wt={loading:{checking:"Verificando autorización...",loading:"Cargando..."},auth:{invalid:"Autorización inválida: {msg}",unknownError:"Error desconocido",checkFailed:"Verificación de autorización fallida, por favor verifique la conexión de red"}},It={layout:Nt,common:Rt,app:wt},kt={dataScope:{title:"Selector de Ámbito de Datos",selectOrg:"Seleccionar Organización",orgList:"Lista de Organizaciones",cancel:"Cancelar",confirm:"Confirmar"},grantResource:{title:"Recurso de Autorización",warning:"Los roles que no son superadministradores no pueden ser autorizados para recursos de menú de módulos del sistema",firstLevel:"Directorio de Primer Nivel",menu:"Menú",buttonAuth:"Autorización de Botón",cancel:"Cancelar",confirm:"Confirmar",selectDataScope:"Por favor seleccione el ámbito de datos"}},Lt={dashboard:"Tablero",system:"Gestión del Sistema",user:"Gestión de Usuarios",role:"Gestión de Roles",menu:"Gestión de Menús",changeModule:{title:"Cambiar Módulo->",belongModule:"Módulo Perteneciente",requiredModule:"Por favor seleccione el módulo perteneciente"},debug:{title:"Debug",description:"Debug"},configure:{title:"Config",description:"Config"},tool:{title:"Tools",description:"Tools"},sysConfig:{title:"Sistema",description:"Sistema"}},zt={limit:{module:{title:"Nombre del Módulo",icon:"Ícono",status:"Estado",sort:"Orden",description:"Descripción",createTime:"Fecha de Creación",operation:"Operación",add:"Agregar Módulo",edit:"Editar Módulo",delete:"Eliminar Módulo",deleteConfirm:"Eliminar módulos seleccionados",deleteConfirmWithName:"Eliminar módulo [{name}]",form:{title:"Por favor ingrese el nombre del módulo",status:"Por favor seleccione el estado",sort:"Por favor ingrese el orden",icon:"Por favor seleccione el ícono"}},menu:{title:"Nombre del Menú",icon:"Ícono del Menú",type:"Tipo de Menú",component:"Nombre del Componente",path:"Ruta",componentPath:"Ruta del Componente",sort:"Orden",status:"Estado",description:"Descripción",operation:"Operación",add:"Agregar Menú",edit:"Editar Menú",delete:"Eliminar Menú",deleteConfirm:"Eliminar menús seleccionados",deleteConfirmWithName:"Eliminar menú [{name}]",form:{title:"Por favor ingrese el nombre del menú",parent:"Por favor seleccione el menú superior",type:"Por favor seleccione el tipo de menú",path:"Por favor ingrese la ruta",component:"Por favor ingrese la ruta del componente",sort:"Por favor ingrese el orden",icon:"Por favor seleccione el ícono",status:"Por favor seleccione el estado",link:"Por favor ingrese la dirección del enlace"}},button:{title:"Nombre del Botón",code:"Código del Botón",sort:"Orden",description:"Descripción",operation:"Operación",add:"Agregar Botón",edit:"Editar Botón",delete:"Eliminar Botón",deleteConfirm:"Eliminar botones seleccionados",deleteConfirmWithName:"Eliminar botón [{name}]",batch:{title:"Agregar Botones en Lote",shortName:"Nombre Corto del Permiso",codePrefix:"Prefijo del Código",form:{shortName:"Por favor ingrese el nombre corto del permiso",codePrefix:"Por favor ingrese el prefijo del código"}},form:{title:"Por favor ingrese el nombre del botón",code:"Por favor ingrese el código del botón",sort:"Por favor ingrese el orden"}},role:{title:"Nombre del Rol",org:"Organización",category:"Tipo de Rol",status:"Estado",sort:"Orden",description:"Descripción",createTime:"Fecha de Creación",operation:"Operación",add:"Agregar Rol",edit:"Editar Rol",delete:"Eliminar Rol",deleteConfirm:"Eliminar roles seleccionados",deleteConfirmWithName:"Eliminar rol [{name}]",grant:{resource:"Recurso de Autorización",permission:"Permiso de Autorización",dataScope:"Alcance de Datos"},form:{title:"Por favor ingrese el nombre del rol",org:"Por favor seleccione la organización",category:"Por favor seleccione el tipo de rol",status:"Por favor seleccione el estado"}},spa:{title:"Nombre de la Página Única",icon:"Ícono",type:"Tipo de Página Única",path:"Ruta",component:"Ruta del Componente",sort:"Orden",description:"Descripción",createTime:"Fecha de Creación",operation:"Operación",add:"Agregar Página Única",edit:"Editar Página Única",delete:"Eliminar Página Única",deleteConfirm:"Eliminar páginas únicas seleccionadas",deleteConfirmWithName:"Eliminar página única [{name}]",form:{title:"Por favor ingrese el nombre de la página única",type:"Por favor seleccione el tipo de página única",path:"Por favor ingrese la ruta",component:"Por favor ingrese la ruta del componente",sort:"Por favor ingrese el orden",icon:"Por favor seleccione el ícono",link:"Por favor ingrese la dirección del enlace, ej.: http://www.baidu.com"}}}},Mt={config:{title:"Configuración del Sistema",paramTitle:"Configuración de Parámetros",systemName:"Nombre del Sistema",systemVersion:"Versión del Sistema",waveToolPath:"Ruta de la Herramienta de Onda de Terceros",waveToolPathPlaceholder:"Por favor seleccione la ruta de la herramienta de onda de terceros",openDirectory:"Abrir Directorio de Archivos",save:"Guardar",reset:"Restablecer",saveSuccess:"Guardado exitosamente",selectWaveTool:"Seleccionar Herramienta de Análisis de Onda",paramRefreshTime:"Intervalo de actualización de valores fijos (ms)",reportRefreshTime:"Intervalo de actualización de informes (ms)",stateRefreshTime:"Intervalo de actualización de estado (ms)",variRefreshTime:"Intervalo de actualización de variables (ms)",configTitle:"Configuración",configKey:"Clave de Configuración",configValue:"Valor de Configuración",remark:"Observación",sortCode:"Índice",operation:"Operación",deleteConfirm:"Eliminar configuración 【{key}】"}},Vt={machineCode:"Código de Máquina",activationCode:"Código de Activación",activationCodePlaceholder:"Por favor ingrese el código de activación",reset:"Restablecer",activate:"Activar",success:{title:"Activación Exitosa",message:"El sistema se ha activado correctamente"},error:{unknown:"Error de activación: {msg}",network:"Error de activación, por favor verifique la conexión de red"}},qt={title:"Gestión de Página Única",list:{title:"Lista de Páginas Únicas",add:"Agregar Página Única",deleteSelected:"Eliminar Seleccionados",deleteConfirm:"¿Está seguro de que desea eliminar la página única {title}?"},form:{title:"{opt} Página Única",basicSettings:"Configuración Básica",functionSettings:"Configuración de Funciones",name:"Nombre de la Página Única",type:"Tipo de Página Única",icon:"Ícono",path:"Ruta",pathPlaceholder:"Por favor ingrese la ruta, ej.:/home/<USER>",componentName:"Nombre del Componente",componentPath:"Ruta del Componente",linkPath:"Dirección del Enlace",linkPathPlaceholder:"Por favor ingrese la dirección del enlace, ej.:http://www.baidu.com",sort:"Índice",description:"Descripción",isHome:"Establecer como Página Principal",isHide:"Ocultar Página",isFull:"Pantalla Completa",isAffix:"Fijar Etiqueta",isKeepAlive:"Caché de Ruta",cancel:"Cancelar",confirm:"Confirmar",nameRequired:"Por favor ingrese el nombre de la página única",typeRequired:"Por favor seleccione el tipo de página única",pathRequired:"Por favor ingrese la ruta",componentNameRequired:"Por favor ingrese el nombre del componente",componentPathRequired:"Por favor ingrese la ruta del componente",sortRequired:"Por favor ingrese el índice",iconRequired:"Por favor seleccione el ícono"}},Bt={title:"Inicio de Sesión del Sistema",account:{title:"Inicio de Sesión con Cuenta",username:"Por favor ingrese el nombre de usuario",password:"Por favor ingrese la contraseña",captcha:"Por favor ingrese el captcha",tenant:"Por favor seleccione el inquilino"},phone:{title:"Inicio de Sesión con Teléfono",phone:"Por favor ingrese el número de teléfono",smsCode:"Por favor ingrese el código SMS",getCode:"Obtener Código",machineVerify:"Verificación de Máquina",captcha:"Por favor ingrese el captcha",sendSuccess:"Código enviado exitosamente",sendFailed:"Error al enviar el código SMS"},button:{reset:"Restablecer",login:"Iniciar Sesión"},dialog:{cancel:"Cancelar",confirm:"Confirmar"}},Ot={title:"Centro de Ayuda",subtitle:"Ayuda y Documentación",catalog:"Catálogo",searchPlaceholder:"Buscar en la ayuda...",loadFail:"No se pudo cargar el documento de ayuda, por favor intente más tarde."},_t={role:kt,menu:Lt,limit:zt,sys:Mt,activate:Vt,spa:qt,login:Bt,help:Ot},Gt={configure:{remoteSet:"Teleajuste"},console:{title:"Consola",clear:"Limpiar",selectAll:"Seleccionar todo",copy:"Copiar",copySuccess:"Copiado con éxito",noTextSelected:"No hay texto seleccionado",copyFailed:"Error al copiar",clearSuccess:"Consola limpiada",collapse:"Colapsar",expand:"Expandir"},groupInfo:{title:"Información de Grupo",table:{id:"Índice",name:"Nombre",desc:"Descripción",fc:"FC",count:"Cantidad"},messages:{fetchDataError:"Error al obtener datos",fetchedData:"Datos obtenidos:"}},treeClickLog:"Clic en treeClick : ",contentView:"Vista de contenido",emptyDeviceId:"El id del dispositivo actual está vacío",invalidResponseStructure:"Estructura de respuesta inválida",formattedMenuDataLog:"Datos de menú formateados ===",allSettings:"Todos los valores",allEditSpSettings:"Todos los valores de zona única",allEditSgSettings:"Todos los valores de zona múltiple",deviceTreeDataLog:"Datos del árbol de dispositivos",failedToLoadMenu:"Error al cargar el menú del dispositivo:",innerTabs:{contentView:"Contenido",fileUpload:"Subir",fileDownload:"Descargar",deviceTime:"Sincronizar",deviceOperate:"Operación",variableDebug:"Debug",oneClickBackup:"Backup",entryConfig:"Config",tabClickLog:"Clic en pestaña:"},devices:{notConnectedAlt:"Dispositivo no conectado",pleaseConnect:"¡Por favor conecte el dispositivo primero!"},list:{unnamedDevice:"Dispositivo sin nombre",connected:"Conectado",disconnected:"Desconectado",connect:"Conectado",edit:"Editar",disconnect:"Desconectado",remove:"Eliminar",noDeviceFound:"No se encontró el dispositivo",handleClickLog:"Clic en handleListClick:",disconnectBeforeEdit:"Por favor, primero desconéctese para editar",connectSuccess:"Dispositivo {name}: Conexión exitosa",connectExist:"Dispositivo {name}: Conexión ya existe",connectFailed:"Dispositivo {name}: Conexión fallida",connectFailedReason:"Razón de falla de conexión del dispositivo:",disconnectedSuccess:"Dispositivo {name}: Desconectado",disconnectedNotify:"Dispositivo {name} conexión desconectada",currentDisconnectedNotify:"Conexión del dispositivo actual desconectada",operateFailed:"Dispositivo {name}: Operación fallida",disconnectBeforeDelete:"Por favor, primero desconéctese para eliminar",dataLog:"Datos:",ipPortExist:"El IP y el puerto ya existen, no repita la adición"},search:{placeholder:"Buscar dispositivo",ipPortExist:"El IP y el puerto ya existen, no repita la adición"},summaryPie:{other:"Otro",title:"Porcentaje de valores",subtext:"Valores de grupo de valores"},deviceInfo:{title:"Información del dispositivo",export:"Exportar",exportTitle:"Exportar información del dispositivo",exportLoading:"Exportando información básica del dispositivo...",exportSuccess:"Exportación de información básica del dispositivo exitosa",exportFailed:"Exportación de información básica del dispositivo fallida",getInfoFailed:"Error al obtener información del dispositivo. Razón: {msg}",getInfoFailedEmpty:"Error al obtener información del dispositivo. Razón: Datos vacíos!",defaultFileName:"Información del dispositivo.xlsx",confirm:"Confirmar",tip:"Sugerencia"},allParamSetting:{title:"Todos los valores",autoRefresh:"Actualización automática",refresh:"Actualizar",confirm:"Confirmar",import:"Importar",export:"Exportar",groupTitle:"Grupo de valores:",allGroups:"Todos",noDataToImport:"No hay datos para importar",importSuccess:"Importación de valores exitosa",importFailed:"Importación de valores fallida: {msg}",requestFailed:"Solicitud fallida, por favor intente más tarde",queryFailed:"Consulta de valores fallida: {msg}",unsavedChanges:"Existen cambios no guardados, ¿desea continuar actualizando?",confirmButton:"Confirmar",cancelButton:"Cancelar",alertTitle:"Sugerencia",errorTitle:"Error",noDataToConfirm:"No hay datos para confirmar",confirmSuccess:"Actualización de valores exitosa",confirmFailed:"Actualización de valores fallida: ",responseLog:"Datos de respuesta:",continueAutoRefresh:"Continuar actualización automática",settingGroup:"Grupo de valores",all:"Todos",minValue:"Valor mínimo",maxValue:"Valor máximo",step:"Paso",unit:"Unidad",searchNamePlaceholder:"Ingrese el nombre del valor para buscar",searchDescPlaceholder:"Ingrese la descripción del valor para buscar",autoRefreshWarning:"No se permite modificar datos cuando la actualización automática está habilitada",invalidValue:"El valor {name} del valor {value} no está en el rango válido",exportFileName:"Parámetros de valores del dispositivo_Todos los valores.xlsx",selectPathLog:"Seleccionar ruta: ",exportSuccess:"Exportar lista de valores exitosa"},variable:{autoRefresh:"Actualización automática",variableName:"Nombre de variable",inputVariableName:"Ingrese el nombre de la variable para agregar",refresh:"Actualizar",add:"Agregar",confirm:"Confirmar",import:"Importar",export:"Exportar",delete:"Eliminar",noDataToConfirm:"No hay datos para confirmar",warning:"Alerta",variableModifiedSuccess:"Modificación de variable exitosa",variableModifiedFailed:"Modificación de variable fallida, razón:",requestFailed:"Solicitud fallida, por favor intente más tarde",error:"Error",success:"Éxito",variableAddSuccess:"Variable de dispositivo de depuración agregada exitosamente",variableAddFailed:"Variable de dispositivo de depuración agregada fallida, razón:",variableDeleteSuccess:"Variable de dispositivo de depuración eliminada exitosamente",variableDeleteFailed:"Variable de dispositivo de depuración eliminada fallida, razón:",exportSuccess:"Exportación de información de depuración de dispositivo exitosa",exportFailed:"Exportación de información de depuración de dispositivo fallida, razón:",importSuccess:"Importación de información de depuración de dispositivo exitosa",importFailed:"Importación de información de depuración de dispositivo fallida:",confirmRefresh:"Existen cambios no guardados, ¿desea continuar actualizando?",confirmAutoRefresh:"Existen cambios no guardados, ¿desea continuar actualizando automáticamente?",pleaseInputVariableName:"Por favor, ingrese el nombre de la variable",exportTitle:"Exportar información de depuración de dispositivo",importTitle:"Importar información de depuración de dispositivo",defaultExportPath:"Información de depuración de dispositivo.xlsx",title:"Depuración de variable",variableNamePlaceholder:"Ingrese el nombre de la variable para agregar",batchDelete:"Eliminar por lotes",modifySuccess:"Modificación de variable exitosa",modifyFailed:"Modificación de variable fallida, razón: {msg}",alertTitle:"Alerta",successTitle:"Sugerencia",errorTitle:"Error",confirmButton:"Confirmar",cancelButton:"Cancelar",sequence:"Índice",name:"Nombre",value:"Valor",type:"Tipo",description:"Descripción",address:"Dirección",operation:"Operación",enterVariableName:"Ingrese el nombre de la variable para agregar",responseLog:"Datos de respuesta:",addSuccess:"Variable de dispositivo de depuración agregada exitosamente",addFailed:"Variable de dispositivo de depuración agregada fallida, razón:",addFailedWithName:"Variable {name} agregada fallida: {reason}",exportFileName:"Información de depuración de dispositivo.xlsx",selectPathLog:"Seleccionar ruta:",exportSuccessLog:"Exportación de información de depuración de dispositivo exitosa, {path}",exportFailedLog:"Exportación de información de depuración de dispositivo fallida, razón:",importFailedLog:"Importación de información de depuración de dispositivo fallida:",unsavedChanges:"Existen cambios no guardados, ¿desea continuar actualizando?",continueAutoRefresh:"Continuar actualización automática",tip:"Sugerencia",sequenceNumber:"Índice"},backup:{sequence:"Índice",title:"Copia de seguridad del dispositivo",savePath:"Ruta de guardado",setPath:"Establecer ruta de guardado",setPathTitle:"Establecer ruta",startBackup:"Iniciar copia de seguridad",cancelBackup:"Cancelar copia de seguridad",backup:"Copia de seguridad",backupType:"Tipo de copia de seguridad",progress:"Progreso",status:"Estado",operation:"Operación",backupTypes:{paramValue:"Valores de parámetros del dispositivo",faultInfo:"Informes de fallos del dispositivo",cidConfigPrjLog:"CID/CCD/Configuración del dispositivo/Información de depuración/PRJ/Registro",waveReport:"Archivos de onda del dispositivo"},backupDesc:"Descripción del contenido de la copia de seguridad",backupDescTypes:{paramValue:"Exportar valores de parámetros del dispositivo (param export.xlsx)",faultInfo:"Exportar información de fallos del dispositivo (informes de evento/operación/fallo/auditoría)",cidConfigPrjLog:"Exportar archivos de configuración (CID/CCD, configuración XML, archivos de registro)",waveReport:"Exportar archivos de onda del dispositivo (/wave/comtrade)"},locateFolder:"Ubicar carpeta",backupSuccess:"Copia de seguridad exitosa",openFolderFailed:"Error al abrir la carpeta",backupFailed:"Copia de seguridad fallida",noTypeSelected:"Por favor, seleccione primero el tipo de copia de seguridad",cancelSuccess:"Cancelación exitosa",cancelFailed:"Error al cancelar"},operate:{title:"Operación del dispositivo",manualWave:"Registrar onda manual",resetDevice:"Restablecer dispositivo",clearReport:"Limpiar informe",clearWave:"Limpiar registro de onda",executing:"Ejecutando...",selectOperation:"Por favor, seleccione la operación",success:{manualWave:"Registro de onda manual exitoso",resetDevice:"Restablecimiento de dispositivo exitoso",clearReport:"Limpiar informe exitoso",clearWave:"Limpiar registro de onda exitoso"},fail:{manualWave:"Registro de onda manual fallido, razón:",resetDevice:"Restablecimiento de dispositivo fallido, razón:",clearReport:"Limpiar informe fallido, razón:",clearWave:"Limpiar registro de onda fallido, razón:"}},time:{title:"Sincronización de tiempo del dispositivo",currentTime:"Tiempo actual",deviceTime:"Tiempo del dispositivo",selectDateTime:"Seleccionar fecha y hora",milliseconds:"Milisegundos",now:"Ahora",read:"Leer",write:"Escribir",readSuccess:"Leer tiempo del dispositivo exitoso.",readFailed:"Error al leer tiempo del dispositivo: {msg}",readFailedInvalidFormat:"Error al leer tiempo del dispositivo: Formato de tiempo no válido",readFailedDataError:"Error al leer tiempo del dispositivo: Error de formato de datos de tiempo",writeSuccess:"Escribir tiempo del dispositivo exitoso.",writeFailed:"Error al escribir tiempo del dispositivo: {msg}",writeFailedInvalidFormat:"Error al escribir tiempo del dispositivo: Formato de tiempo no válido",millisecondsRangeError:"El rango de milisegundos debe estar entre 0-999",unknownError:"Error desconocido"},reportOperate:{title:"Operación de informe",date:"Fecha:",search:"Buscar",save:"Guardar",clearList:"Limpiar lista",loading:"Cargando datos",progress:{title:"Información de progreso",loading:"Cargando",searching:"Buscando {type}"},table:{reportId:"Número de informe",name:"Nombre",time:"Tiempo",operationAddress:"Dirección de operación",operationParam:"Parámetro de operación",value:"Valor",step:"Paso",source:"Fuente",sourceType:"Tipo de fuente",result:"Resultado"},messages:{selectDateRange:"Por favor, seleccione un rango de fecha completo",noDataToSave:"No hay datos para guardar",saveSuccess:"Guardar exitoso",saveReport:"Guardar informe"}},reportGroup:{title:"Grupo de informe",date:"Fecha:",search:"Buscar",save:"Guardar",clearList:"Limpiar lista",autoRefresh:"Actualización automática",loading:"Cargando datos",progress:{title:"Información de progreso",loading:"Cargando",searching:"Buscando {type}"},table:{reportId:"Número de informe",time:"Tiempo",description:"Descripción"},contextMenu:{uploadWave:"Llamar onda",getHistoryReport:"Obtener informe histórico",saveResult:"Guardar resultado",clearContent:"Limpiar contenido de la página"},messages:{selectDateRange:"Por favor, seleccione un rango de fecha completo",noDataToSave:"No hay datos para guardar",noFileToUpload:"No hay archivo para llamar",saveSuccess:"Guardar exitoso",saveReport:"Guardar informe",waveToolNotConfigured:"No se configuró la ruta de herramienta de onda de terceros",waveFileUploading:"Llamando archivo de onda",waveFileUploadComplete:"Llamado de archivo de onda completado",waveFileUploadCompleteWithPath:"Llamado de archivo de onda completado, ruta: {path}",openWaveFileConfirm:"¿Desea abrir el archivo de onda con la herramienta de terceros?",openWaveFileTitle:"Sugerencia de precaución",confirm:"Confirmar",cancel:"Cancelar"},refresh:{stop:"Detener actualización",start:"Actualizar automáticamente"},hiddenItems:{show:"Mostrar elementos ocultos",hide:"No mostrar elementos ocultos"}},fileDownload:{title:"Descarga de archivo",deviceDirectory:"Directorio del dispositivo",reboot:"Reiniciar",noReboot:"No reiniciar",selectFile:"Seleccionar archivo",addDownloadFile:"Agregar archivo para descargar",addDownloadFolder:"Agregar carpeta para descargar",addDownloadFilesAndFolders:"Agregar archivos y carpetas",downloadFile:"Descargar archivo",cancelDownload:"Cancelar descarga",importList:"Importar lista",exportList:"Exportar lista",batchDelete:"Eliminar por lotes",clearList:"Limpiar lista",download:"Descargar",delete:"Eliminar",fileName:"Nombre de archivo",fileSize:"Tamaño de archivo",filePath:"Ruta de archivo",lastModified:"Última modificación",progress:"Progreso",status:"Estado",operation:"Operación",folder:"Carpeta",waitingDownload:"Esperando descarga",calculatingFileInfo:"Calcular información de archivo",downloadPreparing:"Preparando descarga",downloading:"Descargando......",downloadComplete:"Descarga completada",downloadError:"Error al descargar:",userCancelled:"Usuario canceló",allFilesComplete:"Descarga completada",fileExists:"El archivo {path} ya existe, no se puede agregar",selectValidFile:"Por favor, seleccione un archivo válido para la operación de descarga",remotePathEmpty:"La ruta remota no puede estar vacía",noDownloadTask:"No se pudo obtener la tarea de descarga para cancelar",fileSizeZero:"El archivo {fileName} tiene tamaño 0, no se puede descargar",downloadCancelled:"Descarga cancelada de archivo {path} completada",downloadCancelledFailed:"Descarga cancelada de archivo {path} fallida, razón: {msg}",fileDeleted:"Archivo {path} eliminado completamente",exportSuccess:"Exportación de lista de descarga de archivo exitosa",exportFailed:"Exportación de lista de descarga de archivo fallida",importSuccess:"Importación de lista de descarga de archivo exitosa",importFailed:"Importación de lista de descarga de archivo fallida: {msg}",downloadList:"Lista de archivos de descarga",exportTitle:"Exportar lista de archivos de descarga",importTitle:"Importar lista de archivos de descarga",error:"Error",tip:"Aviso",confirm:"Confirmar",sequence:"Índice",confirmButton:"Confirmar",cancelButton:"Cancelar",alertTitle:"Aviso",errorTitle:"Error",successTitle:"Éxito",warningTitle:"Advertencia",loading:"Cargando",executing:"Ejecutando...",noData:"Sin datos",selectDateRange:"Por favor seleccione el rango de fechas",search:"Buscar",save:"Guardar",clear:"Limpiar",refresh:"Actualizar",stop:"Detener",start:"Iniciar",show:"Mostrar",hide:"Ocultar",showHiddenItems:"Mostrar elementos ocultos",hideHiddenItems:"Ocultar elementos",continue:"Continuar",cancel:"Cancelar",confirmImport:"Confirmar importación",confirmExport:"Confirmar exportación",confirmDelete:"Confirmar eliminación",confirmClear:"Confirmar limpieza",confirmCancel:"Confirmar cancelación",confirmContinue:"Confirmar continuación",confirmStop:"Confirmar detención",confirmStart:"Confirmar inicio",confirmShow:"Confirmar mostrar",confirmHide:"Confirmar ocultar",confirmRefresh:"Confirmar actualización",confirmSave:"Confirmar guardado",confirmSearch:"Confirmar búsqueda",confirmClearList:"Confirmar limpieza de lista",confirmImportList:"Confirmar importación de lista",confirmExportList:"Confirmar exportación de lista",confirmBatchDelete:"Confirmar eliminación por lotes",confirmDownload:"Confirmar descarga",confirmCancelDownload:"Confirmar cancelación de descarga",confirmDeleteFile:"Confirmar eliminación de archivo",confirmClearFiles:"Confirmar limpieza de archivos",confirmImportFiles:"Confirmar importación de archivos",confirmExportFiles:"Confirmar exportación de archivos",confirmBatchDeleteFiles:"Confirmar eliminación por lotes de archivos",confirmDownloadFiles:"Confirmar descarga de archivos",confirmCancelDownloadFiles:"Confirmar cancelación de descarga de archivos",rename:"Renombrar para descarga",renamePlaceholder:"Renombrar al descargar (opcional)",renameCopyFailed:"Error al copiar archivo para renombrar:",packageProgram:"Empaquetado de programa",selectSaveDir:"Seleccionar directorio de guardado",packageBtn:"Empaquetar",locateDir:"Ubicar carpeta",saveDirEmpty:"¡Por favor seleccione primero el directorio de guardado!",packageSuccess:"¡Empaquetado de programa completado!",packageFailed:"Error de empaquetado: {msg}",noFileSelected:"¡Por favor seleccione los archivos a empaquetar!",zipPath:"Ruta del archivo ZIP: {zipPath}",addToDownload:"Agregar a descarga",fileAdded:"Archivo agregado a la lista de descarga: {path}",rebootSuccess:"Reinicio del dispositivo exitoso",rebootFailed:"Reinicio del dispositivo fallido: {msg}"},fileUpload:{serialNumber:"Índice",title:"Subida de archivo",importList:"Importar lista",exportList:"Exportar lista",batchDelete:"Eliminar por lotes",clearList:"Limpiar lista",upload:"Subir",cancelUpload:"Cancelar subida",delete:"Eliminar",sequence:"Índice",fileName:"Nombre de archivo",fileSize:"Tamaño de archivo",filePath:"Ruta de archivo",lastModified:"Última modificación",progress:"Progreso",statusTitle:"Estado",status:{waiting:"Esperando subir",preparing:"Preparando subir",uploading:"Subiendo......",completed:"Subida completada",error:"Error al subir:",cancelled:"Usuario canceló"},operation:"Operación",calculatingFileInfo:"Calcular información de archivo",uploadPreparing:"Preparando subir",uploading:"Subiendo......",uploadComplete:"Subida completada",uploadError:"Error al subir: {errorMsg}",userCancelled:"Usuario canceló",allFilesComplete:"Subida completada",fileExists:"El archivo {path} ya existe, no se puede agregar",invalidFile:"Por favor, seleccione un archivo válido para la operación de subida",emptySavePath:"La ruta de guardado de archivo no puede estar vacía",fileUploadComplete:"Archivo {fileName} subido completamente",selectPath:"Seleccionar ruta",pathOptions:{shr:"/shr",configuration:"/shr/configuration",log:"/log",wave:"/wave",comtrade:"/wave/comtrade"},deviceDirectory:"Directorio del dispositivo",savePath:"Ruta de guardado",setPath:"Establecer ruta",getFiles:"Obtener archivos",uploadFiles:"Subir archivos",errors:{invalidFile:"Por favor, seleccione un archivo válido para la operación de subida",emptySavePath:"La ruta de guardado de archivo no puede estar vacía",noUploadTask:"No se pudo obtener la tarea de subida para cancelar",getFilesFailed:"Error al obtener archivos del directorio del dispositivo",fileSizeZero:"El archivo {fileName} tiene tamaño 0, no se puede subir"},messages:{uploadCompleted:"Archivo subido completamente",uploadCancelled:"Subida cancelada completamente",clearListSuccess:"Lista de archivos limpiada exitosamente"}},info:{title:"Información del dispositivo",export:"Exportar",exportSuccess:"Exportación de información básica del dispositivo exitosa",exportFailed:"Exportación de información básica del dispositivo fallida",exportTip:"Sugerencia",confirm:"Confirmar",exportLoading:"Exportando información básica del dispositivo...",getInfoFailed:"Error al obtener información del dispositivo. Razón:",dataEmpty:"Datos vacíos!"},summary:{title:"Resumen de grupo de dispositivos",basicInfo:"Info básica",settingTotal:"Total de valores",telemetry:"Telemetría",teleindication:"Teleindicación",telecontrol:"Telecontrol",driveOutput:"Salida de accionamiento",settingRatio:"Proporción de valores"},dict:{refresh:"Actualizar",confirm:"Confirmar",import:"Importar",export:"Exportar",sequence:"Índice",shortAddress:"Dirección corta",shortAddressTooltip:"Ingrese la dirección corta para buscar",chinese:"Chino",english:"Inglés",spanish:"Español",french:"Francés",operation:"Operación",confirmLog:"Confirmar diccionario",importLog:"Importar diccionario",exportLog:"Exportar diccionario",refreshLog:"Actualizar diccionario",newValueLog:"Nuevo valor:"},allParamCompare:{title:"Comparar diferencia de importación de todos los valores",cancel:"Cancelar",confirm:"Confirmar importación",groupName:"Nombre de grupo",name:"Nombre",description:"Descripción",minValue:"Valor mínimo",maxValue:"Valor máximo",step:"Paso",unit:"Unidad",address:"Dirección",oldValue:"Valor antiguo",newValue:"Nuevo valor",sequence:"Índice",searchName:"Ingrese el nombre del valor para buscar",searchDescription:"Ingrese la descripción del valor para buscar",messages:{noSelection:"No se seleccionó ningún dato",error:"Error"}},deviceForm:{title:{add:"Agregar dispositivo",edit:"Editar dispositivo"},name:"Nombre del dispositivo",ip:"IP",port:"Puerto",connectTimeout:"Tiempo de conexión (milisegundos)",readTimeout:"Tiempo de solicitud global (milisegundos)",paramTimeout:"Tiempo de modificación de valor (milisegundos)",encrypted:"Conexión cifrada",advanced:{show:"Mostrar opciones avanzadas",hide:"Ocultar opciones avanzadas"},buttons:{cancel:"Cancelar",confirm:"Confirmar"},messages:{nameRequired:"Por favor, ingrese el nombre del dispositivo",nameTooLong:"El nombre del dispositivo no debe ser demasiado largo",invalidIp:"Por favor, ingrese una IP válida",invalidPort:"El puerto debe estar entre 1-65535",timeoutTooShort:"El tiempo de espera no debe ser demasiado corto"}},paramCompare:{title:"Comparar diferencia de importación de valor",cancel:"Cancelar",confirm:"Confirmar importación",sequence:"Índice",name:"Nombre",description:"Descripción",minValue:"Valor mínimo",maxValue:"Valor máximo",step:"Paso",unit:"Unidad",address:"Dirección",oldValue:"Valor antiguo",newValue:"Nuevo valor",searchName:"Ingrese el nombre del valor para buscar",searchDescription:"Ingrese la descripción del valor para buscar",messages:{noSelection:"No se seleccionó ningún dato",error:"Error"}},progress:{title:"Información de progreso",executing:"Ejecutando..."},remoteYm:{title:"Remoto de impulso",sequence:"Índice",shortAddress:"Dirección corta",description:"Descripción",value:"Valor",operation:"Operación",inputShortAddressFilter:"Ingrese la dirección corta para filtrar",inputDescriptionFilter:"Ingrese la descripción para filtrar",invalidData:"Valor {name} del valor {value} no válido",error:"Error",success:"Éxito",executeSuccess:"Ejecución exitosa",prompt:"Aviso",confirmButton:"Confirmar",confirmExecute:"Confirmar ejecución",confirm:"Confirmar",executeButton:"Ejecutar",cancelButton:"Cancelar"},remoteYt:{title:"Remoto de ajuste",sequence:"Índice",directControl:"Control directo",selectControl:"Control de selección",shortAddress:"Dirección corta",description:"Descripción",value:"Valor",operation:"Operación",inputShortAddressFilter:"Ingrese la dirección corta para filtrar",inputDescriptionFilter:"Ingrese la descripción para filtrar",invalidData:"Valor {name} del valor {value} no válido",error:"Error",success:"Éxito",executeSuccess:"Ejecución exitosa",prompt:"Aviso",confirm:"Confirmar",errorInfo:"Información de error",executeFailed:"Fallo en la ejecución del telecontrol remoto, razón: {msg}",executeSuccessLog:"{desc} Ejecución del telecontrol remoto exitosa",cancelSuccess:"Cancelación exitosa",cancelFailed:"Fallo en la cancelación del telecontrol remoto, razón: {msg}",selectSuccess:"Selección exitosa, ¿ejecutar?",confirmInfo:"Información de confirmación",execute:"Ejecutar",cancel:"Cancelar"},paramSetting:{title:"Valor de parámetro del dispositivo",autoRefresh:"Actualización automática",refresh:"Actualizar",confirm:"Confirmar",import:"Importar",export:"Exportar",currentEditArea:"Área de operación actual",selectEditArea:"Área de edición actual",noDataToImport:"No hay datos para importar",noDataToConfirm:"No hay datos para confirmar",importSuccess:"Importación de valor exitosa",importFailed:"Importación de valor fallida",updateSuccess:"Actualización de valor exitosa",updateFailed:"Actualización de valor fallida",requestFailed:"Solicitud fallida, por favor intente más tarde",setEditArea:"Establecer",setEditAreaTitle:"Establecer área de edición",setEditAreaSuccess:"Área de edición establecida exitosamente",modifiedWarning:"Existen cambios no guardados, ¿desea continuar actualizando?",autoRefreshWarning:"Existen cambios no guardados, ¿desea continuar actualizando automáticamente?",autoRefreshDisabled:"No se permite modificar datos cuando la actualización automática está habilitada",invalidValue:"Valor {name} del valor {value} no válido",exportSuccess:"Exportación de valor de parámetro de dispositivo exitosa",exportFailed:"Exportación de valor de parámetro de dispositivo fallida",noDiffData:"No se obtuvo datos de diferencia",table:{index:"Índice",name:"Nombre",description:"Descripción",value:"Valor",minValue:"Valor mínimo",maxValue:"Valor máximo",step:"Paso",address:"Dirección",unit:"Unidad",operation:"Operación"},search:{namePlaceholder:"Ingrese el nombre del valor para buscar",descPlaceholder:"Ingrese la descripción del valor para buscar"}},remoteControl:{title:"Control remoto",sequence:"Índice",shortAddress:"Dirección corta",description:"Descripción",control:"Control de división/combinación",type:"Tipo",operation:"Operación",directControl:"Control directo",selectControl:"Control de selección",controlClose:"Control de división",controlOpen:"Control de combinación",noCheck:"No verificar",syncCheck:"Verificar sincronización",deadCheck:"Verificar sin presión",confirmInfo:"Confirmar información",execute:"Ejecutar",cancel:"Cancelar",confirm:"Confirmar",success:"Éxito",failed:"Fallo",errorInfo:"Información de error",promptInfo:"Información de sugerencia",confirmSuccess:"Selección exitosa, ¿ejecutar?",executeSuccess:"Ejecución exitosa",cancelSuccess:"Cancelación exitosa",executeFailed:"Ejecución de control remoto fallida, razón:",cancelFailed:"Cancelación de control remoto fallida, razón:",remoteExecuteSuccess:"Ejecución de control remoto exitosa",remoteCancelSuccess:"Cancelación de control remoto exitosa"},remoteDrive:{action:"Acción",executeSuccess:"Ejecución exitosa",executeFailed:"Ejecución fallida",prompt:"Información de sugerencia",error:"Información de error",confirm:"Confirmar",shortAddress:"Dirección corta",description:"Descripción",operation:"Operación",enterToFilter:"Ingrese la dirección corta para filtrar",enterToFilterDesc:"Ingrese la descripción para filtrar",actionSuccess:"Ejecución de acción exitosa",actionFailed:"Ejecución de acción fallida",failureReason:"Razón de falla",sequence:"Índice"},remoteSignal:{autoRefresh:"Actualización automática",refresh:"Actualizar",export:"Exportar",sequence:"Índice",name:"Nombre",description:"Descripción",value:"Valor",quality:"Calidad",searchName:"Ingrese el nombre para buscar",searchDesc:"Ingrese la descripción para buscar",searchValue:"Ingrese el valor para buscar",exportTitle:"Exportar información de señal de dispositivo",exportSuccess:"Exportación de información de señal de dispositivo exitosa",exportFailed:"Exportación de información de señal de dispositivo fallida",exportSuccessWithPath:"Exportación de información de señal de dispositivo exitosa,",exportFailedWithError:"Exportación de información de señal de dispositivo fallida:",invalidData:"Datos no válidos:",errorInDataCallback:"Error en procesamiento de devolución de datos:",errorFetchingData:"Error al obtener datos:"},remoteTelemetry:{autoRefresh:"Actualización automática",refresh:"Actualizar",export:"Exportar",sequence:"Índice",name:"Nombre",description:"Descripción",value:"Valor",unit:"Unidad",quality:"Calidad",searchName:"Ingrese el nombre para buscar",searchDesc:"Ingrese la descripción para buscar",searchValue:"Ingrese el valor para buscar",exportTitle:"Exportar información de estado de dispositivo",exportSuccess:"Exportación de información de estado de dispositivo exitosa",exportFailed:"Exportación de información de estado de dispositivo fallida",exportSuccessWithPath:"Exportación de información de estado de dispositivo exitosa,",exportFailedWithError:"Exportación de información de estado de dispositivo fallida:",confirm:"Confirmar",tip:"Sugerencia",exportFileName:"Información de estado de dispositivo",selectPathLog:"Seleccionar ruta:"},remote:{directControl:"Control directo",selectControl:"Control de selección",serialNumber:"Índice",shortAddress:"Dirección corta",description:"Descripción",value:"Valor",operation:"Operación",inputShortAddressFilter:"Ingrese la dirección corta para filtrar",inputDescriptionFilter:"Ingrese la descripción para filtrar",invalidData:"Valor {name} del valor {value} no válido",error:"Error",success:"Éxito",executeSuccess:"Ejecución exitosa",prompt:"Información de sugerencia",confirm:"Confirmar",errorInfo:"Información de error",executeFailed:"Ejecución de ajuste fallida, razón: {msg}",executeSuccessLog:"{desc} Ajuste de ajuste exitoso",cancelSuccess:"Cancelación exitosa",cancelFailed:"Ejecución de ajuste fallida, razón: {msg}",selectSuccess:"Selección exitosa, ¿ejecutar?",confirmInfo:"Confirmar información",execute:"Ejecutar",cancel:"Cancelar"},report:{uploadWave:"Llamar onda",searchHistory:"Obtener informe histórico",saveResult:"Guardar resultado",clearContent:"Limpiar contenido de la página",date:"Fecha",query:"Buscar",save:"Guardar",autoRefresh:"Actualizar automáticamente",stopRefresh:"Detener actualización",clearList:"Limpiar lista",progressInfo:"Información de progreso",loading:"Cargando datos",reportNo:"Número de informe",time:"Tiempo",description:"Descripción",noFileToUpload:"No hay archivo para llamar",uploadSuccess:"Archivo de onda llamado completamente",uploadPath:"Archivo de onda llamado completamente, ruta:",noDataToSave:"No hay datos para guardar",saveSuccess:"Guardar exitoso",saveReport:"Guardar informe",openWaveConfirm:"¿Desea abrir el archivo de onda con la herramienta de terceros?",confirm:"Confirmar",cancel:"Cancelar",waveToolNotConfigured:"No se configuró la ruta de herramienta de onda de terceros",pleaseSelectTimeRange:"Por favor, seleccione un rango de fecha completo",querying:"Buscando",reportNumber:"Número de informe",operationAddress:"Dirección de operación",operationParams:"Parámetros de operación",result:"Resultado",progress:"Información de progreso",loadingText:"Cargando",selectCompleteTimeRange:"Por favor, seleccione un rango de fecha completo",fileUploading:"Llamando archivo de onda",fileUploadComplete:"Archivo llamado completamente"},customMenu:{addMenu:"Agregar menú personalizado",editMenu:"Editar menú personalizado",deleteMenu:"Eliminar menú personalizado",addReport:"Agregar informe personalizado",editReport:"Editar informe personalizado",deleteReport:"Eliminar informe personalizado",addPointGroup:"Agregar grupo personalizado (puntos)",editPointGroup:"Editar grupo personalizado (puntos)",deletePointGroup:"Eliminar grupo personalizado (puntos)",selectedPoints:"Puntos seleccionados",selectFc:"Seleccionar FC",selectGroupType:"Seleccionar tipo de grupo",groupTypes:{ST:"Señal remota",MX:"Medición remota",SP:"Configuración de zona única",SG:"Configuración de zona múltiple"},filterPlaceholder:"Filtrar por nombre/descripción",loadingData:"Cargando datos...",noDataForFc:"No hay datos para este FC",noDataForGroupType:"No hay datos para este tipo de grupo",pleaseSelectFc:"Por favor seleccione FC primero",pleaseSelectGroupType:"Por favor seleccione tipo de grupo primero",loadingGroupTypeData:"Cargando datos de tipo de grupo...",loadingGroupTypes:"Cargando datos de tipos de grupo...",loadedGroupTypes:"Tipos de grupo cargados",dataLoadComplete:"Carga de datos completa",dataLoadFailed:"Fallo en la carga de datos",switchingToGroupType:"Cambiando a",loadingGroupTypeDataSingle:"Cargando datos...",loadGroupTypeFailed:"Fallo al cargar datos",loadGroupTypeError:"Error al cargar datos",inputGroupName:"Por favor ingrese el nombre del grupo",inputGroupDesc:"Por favor ingrese la descripción del grupo",selectGroupTypeFirst:"Por favor seleccione tipo de grupo primero",menuName:"Nombre del grupo",menuDesc:"Descripción",reportName:"Nombre del informe",reportDesc:"Descripción",reportKeyword:"Palabra clave",reportInherit:"Heredar informe",inputMenuName:"Por favor ingrese el nombre del grupo",inputMenuDesc:"Por favor ingrese la descripción",inputReportName:"Por favor ingrese el nombre del informe",inputReportDesc:"Por favor ingrese la descripción",inputReportKeyword:"Por favor ingrese la palabra clave",selectReportInherit:"Por favor seleccione el informe a heredar",cancel:"Cancelar",confirm:"Confirmar",successAddMenu:"Menú personalizado agregado con éxito",successEditMenu:"Menú personalizado editado con éxito",successDeleteMenu:"Menú personalizado eliminado con éxito",successAddReport:"Informe personalizado agregado con éxito",successEditReport:"Informe personalizado editado con éxito",successDeleteReport:"Informe personalizado eliminado con éxito",successDeletePointGroup:"Grupo personalizado (puntos) eliminado con éxito",errorAction:"Operación fallida",errorDelete:"Error al eliminar",confirmDeleteMenu:"¿Está seguro de que desea eliminar este menú personalizado?",confirmDeleteReport:"¿Está seguro de que desea eliminar este informe personalizado?",confirmDeletePointGroup:"¿Está seguro de que desea eliminar este grupo personalizado (puntos)?",tip:"Sugerencia"},tree:{inputGroupName:"Por favor ingrese el nombre del grupo",expandAll:"Expandir todo",collapseAll:"Colapsar todo"}},Ut={device:{configure:{selectConfigure:"¡Por favor seleccione el archivo de símbolo de configuración!",loading:"Cargando",operating:"En operación",loadFailed:"Error al cargar, razón:",getCustomDeviceFailed:"Error al obtener dispositivo personalizado",registerDataFailed:"Error al registrar datos, razón:",variablesNotExist:"Algunas variables no existen:",getDataError:"Error al obtener datos",remoteSet:"Teleajuste",remoteControlFailed:"Telecontrol fallido, razón:",remoteControlSuccess:"Telecontrol exitoso",noRemoteControlType:"Tipo de telecontrol no configurado",symbolChangeReload:"Cambio de símbolo, recargar",deviceChangeReload:"Cambio de dispositivo, recargar",deviceConnectReload:"Dispositivo conectado con éxito, recargar"},configureList:{searchPlaceholder:"Buscar por palabra clave",deviceMonitor:"Monitor de dispositivo",newProject:"Nuevo proyecto",addCustomComponent:"Agregar componente personalizado",newConfigure:"Nueva configuración",renameProject:"Renombrar proyecto",deleteProject:"Eliminar proyecto",editConfigure:"Editar configuración",renameConfigure:"Renombrar configuración",deleteConfigure:"Eliminar configuración",openFolder:"Abrir carpeta contenedora",inputProjectName:"Por favor ingrese el nombre del proyecto (máx. 10 caracteres, sin caracteres especiales)",inputConfigureName:"Por favor ingrese el nombre de la configuración (máx. 10 caracteres, sin caracteres especiales)",confirm:"Confirmar",cancel:"Cancelar",invalidName:"Longitud o contenido del nombre no válido",projectAddSuccess:"Proyecto: {name} agregado exitosamente",projectAddFailed:"Proyecto: {name} agregado fallido, razón:",configureAddSuccess:"Configuración: {name} agregada exitosamente",configureAddFailed:"Configuración: {name} agregada fallida, razón:",renameSuccess:"Renombrado exitoso",renameFailed:"Renombrado fallido, razón:",confirmDelete:"¿Confirmar eliminación?",confirmBatchDelete:"Este proyecto tiene configuraciones asociadas, ¿confirmar eliminación por lotes?",deleteSuccess:"Eliminación exitosa",deleteFailed:"Eliminación fallida, razón:",remoteSet:"Confirmación de Eliminación"},configures:{customComponent:"Componente personalizado",selectDevice:"¡Por favor seleccione el dispositivo asociado!",edit:"Editar:"},customConfigure:{getCustomDeviceFailed:"Error al obtener dispositivo personalizado",saveDeviceFailed:"Error al guardar símbolo del dispositivo",saveSuccess:"Guardado exitosamente",deleteDeviceFailed:"Error al eliminar símbolo del dispositivo",deleteSuccess:"Eliminación exitosa",tip:"Mensaje de sugerencia"},deviceList:{unnamed:"Dispositivo Sin Nombre",connect:"Conectar",disconnect:"Desconectar",edit:"Editar",delete:"Eliminar",notFound:"No se encontró el dispositivo",editWarn:"Por favor desconecte antes de editar",deleteWarn:"Por favor desconecte antes de eliminar",connectSuccess:"Dispositivo {name}: conectado exitosamente",connectExist:"Dispositivo {name}: conexión ya existe",connectFailed:"Dispositivo {name}: conexión fallida",connectFailedReason:"Razón de falla de conexión del dispositivo: {reason}",disconnectSuccess:"Dispositivo {name}: desconectado",disconnectedNotify:"Dispositivo {name} conexión desconectada",currentDisconnectedNotify:"Conexión del dispositivo actual desconectada",operateFailed:"Dispositivo {name}: operación fallida",remove:"Eliminar"},deviceSearch:{searchPlaceholder:"Buscar dispositivo"},editConfigure:{saveSuccess:"Guardado exitosamente",saveFailed:"Guardado fallido, razón:",loadFailed:"Error al cargar, razón:",getCustomDeviceFailed:"Error al obtener dispositivo personalizado",tip:"Mensaje de sugerencia"},remoteSet:{inputValue:"Valor de entrada",write:"Escribir",cancel:"Cancelar",setFailed:"Teleajuste fallido, razón:",operateSuccess:"Operación exitosa",noSetType:"Tipo de teleajuste no configurado"}},graph:{component:{electricSymbols:"Símbolos eléctricos",customComponents:"Componentes personalizados",basicComponents:"Componentes básicos"},toolbar:{undo:"Deshacer",redo:"Rehacer",bringToFront:"Frente",sendToBack:"Atrás",ratio:"Escalado Proporcional",delete:"Eliminar",save:"Guardar"},contextMenu:{group:"Grupo",ungroup:"Desagrupar",linkData:"Datos asociados",equipmentSaddr:"Dirección del dispositivo"},dialog:{dataConfig:"Configuración de datos",tip:"Mensaje de sugerencia",selectOneGraph:"Por favor, seleccione un gráfico"},message:{waitForCanvasInit:"Por favor, espere a que se complete la inicialización del lienzo",loadEquipmentFailed:"Error al cargar gráfico",loadEquipmentError:"Error al cargar dispositivo",equipmentLoaded:"Dispositivo cargado completamente"},basic:{title:"Componentes básicos",components:{line:"Línea",text:"Texto",rectangle:"Rectángulo",circle:"Círculo",ellipse:"Elipse",triangle:"Triángulo",arc:"Arco"}},selectEquipment:{sequence:"Índice",name:"Nombre",type:"Tipo",symbol:"Símbolo",operation:"Operación",reference:"Referencia"},setSAddr:{telemetry:"Telemetría/Telemedida",format:"Formato",factor:"Factor",remoteControl:"Telecontrol",controlType:"Método de telecontrol",controlValue:"Valor de telecontrol",remoteSet:"Teleajuste",setType:"Método de teleajuste",displayConfig:"Configuración de visualización",addRow:"Agregar fila",sequence:"Índice",type:"Tipo",originalValue:"Valor original",displayValue:"Valor de visualización",operation:"Operación",text:"Texto",symbol:"Símbolo",selectSymbol:"Seleccionar símbolo",confirm:"Confirmar",cancel:"Cancelar",confirmDelete:"¿Confirmar eliminación?",tip:"Mensaje de sugerencia",selectControl:"Control de selección",directControl:"Control directo",controlClose:"Control de cierre",controlOpen:"Control de apertura",cancelDelete:"Cancelar eliminación"},equipmentType:{CBR:"Interruptor",DIS:"Corte de aislamiento",GDIS:"Corte de aislamiento de tierra",PTR2:"Transformador (Y/△-11)",PTR3:"Transformador (Y/△-11)",VTR:"Transformador de voltaje",CTR:"Transformador de corriente",EFN:"Dispositivo de aterrizaje del punto neutro",IFL:"Línea",EnergyConsumer:"Carga",GND:"Tierra",Arrester:"Pararrayos",Capacitor_P:"Capacitor en paralelo",Capacitor_S:"Capacitor en serie",Reactor_P:"Reactor en paralelo",Reactor_S:"Reactor en serie",Ascoil:"Bobina de compensación",Fuse:"Fusible",BAT:"Batería",BSH:"Tubo",CAB:"Cable",LIN:"Línea aérea",GEN:"Generador",GIL:"Línea de aislamiento eléctrico",RRC:"Componente rotativo de potencia reactiva",TCF:"Convertidor de control de frecuencia de tiristor",TCR:"Componente de control de potencia reactiva de tiristor",LTC:"Conmutador",IND:"Inductor"},equipmentName:{breaker_vertical:"Interruptor-vertical",breaker_horizontal:"Interruptor-horizontal",breaker_invalid_vertical:"Interruptor-inválido-vertical",breaker_invalid_horizontal:"Interruptor-inválido-horizontal",disconnector_vertical:"Corte-vertical",disconnector_horizontal:"Corte-horizontal",disconnector_invalid_vertical:"Corte-inválido-vertical",disconnector_invalid_horizontal:"Corte-inválido-horizontal",hv_fuse:"Fusible de alta tensión",station_transformer_2w:"Transformador de estación (dos devanados)",transformer_y_d_11:"Transformador (Y/△-11)",transformer_d_y_11:"Transformador (△/Y-11)",transformer_d_d:"Transformador (△/△)",transformer_y_y_11:"Transformador (Y/Y-11)",transformer_y_y_12_d_11:"Transformador (Y/Y-12/△-11)",transformer_y_d_11_d_11:"Transformador (Y/△-11/△-11)",transformer_y_y_v:"Transformador (Y/Y/V)",transformer_autotransformer:"Transformador (auto-transformador)",voltage_transformer_2w:"Transformador de voltaje (dos devanados)",voltage_transformer_3w:"Transformador de voltaje (tres devanados)",voltage_transformer_4w:"Transformador de voltaje (cuatro devanados)",arrester:"Pararrayos",capacitor_horizontal:"Capacitor-horizontal",capacitor_vertical:"Capacitor-vertical",reactor:"Reactor",split_reactor:"Reactor de división",power_inductor:"Inductor de potencia",feeder:"Línea",ground:"Tierra",tap_changer:"Conmutador",connection_point:"Punto de conexión",transformer_y_y_12_d_11_new:"Transformador (Y/Y-12/△-11) (nuevo)",pt:"PT",arrester_new:"Pararrayos (nuevo)",disconnector_vertical_new:"Corte-vertical (nuevo)",disconnector_horizontal_new:"Corte-horizontal (nuevo)",arrester_new_vertical:"Pararrayos (nuevo)-vertical",disconnector_vertical_left_new:"Corte-vertical izquierdo (nuevo)"}},graphProperties:{blank:{propertySetting:"Configuración de propiedad"},graph:{canvasSetting:"Configuración de lienzo",grid:"Cuadrícula",backgroundColor:"Color de fondo"},group:{groupProperty:"Propiedad de grupo",basic:"Básico",width:"Ancho",height:"Altura",x:"Posición (X)",y:"Posición (Y)",angle:"Ángulo de rotación"},node:{nodeProperty:"Propiedad de nodo",style:"Estilo",backgroundColor:"Color de fondo",borderWidth:"Ancho del borde",borderColor:"Color del borde",borderDasharray:"Estilo del borde",rx:"rx del borde",ry:"ry del borde",position:"Posición",width:"Ancho",height:"Altura",x:"Posición (X)",y:"Posición (Y)",property:"Propiedad",angle:"Ángulo de rotación",zIndex:"Nivel (z)",fontFamily:"Fuente",fontColor:"Color de fuente",fontSize:"Tamaño de fuente",text:"Texto"},pathLine:{lineSetting:"Configuración de línea",style:"Estilo",lineHeight:"Ancho",lineColor:"Color",borderDasharray:"Borde",position:"Posición",width:"Ancho",height:"Altura",x:"Posición (X)",y:"Posición (Y)",property:"Propiedad",angle:"Ángulo de rotación",zIndex:"Nivel (z)"}},business:{hmi:{title:"Gestión de pantallas",form:{add:"Nueva pantalla",edit:"Editar pantalla",view:"Ver pantalla",name:"Nombre de pantalla",type:"Tipo de pantalla",template:"Plantilla de pantalla",description:"Descripción",cancel:"Cancelar",confirm:"Confirmar",validation:{name:"Por favor ingrese el nombre de la pantalla",type:"Por favor seleccione el tipo de pantalla",template:"Por favor seleccione la plantilla de pantalla"}},columns:{name:"Nombre de pantalla",type:"Tipo de pantalla",template:"Plantilla de pantalla",createTime:"Tiempo de creación",updateTime:"Tiempo de actualización",status:"Estado",operation:"Operación"},type:{device:"Pantalla de dispositivo",process:"Pantalla de proceso",alarm:"Pantalla de alarma",custom:"Pantalla personalizada"},status:{draft:"Borrador",published:"Publicado",archived:"Archivado"},editor:{title:"Editor de pantalla",save:"Guardar",preview:"Vista previa",publish:"Publicar",cancel:"Cancelar",tools:{select:"Seleccionar",rectangle:"Rectángulo",circle:"Círculo",line:"Línea",text:"Texto",image:"Imagen",device:"Dispositivo",alarm:"Alarma",chart:"Gráfico"},properties:{title:"Propiedades",position:"Posición",size:"Tamaño",style:"Estilo",data:"Datos",event:"Evento"}},preview:{title:"Vista previa de pantalla",fullscreen:"Pantalla completa",exit:"Salir",zoom:{in:"Ampliar",out:"Reducir",fit:"Ajustar"}},publish:{title:"Publicar pantalla",version:"Número de versión",description:"Descripción de publicación",cancel:"Cancelar",confirm:"Confirmar",validation:{version:"Por favor ingrese el número de versión",description:"Por favor ingrese la descripción de publicación"}},template:{title:"Plantilla de pantalla",add:"Nueva plantilla",edit:"Editar plantilla",delete:"Eliminar plantilla",name:"Nombre de plantilla",category:"Categoría de plantilla",description:"Descripción",preview:"Vista previa",cancel:"Cancelar",confirm:"Confirmar",validation:{name:"Por favor ingrese el nombre de la plantilla",category:"Por favor seleccione la categoría de plantilla"}}}}},Wt={common:{date:"Fecha",search:"Buscar",save:"Guardar",clear:"Limpiar",loading:"Cargando...",reportNo:"Número de informe",time:"Tiempo",description:"Descripción",progress:"Progreso",selectDateRange:"Por favor seleccione el rango de fecha",noData:"Sin datos",saveSuccess:"Guardado exitosamente",saveFailed:"Error al guardar"},date:"Fecha",search:"Buscar",filter:"Filtrar",save:"Guardar",clearList:"Limpiar lista",loading:"Cargando...",reportNumber:"Número de informe",time:"Tiempo",description:"Descripción",progress:"Progreso",loadingText:"Cargando...",querying:"Buscando...",selectCompleteTimeRange:"Por favor seleccione el rango de fecha completo",noDataToSave:"No hay datos para guardar",saveSuccess:"Guardado exitosamente",saveReport:"Guardar informe",fileUploading:"Subiendo archivo",fileUploadComplete:"Archivo subido completamente",autoRefresh:"Actualización automática",showHiddenItems:"Mostrar elementos ocultos",hideHiddenItems:"Ocultar elementos ocultos",name:"Nombre",operationAddress:"Dirección de operación",operationParams:"Parámetros de operación",value:"Valor",step:"Paso",source:"Fuente",sourceType:"Tipo de fuente",result:"Resultado",searchType:"Tipo de búsqueda",total:"Total {num} registros",sameSearch:"Búsqueda de contenido similar",sameFilter:"Filtrado de contenido similar",showHideTime:"Mostrar/Ocultar columna de tiempo",selectRowToOperate:"Por favor seleccione la fila a operar",trip:{autoRefresh:"Actualización automática"},operate:{name:"Nombre",operateAddress:"Dirección de operación",operateParam:"Parámetro de operación",value:"Valor",step:"Paso",source:"Fuente",sourceType:"Tipo de fuente",result:"Resultado"},group:{uploadWave:"Subir onda",searchHistory:"Buscar historial",saveResult:"Guardar resultado",clearContent:"Limpiar contenido",contextMenu:{uploadWave:"Subir onda",getHistoryReport:"Obtener informe histórico",saveResult:"Guardar resultado",clearContent:"Limpiar contenido"},date:"Fecha",search:"Buscar",save:"Guardar",clearList:"Limpiar lista",loading:"Cargando...",table:{reportId:"ID de informe",time:"Tiempo",description:"Descripción"},progress:{title:"Progreso",searching:"Buscando {type}",loading:"Cargando..."},refresh:{start:"Iniciar actualización",stop:"Detener actualización"},hiddenItems:{show:"Mostrar elementos ocultos"},messages:{noFileToUpload:"No hay archivo para subir",selectDateRange:"Por favor seleccione el rango de fecha",noDataToSave:"No hay datos para guardar",saveReport:"Guardar informe",saveSuccess:"Guardado exitosamente"}},entryID:"Índice",module:"Nombre del módulo",msg:"Contenido",level:"Nivel",type:"Tipo",origin:"Origen",user:"Nombre de usuario",exporting:"Exportando...",stopRefresh:"Detener Actualización",searchProgress:"Buscando {type}",pleaseSelectSavePath:"Por favor seleccione la ruta de guardado...",saveFailed:"Guardado fallido",exportLogSuccess:"Exportación exitosa: {path}",exportLogFailed:"Exportación fallida: {msg}",exportLogCancelled:"Usuario canceló la operación de exportación"},jt={device:Gt,hmi:Ut,report:Wt},Ht={common:{add:"Agregar",index:"Índice",delete:"Eliminar",clear:"Limpiar",import:"Importar",export:"Exportar",execute:"Ejecutar",moveUp:"Mover arriba",moveDown:"Mover abajo",loading:"Cargando...",success:"Éxito",failed:"Fallido",confirm:"Confirmar",cancel:"Cancelar",yes:"Sí",no:"No",operation:"Operación",tips:"Consejo",title:"Aviso"},search:{placeholder:"Buscar función"},functionList:{unnamedDevice:"Dispositivo sin nombre",batchDownload:{name:"Descarga por lotes",desc:"Descarga de archivos de múltiples dispositivos e importación de valores por lotes"},xmlFormatter:{name:"Formato XML",desc:"Organización jerárquica rápida de datos XML"},jsonFormatter:{name:"Formato JSON",desc:"Formato inteligente de datos JSON, con validación de sintaxis"},radixConverter:{name:"Conversión de Base",desc:"Conversión entre binario, decimal, hexadecimal y otras bases"},temperatureConverter:{name:"Conversión de Temperatura",desc:"Conversión inteligente entre Celsius, Fahrenheit, Kelvin y otras unidades de temperatura"},encryption:{name:"Encriptación de Texto",desc:"Encriptación y desencriptación rápida de texto basada en algoritmos como AES, RSA, Base64, etc."},packageProgram:{name:"Empaquetado de programa",desc:"Empaqueta los archivos de ejecución del dispositivo para exportar, soporta directorio de guardado personalizado y ubicación de carpeta con un clic"}},matrixContent:{loading:"Cargando...",tabs:{deviceList:"Dispositivos",downloadConfig:"Archivos",paramConfig:"Parámetros"}},taskSteps:{connect:"Conectar",download:"Descargar",import:"Importar",disconnect:"Desconectar",complete:"Completar"},messages:{connectDevice:"Conectar Dispositivo",executeFileDownload:"Ejecutar Descarga de Archivo",downloadingFile:"Descargando Archivo",downloadFileFailed:"Descarga de Archivo Falló",fileDownloadCompleted:"Descarga de Archivo Completada",executeParamImport:"Ejecutar Importación de Parámetros",paramValidationFailed:"Validación de Parámetros Falló",paramImportFailed:"Importación de Parámetros Falló",paramImportCompleted:"Importación de Parámetros Completada",taskCompleted:"Tarea Completada",deviceConnectionFailed:"Conexión de Dispositivo Falló",deviceRebootSuccess:"Reinicio de Dispositivo Exitoso"},deviceList:{title:"Dispositivos",deviceListExcel:"Lista de Dispositivos.xlsx",exportDeviceList:"Exportar Lista de Dispositivos",importDeviceList:"Importar Lista de Dispositivos",exportSuccess:"Lista de dispositivos exportada correctamente",exportFail:"Error al exportar la lista de dispositivos",importSuccess:"Lista de dispositivos importada correctamente",importFail:"Error al importar la lista de dispositivos",exportSuccessMsg:"Lista de dispositivos exportada correctamente",exportFailMsg:"Error al exportar la lista de dispositivos",importSuccessMsg:"Lista de dispositivos importada correctamente",importFailMsg:"Error al importar la lista de dispositivos: {msg}",deviceName:"Nombre del Dispositivo",deviceAddress:"Dirección del Dispositivo",devicePort:"Puerto del Dispositivo",isEncrypted:"¿Encriptado?",encrypted:"Encriptado",notEncrypted:"No encriptado",status:"Estado",operation:"Operación",reboot:"Reiniciar",noReboot:"No Reiniciar",addDevice:"Agregar Dispositivo",deleteDevice:"Eliminar Dispositivo",clearDevices:"Limpiar Dispositivos",deviceExists:"El dispositivo ya existe",deviceDeleted:"Dispositivo {ip} eliminado",downloadFile:"¿Descargar archivo?",importParam:"¿Importar parámetro?",connectTimeout:"Tiempo de Espera de Conexión",paramTimeout:"Tiempo de Espera de Modificación de Parámetro",readTimeout:"Tiempo de Espera de Solicitud Global",progress:"Progreso"},downList:{title:"Descarga",deviceDirectory:"Directorio del Dispositivo",fileName:"Nombre del Archivo",fileSize:"Tamaño del Archivo",filePath:"Ruta del Archivo",lastModified:"Última Modificación",addFile:"Agregar archivo a descargar",addFolder:"Agregar carpeta a descargar",fileExists:"¡El archivo {path} ya existe, no se pudo agregar!",fileDeleted:"Archivo {path} eliminado",filesDeleted:"Archivos eliminados",defaultExportFileName:"Lista de Archivos Descargados.xlsx",exportTitle:"Exportar Lista de Archivos Descargados",importTitle:"Importar Lista de Archivos Descargados",exportSuccess:"Lista de archivos exportada correctamente",exportFailed:"Error al exportar la lista de archivos",importSuccess:"Lista de archivos importada correctamente",importFailed:"Error al importar la lista de archivos",fileExistsMsg:"El archivo {path} ya existe, no se pudo agregar!"},paramList:{title:"Parámetros",paramGroup:"Grupo de Valores",groupName:"Nombre del Grupo",paramName:"Nombre del Parámetro",paramDesc:"Descripción del Parámetro",paramValue:"Valor del Parámetro",minValue:"Valor Mínimo",maxValue:"Valor Máximo",step:"Paso",unit:"Unidad",searchParamName:"Nombre del parámetro",searchParamDesc:"Descripción del parámetro",importSuccess:"Importación de valores de parámetros exitosa",importFailed:"Error al importar valores de parámetros",exportSuccess:"Exportación de la lista de valores exitosa",exportFailed:"Error al exportar la lista de valores",clearSuccess:"Lista de valores limpiada exitosamente"},progressDialog:{title:"Procesando...",pleaseWait:"Por favor, espere..."},packageProgram:{saveDir:"Directorio de guardado",selectSaveDir:"Seleccionar directorio de guardado",packageBtn:"Empaquetar",locateDir:"Ubicar carpeta",delete:"Eliminar",sequence:"N.º",fileName:"Nombre de archivo",fileSize:"Tamaño de archivo",filePath:"Ruta de archivo",lastModified:"Última modificación",operation:"Operación",saveDirEmpty:"¡Por favor seleccione primero el directorio de guardado!",packageSuccess:"¡Empaquetado del programa completado!",tip:"Aviso",confirmButton:"Aceptar",defaultExportFileName:"Lista de archivos del paquete de programa.xlsx",exportTitle:"Exportar lista de archivos del paquete de programa",importTitle:"Importar lista de archivos del paquete de programa",exportSuccess:"Lista de archivos exportada correctamente",exportFailed:"Error al exportar la lista de archivos",importSuccess:"Lista de archivos importada correctamente",importFailed:"Error al importar la lista de archivos",fileExists:"¡El archivo {path} ya existe, no se pudo agregar!",selectDirSuccess:"Directorio seleccionado: {dir}",locateDirSuccess:"Directorio localizado: {dir}",addFileStart:"Abriendo selector de archivos...",addFileSuccess:"{count} archivos/carpetas añadidos correctamente",addFileNone:"No se añadieron archivos/carpetas nuevos",deleteSuccess:"{count} archivos/carpetas eliminados correctamente",clearSuccess:"Todos los archivos/carpetas han sido eliminados",moveUpSuccess:"Movido arriba: {name}",moveDownSuccess:"Movido abajo: {name}",noFileSelected:"¡Por favor seleccione primero los archivos a empaquetar!",noDeviceSelected:"¡Por favor seleccione primero los dispositivos a empaquetar!",packageFailed:"Error de empaquetado: {msg}",zipPath:"Ruta de empaquetado: {zipPath}",openFileButton:"Abrir archivo"}},$t={search:{placeholder:"Buscar dispositivo",button:"Buscar",success:"Búsqueda exitosa"},device2:{search:{placeholder:"Buscar equipo",add:"Agregar equipo",duplicate:"La IP y el puerto ya existen, no agregue repetidamente"},list:{empty:"No se encontraron dispositivos",unnamed:"Dispositivo sin nombre",status:{connected:"Conectado",disconnected:"Desconectado"},contextMenu:{connect:"Conectar",edit:"Editar",disconnect:"Desconectar",remove:"Eliminar"},message:{disconnectFirst:"Por favor, desconecte primero antes de editar",disconnectFirstDelete:"Por favor, desconecte primero antes de eliminar",connectSuccess:"Dispositivo {name}: conexión exitosa",connectExists:"Dispositivo {name}: la conexión ya existe",connectFailed:"Dispositivo {name}: error de conexión",connectFailedReason:"Error de conexión del dispositivo: {reason}",disconnected:"Dispositivo {name}: desconectado",operationFailed:"Dispositivo {name}: operación fallida"}},report:{group:{openWaveConfirm:"¿Abrir el archivo de forma de onda con una herramienta de terceros?",tips:"Consejo",noWaveTool:"Ruta de la herramienta de forma de onda de terceros no configurada"},common:{selectRow:"Por favor, seleccione la fila a operar"}},backup:{savePath:"Ruta de Guardado",setPath:"Por favor establezca la ruta de respaldo",setPathTitle:"Establecer ruta de respaldo",locateFolder:"Localizar Carpeta",startBackup:"Iniciar Respaldo",cancelBackup:"Cancelar Respaldo",backup:"Respaldo",sequence:"Secuencia",backupType:"Tipo de Respaldo",backupDesc:"Descripción del Respaldo",progress:"Progreso",status:"Estado",noTypeSelected:"Por favor seleccione el tipo de respaldo",backupSuccess:"Respaldo exitoso",backupFailed:"Respaldo fallido",openFolderFailed:"Error al abrir carpeta",backupTypes:{paramValue:"Valores de Parámetros",faultInfo:"Información de Fallas",cidConfigPrjLog:"Registro de Proyecto de Configuración CID",waveReport:"Reporte de Ondas"},backupDescTypes:{paramValue:"Respaldar todos los valores de configuración de parámetros del dispositivo",faultInfo:"Respaldar información de registro de fallas del dispositivo",cidConfigPrjLog:"Respaldar archivos de configuración CID y registros de proyecto",waveReport:"Respaldar archivos de reporte de análisis de forma de onda"},backupStatus:{userCancelled:"Cancelado por Usuario",transferring:"Transfiriendo"},console:{pathNotSet:"Ruta de respaldo no establecida, no se puede iniciar el respaldo",noTypeSelected:"Ningún tipo de respaldo seleccionado, no se puede iniciar el respaldo",startBackup:"Iniciar respaldo, tipos: {types}, ruta: {path}",backupException:"Excepción de respaldo: {error}",pathSelected:"Ruta de respaldo seleccionada: {path}",pathNotSelected:"Ninguna ruta de respaldo seleccionada",pathNotSetForLocate:"Ruta de respaldo no establecida, no se puede localizar la carpeta",folderOpened:"Carpeta de respaldo abierta: {path}",openFolderFailed:"Error al abrir carpeta de respaldo: {error}",taskCompleted:"Tarea completada",taskCancelled:"Tarea cancelada",typeError:"Tipo [{type}] error: {error}",typeCompleted:"Tipo [{type}] respaldo completado",typeCancelled:"Tipo [{type}] cancelado",typeFailed:"Tipo [{type}] fallido"}},remoteControl:{directControl:"Control Directo",selectControl:"Control Selectivo"}}},Yt={search:{placeholder:"Buscar por palabra clave"},categories:{title:"📦Herramientas IT",formatting:"📝Herramientas de Formato",xml:"🟡Formato XML",json:"🟡Formato JSON",conversion:"🔄Herramientas de Conversión",radix:"🟢Conversión de Base",temperature:"🟢Conversión de Temperatura",encryption:"🔑Herramientas de Encriptación",textEncryption:"🔵Encriptación de Texto"},encryption:{title:"Encriptación de Texto",description:"Encripta y desencripta texto plano usando algoritmos como AES, TripleDES, Rabbit o RC4",encrypt:"Encriptar",inputText:"Texto a encriptar:",inputPlaceholder:"Ingrese el texto que necesita encriptar...",key:"Clave:",keyPlaceholder:"Ingrese la clave de encriptación",algorithm:"Algoritmo de encriptación:",outputText:"Texto encriptado:",outputPlaceholder:"El resultado de la encriptación se mostrará aquí...",decrypt:"Desencriptar",decryptInputText:"Texto a desencriptar:",decryptInputPlaceholder:"Ingrese el texto cifrado que necesita desencriptar...",decryptKey:"Clave:",decryptAlgorithm:"Algoritmo de desencriptación:",decryptOutputText:"Texto desencriptado:",decryptError:"No se puede desencriptar el texto"},json:{title:"Formato JSON",description:"Formatea cadenas JSON para una visualización legible",sortKeys:"Ordenar",indentSize:"Sangría",inputLabel:"JSON a formatear",inputPlaceholder:"Pega tu JSON...",outputLabel:"JSON formateado",invalid:"El documento no cumple con el estándar JSON, por favor verifique"},xml:{title:"Formato XML",description:"Formatea cadenas XML para una visualización legible",collapseContent:"Colapsar contenido:",indentSize:"Tamaño de sangría:",inputLabel:"Ingresar XML",inputPlaceholder:"Pega tu XML...",outputLabel:"XML formateado",invalid:"El documento no cumple con el estándar XML, por favor verifique"},temperature:{title:"Conversión de Temperatura",description:"Conversión entre Kelvin, Celsius, Fahrenheit, Rankine, Delisle, Newton, Réaumur y Rømer",kelvin:"Kelvin",kelvinUnit:"K",celsius:"Celsius",celsiusUnit:"°C",fahrenheit:"Fahrenheit",fahrenheitUnit:"°F",rankine:"Rankine",rankineUnit:"°R",delisle:"Delisle",delisleUnit:"°De",newton:"Newton",newtonUnit:"°N",reaumur:"Réaumur",reaumurUnit:"°Ré",romer:"Rømer",romerUnit:"°Rø"},radix:{title:"Conversión de Base",description:"Convierte números entre diferentes bases (decimal, hexadecimal, binario, octal, base64, etc.)",inputLabel:"Número a convertir",inputPlaceholder:"Ingrese un número (ej: 100)",outputLabel:"Resultado de conversión",binary:"Binario (2)",binaryPlaceholder:"Resultado binario...",octal:"Octal (8)",octalPlaceholder:"Resultado octal...",decimal:"Decimal (10)",decimalPlaceholder:"Resultado decimal...",hex:"Hexadecimal (16)",hexPlaceholder:"Resultado hexadecimal...",base64:"Base64 (64)",base64Placeholder:"Resultado Base64...",customBase:"Base personalizada",customBasePlaceholder:"Resultado Base {{base}}..."},jsonViewer:{title:"Formato JSON",description:"Formatea cadenas JSON para una visualización legible",sortKeys:"Ordenar",indentSize:"Sangría",inputJson:"JSON a formatear",formattedJson:"JSON formateado",placeholder:"Pega tu JSON...",validationError:"El documento no cumple con el estándar JSON. Por favor verifique"}},Kt={matrix:Ht,debug:$t,tools:Yt},Jt={checkCard:{default:"Predeterminado"},chooseModule:{title:"Seleccionar Aplicación",noModule:"¡No se encontró módulo!",setDefault:"Establecer como Predeterminado",cancel:"Cancelar",confirm:"Confirmar"},closer:{title:"Confirmación de Salida",message:"¿Está seguro de que desea salir?",confirm:"Confirmar",minimize:"Minimizar a la Bandeja",cancel:"Cancelar"},codeHighLight:{noCode:"Ninguno"},cropUpload:{title:"Recorte de Imagen",zoomIn:"Acercar",zoomOut:"Alejar",rotateLeft:"Girar a la Izquierda",rotateRight:"Girar a la Derecha",uploadImage:"Haga clic para Subir Imagen",uploadTip:"Por favor suba un archivo de imagen, se recomienda que no exceda 2M",cancel:"Cancelar",confirm:"Confirmar"},error:{forbidden:"Lo siento, no tiene permiso para acceder a esta página~🙅‍♂️🙅‍♀️",notFound:"Lo siento, la página que visitó no existe~🤷‍♂️🤷‍♀️",serverError:"Lo siento, su red se ha perdido~🤦‍♂️🤦‍♀️",back:"Volver"},form:{input:{placeholder:"Por favor complete {label}"},select:{placeholder:"Por favor seleccione {label}"},button:{add:"Agregar",edit:"Editar",delete:"Eliminar",view:"Ver"},search:{inputPlaceholder:"Por favor ingrese",selectPlaceholder:"Por favor seleccione",rangeSeparator:"a",startPlaceholder:"Hora de inicio",endPlaceholder:"Hora de fin"}},selectIcon:{title:"Seleccionar Icono",placeholder:"Por favor seleccione un icono",searchPlaceholder:"Buscar icono",noSearchResult:"No se encontró el icono~",moreIcons:"Más iconos",enterIconifyCode:"Por favor ingrese el código iconify que desea, por ejemplo mdi:home-variant",iconifyAddress:"Dirección Iconify",localIcons:"Iconos Locales"},selector:{add:"Agregar",addCurrent:"Agregar Actual",addSelected:"Agregar Seleccionado",delete:"Eliminar",deleteCurrent:"Eliminar Actual",deleteSelected:"Eliminar Seleccionado",cancel:"Cancelar",confirm:"Confirmar",selected:"Seleccionado",maxSelect:"Selección Máxima",singleSelectOnly:"Solo se puede seleccionar uno",maxSelectLimit:"Se pueden seleccionar hasta {count}",person:"Persona"},upload:{view:"Ver",edit:"Editar",delete:"Eliminar",uploadImage:"Por favor suba una imagen",uploadSuccess:"¡Imagen subida exitosamente!",uploadFailed:"Error al subir la imagen, por favor vuelva a subirla!",invalidFormat:"¡La imagen subida no cumple con el formato requerido!",fileSizeExceeded:"¡El tamaño de la imagen subida no puede exceder {size}M!",maxFilesExceeded:"Solo puede subir hasta {limit} imágenes, por favor elimine algunas antes de subir!",fileSizeZero:"El archivo {fileName} tiene tamaño 0, ¡no se puede subir!",tips:"Consejos"},treeFilter:{searchPlaceholder:"Ingrese palabra clave para filtrar",expandAll:"Expandir Todo",collapseAll:"Colapsar Todo",all:"Todo"},proTable:{search:{reset:"Restablecer",search:"Buscar",expand:"Expandir",collapse:"Colapsar"},pagination:{total:"Total {total}",pageSize:"por página",goto:"Ir a",page:"Página"},colSetting:{title:"Configuración de Columnas",fixedLeft:"Mostrar",fixedRight:"Ordenable",cancelFixed:"Cancelar Fijación",reset:"Restaurar Predeterminado",confirm:"Confirmar",cancel:"Cancelar"},table:{empty:"Sin datos"}},basicComponent:{title:"Componentes Básicos",line:"Línea",text:"Texto",rect:"Rectángulo",circle:"Círculo",ellipse:"Elipse",triangle:"Triángulo",arc:"Arco"}},Xt={equipmentList:{sequence:"Índice",name:"Nombre",type:"Tipo",operation:"Operación",preview:"Vista previa",copy:"Copiar",delete:"Eliminar",confirmDelete:"¿Confirmar eliminación?",tip:"Mensaje de sugerencia",error:"Error"},graphComponent:{deviceType:"Tipo de dispositivo",deviceName:"Nombre del dispositivo",save:"Guardar"},contextMenu:{group:"Grupo",ungroup:"Desagrupar",setStatus:"Establecer estado",copy:"Copiar",delete:"Eliminar",rename:"Renombrar"},graphCreate:{needTwoDevices:"Se necesita seleccionar dos gráficos de dispositivo para la operación de interruptor o cortacircuito",needCorrectStatus:"Por favor, establezca el atributo de estado correcto para el interruptor o cortacircuito",needOneDevice:"Por favor, seleccione un gráfico de dispositivo"},graphDefine:{waitCanvasInit:"Por favor, espere a que se complete la inicialización del lienzo",selectOneGraph:"Por favor, seleccione un símbolo",tip:"Sugerencia"},setStatus:{open:"Abrir",close:"Cerrar",none:"Ninguno"},graphTools:{undo:"Deshacer",redo:"Rehacer",front:"Frente",back:"Atrás",delete:"Eliminar",save:"Guardar",equipmentList:"Lista de Equipos"},graphEditor:{dataConfig:"Configuración de datos",loadEquipmentFailed:"Error al cargar gráfico"}},Qt={more:{importPathNotExists:"La ruta de importación no existe",exportPathNotExists:"La ruta de exportación no existe",selectCorrectConfigFile:"Por favor seleccione el archivo de configuración correcto",exportProjectConfigException:"Excepción al exportar configuración del proyecto",importProjectConfigException:"Excepción al importar configuración del proyecto"}},Zt={...It,..._t,...jt,...Kt,components:Jt,graphDefine:Xt,services:Qt,Home:"Inicio"},ea={language:{title:"Langue",zh:"Chinois simplifié",en:"Anglais",es:"Espagnol",fr:"Français",ru:"Russe",tooltip:"Multilingue"},about:{title:"À propos",introduction:"Introduction",description:"Outil de débogage de plateforme haute performance de nouvelle génération développé avec les dernières technologies : Vue3, TypeScript, Vite4, Pinia, Element-Plus, Electron, etc.",versionInfo:"Informations de version",toolName:"Nom de l'outil",version:"Numéro de version",machineCode:"Code Machine",loading:"Chargement...",machineCodeError:"Échec de récupération",copySuccess:"Code machine copié dans le presse-papiers",copyError:"Échec de la copie",versionFeatures:"Caractéristiques de version",features:{visualTool:"Inclut les fonctions de connexion d'outils visuels, visualisation d'informations d'appareils, paramètres, valeurs analogiques, états, télécommunications, télémétrie, télécommande, rapports, synchronisation d'appareils, import/export de valeurs fixes, débogage de variables",configTool:"Inclut les fonctions d'aperçu d'outils de configuration, ajout, édition, icônes personnalisées, association d'informations d'appareils",themeTool:"Inclut les fonctions de personnalisation de thème, petits outils IT, import/export de configuration d'appareils"}},footer:{copyright:"{version}"},header:{minimize:"Minimiser",maximize:"Maximiser",restore:"Restaurer",close:"Fermer",company:{name:"Sieyuan Electric",englishName:"Sieyuan"},collapse:{expand:"Développer les appareils",fold:"Réduire les appareils",expandTool:"Développer la liste des outils",foldTool:"Réduire la liste des outils"},breadcrumb:{home:"Accueil"},assemblySize:{title:"Paramètres de taille",default:"Par défaut",large:"Grand",small:"Petit"},avatar:{profile:"Centre personnel",switchApp:"Changer d'application",logout:"Se déconnecter",logoutConfirm:{title:"Conseils",message:"Êtes-vous sûr de vouloir vous déconnecter ?",confirm:"Confirmer",cancel:"Annuler"},logoutSuccess:"Déconnexion réussie !"},changeModule:{title:"Changer de module"},enginConfig:{configType:"Type de configuration",openDirectory:"Ouvrir le répertoire de fichiers",cancel:"Annuler",confirm:"Confirmer",all:"Tout",deviceList:"Liste des appareils",configureList:"Liste de configuration",exportSuccess:"Export de configuration réussi",importSuccess:"Import de configuration réussi",disconnectDeviceFirst:"Veuillez d'abord déconnecter les appareils connectés",overrideConfirm:"La liste de configuration existe déjà, voulez-vous la remplacer ?",warmTips:"Conseils",importConfigFile:"Importer le fichier de configuration"},userInfo:{title:"Informations personnelles",cancel:"Annuler",confirm:"Confirmer"},password:{title:"Modifier le mot de passe",cancel:"Annuler",confirm:"Confirmer"},globalSetting:{title:"Paramètres",tooltip:"Paramètres"},moreInfo:{title:"Plus",tooltip:"Plus",items:{importConfig:"Importer Config",printScreen:"Capture",search:"Recherche Menu",exportConfig:"Exporter Config",about:"À propos",help:"Aide"},importConfig:{title:"Importer la configuration du projet",placeholder:"Veuillez sélectionner le chemin du fichier de configuration à importer"},exportConfig:{title:"Exporter la configuration du projet",placeholder:"Veuillez sélectionner le répertoire d'export"}},searchMenu:{placeholder:"Recherche de menu : supporte le nom et le chemin du menu",empty:"Aucun menu"},theme:{title:"Thème",tooltip:"Thème"}},main:{maximize:{exit:"Quitter la maximisation"}},theme:{title:"Paramètres de mise en page",quickTheme:{title:"Paramètres de thème"},layoutSettings:{title:"Paramètres de mise en page"},layout:{title:"Style de mise en page",columns:"Colonnes",classic:"Classique",transverse:"Transversal",vertical:"Vertical"},global:{title:"Thème global",primary:"Couleur du thème",dark:"Mode sombre",grey:"Mode gris",weak:"Mode daltonien",special:"Mode spécial"},mode:{light:"Clair",dark:"Sombre"},interface:{title:"Paramètres d'interface",watermark:"Filigrane",breadcrumb:"Fil d'Ariane",breadcrumbIcon:"Icône du fil d'Ariane",tabs:"Barre d'onglets",tabsIcon:"Icône de la barre d'onglets",footer:"Pied de page",drawerForm:"Formulaire en tiroir"},presetThemes:{title:"Thèmes prédéfinis",default:{name:"Thème par défaut",description:"Thème bleu classique"},dark:{name:"Thème sombre",description:"Mode sombre pour les yeux"},techBlue:{name:"Bleu Tech",description:"Bleu technologique moderne"},deepBlue:{name:"Bleu Profond",description:"Bleu océan profond"},nature:{name:"Thème naturel",description:"Système vert frais"},forestGreen:{name:"Vert Forêt",description:"Vert forêt profond"},warm:{name:"Thème chaleureux",description:"Système orange chaleureux"},sunsetOrange:{name:"Orange Coucher",description:"Orange coucher de soleil"},elegant:{name:"Thème élégant",description:"Système violet noble"},lavender:{name:"Lavande",description:"Violet lavande doux"},sakura:{name:"Rose Sakura",description:"Rose sakura romantique"},rose:{name:"Rouge Rose",description:"Rouge rose passionné"},lime:{name:"Vert Citron",description:"Vert citron vibrant"},skyBlue:{name:"Bleu Ciel",description:"Bleu ciel clair"},eyeCare:{name:"Mode soin des yeux",description:"Thème gris pour les yeux"}},colors:{techBlue:{name:"Bleu technologique",description:"Sensation technologique moderne"},natureGreen:{name:"Vert naturel",description:"Fraîcheur naturelle"},vibrantOrange:{name:"Orange vibrant",description:"Chaleur et vitalité"},elegantPurple:{name:"Violet élégant",description:"Noble et élégant"},romanticPink:{name:"Rose romantique",description:"Doux et romantique"},freshCyan:{name:"Cyan frais",description:"Fraîcheur et élégance"},brightYellow:{name:"Jaune vif",description:"Vif et dynamique"},warmOrange:{name:"Orange chaleureux",description:"Chaleureux et confortable"},limeGreen:{name:"Vert citron vert",description:"Citron vert frais"},deepBlue:{name:"Bleu profond",description:"Profond et stable"},golden:{name:"Doré",description:"Doré classique"},chinaRed:{name:"Rouge chinois",description:"Rouge traditionnel"}}},tabs:{moreButton:{refresh:"Actualiser",closeCurrent:"Fermer l'actuel",closeLeft:"Fermer à gauche",closeRight:"Fermer à droite",closeOthers:"Fermer les autres",closeAll:"Fermer tout"}}},ta={confirm:"Confirmer",cancel:"Annuler",save:"Enregistrer",delete:"Supprimer",remove:"Retirer",edit:"Modifier",add:"Ajouter",search:"Rechercher",reset:"Réinitialiser",export:"Exporter",import:"Importer",upload:"Télécharger",download:"Télécharger",preview:"Aperçu",print:"Imprimer",refresh:"Actualiser",back:"Retour",next:"Suivant",submit:"Soumettre",loading:"Chargement...",success:"Succès",error:"Erreur",warning:"Avertissement",info:"Information",index:"Index",title:"Titre",operation:"Opération",execute:"Exécuter",clear:"Effacer",moveUp:"Monter",moveDown:"Descendre",status:{active:"Actif",inactive:"Inactif",enabled:"Activé",disabled:"Désactivé",online:"En ligne",offline:"Hors ligne",pending:"En attente",completed:"Terminé",failed:"Échoué"},time:{today:"Aujourd'hui",yesterday:"Hier",thisWeek:"Cette semaine",lastWeek:"Semaine dernière",thisMonth:"Ce mois",lastMonth:"Mois dernier",custom:"Plage personnalisée"},pagination:{total:"Total",items:"éléments",page:"page",perPage:"par page",showing:"Affichage",to:"à",of:"sur"},validation:{required:"Ce champ est obligatoire",email:"Veuillez entrer une adresse e-mail valide",phone:"Veuillez entrer un numéro de téléphone valide",number:"Veuillez entrer un nombre valide",integer:"Veuillez entrer un entier valide",min:"La valeur minimale est {min}",max:"La valeur maximale est {max}",length:"La longueur doit être {length}",minLength:"La longueur minimale est {min}",maxLength:"La longueur maximale est {max}"},message:{saveSuccess:"Enregistrement réussi",deleteSuccess:"Suppression réussie",updateSuccess:"Mise à jour réussie",operationSuccess:"Opération réussie",operationFailed:"Échec de l'opération",confirmDelete:"Êtes-vous sûr de vouloir supprimer ?",noData:"Aucune donnée",loading:"Chargement...",networkError:"Erreur réseau, veuillez réessayer",copySuccess:"Copie réussie"},customFileSelector:{title:"Sélectionner des fichiers et des dossiers",searchPlaceholder:"Rechercher des fichiers ou des dossiers...",selectedItems:"Éléments sélectionnés",clearAll:"Tout effacer",noItemsSelected:"Aucun élément sélectionné",cancel:"Annuler",confirm:"Confirmer",loading:"Chargement...",error:{loadFailed:"Échec du chargement",accessDenied:"Accès refusé",notFound:"Chemin introuvable"}},languageSyncWarning:"Échec de la synchronisation de la langue avec le backend, mais la langue du frontend a été changée avec succès",test:{languageSwitch:{title:"Test de changement de langue",progressDialog:"Test de dialogue de progression",showProgress:"Afficher le dialogue de progression",temperatureConverter:"Test de convertisseur de température",temperatureDesc:"Les noms d'unités de température suivants devraient se mettre à jour automatiquement après le changement de langue :",reportNames:"Test de noms de rapports",reportDesc:"Les noms liés aux rapports suivants devraient se mettre à jour automatiquement après le changement de langue :",autoRefresh:"Actualisation automatique",showHidden:"Afficher les éléments cachés",instructions:"Instructions de test",step1:"Cliquez sur le bouton de changement de langue dans le coin supérieur droit",step2:"Sélectionnez une langue différente (comme l'anglais, l'espagnol, le français)",step3:"Observez si le texte sur la page se met à jour immédiatement vers la nouvelle langue"}}},aa={loading:{checking:"Vérification de l'autorisation...",loading:"Chargement..."},auth:{invalid:"Autorisation invalide : {msg}",unknownError:"Erreur inconnue",checkFailed:"Échec de la vérification d'autorisation, veuillez vérifier votre connexion réseau"}},ra={layout:ea,common:ta,app:aa},oa={dataScope:{title:"Sélecteur de portée de données",selectOrg:"Sélectionner l'organisation",orgList:"Liste des organisations",cancel:"Annuler",confirm:"Confirmer"},grantResource:{title:"Ressources d'autorisation",warning:"Les rôles non-super administrateurs ne peuvent pas être autorisés pour les ressources de menu de module système",firstLevel:"Répertoire de premier niveau",menu:"Menu",buttonAuth:"Autorisation de bouton",cancel:"Annuler",confirm:"Confirmer",selectDataScope:"Veuillez sélectionner la portée des données"}},ia={dashboard:"Tableau de bord",system:"Gestion système",user:"Gestion des utilisateurs",role:"Gestion des rôles",menu:"Gestion des menus",changeModule:{title:"Changer de module->",belongModule:"Module d'appartenance",requiredModule:"Veuillez sélectionner le module d'appartenance"},debug:{title:"Debug",description:"Debug"},configure:{title:"Config",description:"Config"},tool:{title:"Outils",description:"Outils"},sysConfig:{title:"Système",description:"Système"}},na={limit:{module:{title:"Nom du module",icon:"Icône",status:"Statut",sort:"Tri",description:"Description",createTime:"Date de création",operation:"Opération",add:"Ajouter un module",edit:"Modifier le module",delete:"Supprimer le module",deleteConfirm:"Supprimer les modules sélectionnés",deleteConfirmWithName:"Supprimer le module 【{name}】",form:{title:"Veuillez entrer le nom du module",status:"Veuillez sélectionner le statut",sort:"Veuillez entrer le tri",icon:"Veuillez sélectionner une icône"}},menu:{title:"Nom du menu",icon:"Icône du menu",type:"Type de menu",component:"Nom du composant",path:"Adresse de route",componentPath:"Chemin du composant",sort:"Tri",status:"Statut",description:"Description",operation:"Opération",add:"Ajouter un menu",edit:"Modifier le menu",delete:"Supprimer le menu",deleteConfirm:"Supprimer les menus sélectionnés",deleteConfirmWithName:"Supprimer le menu 【{name}】",form:{title:"Veuillez entrer le nom du menu",parent:"Veuillez sélectionner le menu parent",type:"Veuillez sélectionner le type de menu",path:"Veuillez entrer l'adresse de route",component:"Veuillez entrer l'adresse du composant",sort:"Veuillez entrer le tri",icon:"Veuillez sélectionner une icône",status:"Veuillez sélectionner le statut",link:"Veuillez entrer l'adresse de lien"}},button:{title:"Nom du bouton",code:"Code du bouton",sort:"Tri",description:"Description",operation:"Opération",add:"Ajouter un bouton",edit:"Modifier le bouton",delete:"Supprimer le bouton",deleteConfirm:"Supprimer les boutons sélectionnés",deleteConfirmWithName:"Supprimer le bouton 【{name}】",batch:{title:"Ajouter des boutons en lot",shortName:"Nom court de permission",codePrefix:"Préfixe de code",form:{shortName:"Veuillez entrer le nom court de permission",codePrefix:"Veuillez entrer le préfixe de code"}},form:{title:"Veuillez entrer le nom du bouton",code:"Veuillez entrer le code du bouton",sort:"Veuillez entrer le tri"}},role:{title:"Nom du rôle",org:"Organisation d'appartenance",category:"Type de rôle",status:"Statut",sort:"Tri",description:"Description",createTime:"Date de création",operation:"Opération",add:"Ajouter un rôle",edit:"Modifier le rôle",delete:"Supprimer le rôle",deleteConfirm:"Supprimer les rôles sélectionnés",deleteConfirmWithName:"Supprimer le rôle 【{name}】",grant:{resource:"Ressources d'autorisation",permission:"Permissions d'autorisation",dataScope:"Portée des données"},form:{title:"Veuillez entrer le nom du rôle",org:"Veuillez sélectionner l'organisation d'appartenance",category:"Veuillez sélectionner le type de rôle",status:"Veuillez sélectionner le statut"}},spa:{title:"Nom de la page unique",icon:"Icône",type:"Type de page unique",path:"Adresse de route",component:"Chemin du composant",sort:"Tri",description:"Description",createTime:"Date de création",operation:"Opération",add:"Ajouter une page unique",edit:"Modifier la page unique",delete:"Supprimer la page unique",deleteConfirm:"Supprimer les pages uniques sélectionnées",deleteConfirmWithName:"Supprimer la page unique 【{name}】",form:{title:"Veuillez entrer le nom de la page unique",type:"Veuillez sélectionner le type de page unique",path:"Veuillez entrer l'adresse de route",component:"Veuillez entrer l'adresse du composant",sort:"Veuillez entrer le tri",icon:"Veuillez sélectionner une icône",link:"Veuillez remplir l'adresse de lien, ex: http://www.baidu.com"}}}},sa={config:{title:"Configuration système",paramTitle:"Configuration des paramètres",systemName:"Nom du système",systemVersion:"Version du système",waveToolPath:"Chemin de l'outil de forme d'onde tiers",waveToolPathPlaceholder:"Veuillez sélectionner le chemin de l'outil de forme d'onde tiers",openDirectory:"Ouvrir le répertoire de fichiers",save:"Enregistrer",reset:"Réinitialiser",saveSuccess:"Enregistrement réussi",selectWaveTool:"Sélectionner l'outil d'analyse de forme d'onde",paramRefreshTime:"Intervalle de rafraîchissement des valeurs fixes (ms)",reportRefreshTime:"Intervalle de rafraîchissement des rapports (ms)",stateRefreshTime:"Intervalle de rafraîchissement des états (ms)",variRefreshTime:"Intervalle de rafraîchissement des variables (ms)",configTitle:"Configuration",configKey:"Clé de configuration",configValue:"Valeur de configuration",remark:"Remarque",sortCode:"Tri",operation:"Opération",deleteConfirm:"Supprimer la configuration 【{key}】"}},la={machineCode:"Code machine",activationCode:"Code d'activation",activationCodePlaceholder:"Veuillez entrer le code d'activation",reset:"Réinitialiser",activate:"Activer",success:{title:"Activation réussie",message:"Le système a été activé avec succès"},error:{unknown:"Échec de l'activation : {msg}",network:"Échec de l'activation, veuillez vérifier votre connexion réseau"}},ca={title:"Gestion des pages uniques",list:{title:"Liste des pages uniques",add:"Ajouter une page unique",deleteSelected:"Supprimer la sélection",deleteConfirm:"Êtes-vous sûr de vouloir supprimer la page unique {title} ?"},form:{title:"{opt} page unique",basicSettings:"Paramètres de base",functionSettings:"Paramètres de fonction",name:"Nom de la page unique",type:"Type de page unique",icon:"Icône",path:"Adresse de route",pathPlaceholder:"Veuillez remplir l'adresse de route, ex: /home/<USER>",componentName:"Nom du composant",componentPath:"Adresse du composant",linkPath:"Adresse de lien",linkPathPlaceholder:"Veuillez remplir l'adresse de lien, ex: http://www.baidu.com",sort:"Tri",description:"Description",isHome:"Définir comme page d'accueil",isHide:"Masquer la page",isFull:"Page plein écran",isAffix:"Onglet fixe",isKeepAlive:"Cache de route",cancel:"Annuler",confirm:"Confirmer",nameRequired:"Veuillez entrer le nom de la page unique",typeRequired:"Veuillez sélectionner le type de page unique",pathRequired:"Veuillez entrer l'adresse de route",componentNameRequired:"Veuillez entrer le nom du composant",componentPathRequired:"Veuillez entrer l'adresse du composant",sortRequired:"Veuillez entrer le tri",iconRequired:"Veuillez sélectionner une icône"}},da={title:"Connexion système",account:{title:"Connexion par compte",username:"Veuillez entrer votre nom d'utilisateur",password:"Veuillez entrer votre mot de passe",captcha:"Veuillez entrer le code de vérification",tenant:"Veuillez sélectionner le locataire"},phone:{title:"Connexion par téléphone",phone:"Veuillez entrer votre numéro de téléphone",smsCode:"Veuillez entrer le code SMS",getCode:"Obtenir le code",machineVerify:"Vérification machine",captcha:"Veuillez entrer le code de vérification",sendSuccess:"Code envoyé avec succès",sendFailed:"Échec de l'envoi du code SMS"},button:{reset:"Réinitialiser",login:"Se connecter"},dialog:{cancel:"Annuler",confirm:"Confirmer"}},pa={title:"Centre d'aide",subtitle:"Aide et documentation",catalog:"Catalogue",searchPlaceholder:"Rechercher dans l'aide...",loadFail:"Échec du chargement du document d'aide, veuillez réessayer plus tard."},ua={role:oa,menu:ia,limit:na,sys:sa,activate:la,spa:ca,login:da,help:pa},ma={configure:{remoteSet:"Téléréglage"},console:{title:"Console",clear:"Effacer",selectAll:"Tout sélectionner",copy:"Copier",copySuccess:"Copie réussie",noTextSelected:"Aucun texte sélectionné",copyFailed:"Échec de la copie",clearSuccess:"Console effacée",collapse:"Réduire",expand:"Développer"},groupInfo:{title:"Informations de groupe",table:{id:"Index",name:"Nom",desc:"Description",fc:"FC",count:"Nombre"},messages:{fetchDataError:"Erreur lors de l'obtention des données",fetchedData:"Données obtenues :"}},treeClickLog:"Clic treeClick : ",contentView:"Vue du contenu",emptyDeviceId:"L'ID de l'appareil actuel est vide",invalidResponseStructure:"Structure de réponse invalide",formattedMenuDataLog:"Données de menu formatées ===",allSettings:"Toutes les valeurs fixes",allEditSpSettings:"Toutes les valeurs fixes mono-zone",allEditSgSettings:"Toutes les valeurs fixes multi-zones",deviceTreeDataLog:"Données de l'arbre d'appareils",failedToLoadMenu:"Échec du chargement du menu d'appareil :",innerTabs:{contentView:"Contenu",fileUpload:"Upload",fileDownload:"Download",deviceTime:"Sync",deviceOperate:"Opération",variableDebug:"Debug",oneClickBackup:"Sauvegarde",entryConfig:"Config",tabClickLog:"Clic d'onglet :"},devices:{notConnectedAlt:"Appareil non connecté",pleaseConnect:"Veuillez d'abord connecter l'appareil !"},list:{unnamedDevice:"Appareil sans nom",connected:"Connecté",disconnected:"Déconnecté",connect:"Se connecter",edit:"Modifier",disconnect:"Se déconnecter",remove:"Supprimer",noDeviceFound:"Aucun appareil trouvé",handleClickLog:"Clic handleListClick :",disconnectBeforeEdit:"Veuillez d'abord vous déconnecter avant de modifier",connectSuccess:"Appareil {name} : Connexion réussie",connectExist:"Appareil {name} : Connexion déjà existante",connectFailed:"Appareil {name} : Échec de connexion",connectFailedReason:"Raison de l'échec de connexion de l'appareil :",disconnectedSuccess:"Appareil {name} : Déconnecté",disconnectedNotify:"Appareil {name} connexion déconnectée",currentDisconnectedNotify:"Connexion de l'appareil actuel déconnectée",operateFailed:"Appareil {name} : Échec de l'opération",disconnectBeforeDelete:"Veuillez d'abord vous déconnecter avant de supprimer",dataLog:"Données :",ipPortExist:"Cette IP et ce port existent déjà, veuillez ne pas les ajouter en double"},search:{placeholder:"Rechercher un appareil",ipPortExist:"Cette IP et ce port existent déjà, veuillez ne pas les ajouter en double"},summaryPie:{other:"Autre",title:"Proportion des valeurs fixes",subtext:"Valeurs fixes du groupe"},deviceInfo:{title:"Informations d'appareil",export:"Exporter",exportTitle:"Exporter les informations d'appareil",exportLoading:"Export des informations de base de l'appareil en cours...",exportSuccess:"Export des informations de base de l'appareil réussi",exportFailed:"Échec de l'export des informations de base de l'appareil",getInfoFailed:"Échec de l'obtention des informations d'appareil. Raison : {msg}",getInfoFailedEmpty:"Échec de l'obtention des informations d'appareil. Raison : données vides !",defaultFileName:"Informations d'appareil.xlsx",confirm:"Confirmer",tip:"Conseil"},allParamSetting:{title:"Toutes les valeurs fixes",autoRefresh:"Actualisation automatique",refresh:"Actualiser",confirm:"Confirmer",import:"Importer",export:"Exporter",groupTitle:"Groupe de valeurs fixes :",allGroups:"Tout",noDataToImport:"Aucune donnée à importer",importSuccess:"Import des valeurs fixes réussi",importFailed:"Échec de l'import des valeurs fixes : {msg}",requestFailed:"Échec de la requête, veuillez réessayer plus tard",queryFailed:"Échec de la requête des valeurs fixes : {msg}",unsavedChanges:"Il y a des modifications non enregistrées, voulez-vous continuer l'actualisation ?",confirmButton:"Confirmer",cancelButton:"Annuler",alertTitle:"Conseil",errorTitle:"Erreur",noDataToConfirm:"Aucune donnée à confirmer",confirmSuccess:"Mise à jour des valeurs fixes réussie",confirmFailed:"Échec de la mise à jour des valeurs fixes : ",responseLog:"Données de réponse :",continueAutoRefresh:"Continuer l'actualisation automatique",settingGroup:"Groupe de valeurs fixes",all:"Tout",minValue:"Valeur minimale",maxValue:"Valeur maximale",step:"Pas",unit:"Unité",searchNamePlaceholder:"Entrez le nom de la valeur fixe pour rechercher",searchDescPlaceholder:"Entrez la description de la valeur fixe pour rechercher",autoRefreshWarning:"La modification des données n'est pas autorisée lorsque l'actualisation automatique est activée",invalidValue:"La valeur {value} de la valeur fixe {name} n'est pas dans la plage valide",exportFileName:"Valeurs fixes de paramètres d'appareil_Toutes les valeurs fixes.xlsx",selectPathLog:"Sélectionner le chemin : ",exportSuccess:"Liste des valeurs exportée avec succès"},variable:{autoRefresh:"Actualisation automatique",variableName:"Nom de variable",inputVariableName:"Veuillez entrer le nom de variable à ajouter",refresh:"Actualiser",add:"Ajouter",confirm:"Confirmer",import:"Importer",export:"Exporter",delete:"Supprimer",noDataToConfirm:"Aucune donnée à confirmer",warning:"Avertissement",variableModifiedSuccess:"Modification de variable réussie",variableModifiedFailed:"Échec de la modification de variable, raison :",requestFailed:"Échec de la requête, veuillez réessayer plus tard",error:"Erreur",success:"Succès",variableAddSuccess:"Ajout de variable réussi",variableAddFailed:"Échec de l'ajout de variable, raison :",variableDeleteSuccess:"Suppression de variable réussie",variableDeleteFailed:"Échec de la suppression de variable, raison :",exportSuccess:"Export des informations de variables de débogage d'appareil réussi",exportFailed:"Échec de l'export des informations de variables de débogage d'appareil, raison :",importSuccess:"Import des informations de variables de débogage d'appareil réussi",importFailed:"Échec de l'import des informations de variables de débogage d'appareil :",confirmRefresh:"Il y a des modifications non enregistrées, voulez-vous continuer l'actualisation ?",confirmAutoRefresh:"Il y a des modifications non enregistrées, voulez-vous continuer l'actualisation automatique ?",pleaseInputVariableName:"Veuillez remplir le nom de variable",exportTitle:"Exporter les variables de débogage d'appareil",importTitle:"Importer les variables de débogage d'appareil",defaultExportPath:"Variables de débogage d'appareil.xlsx",title:"Débogage de variables",variableNamePlaceholder:"Veuillez entrer le nom de variable à ajouter",batchDelete:"Suppression en lot",modifySuccess:"Modification de variable réussie",modifyFailed:"Échec de la modification de variable, raison : {msg}",alertTitle:"Avertissement",successTitle:"Conseil",errorTitle:"Erreur",confirmButton:"Confirmer",cancelButton:"Annuler",sequence:"Index",name:"Nom",value:"Valeur",type:"Type",description:"Description",address:"Adresse",operation:"Opération",enterVariableName:"Veuillez entrer le nom de variable à ajouter",responseLog:"Données de réponse :",addSuccess:"Ajout de variable réussi",addFailed:"Échec de l'ajout de variable, raison :",addFailedWithName:"Échec de l'ajout de variable {name} : {reason}",exportFileName:"Variables de débogage d'appareil.xlsx",selectPathLog:"Sélection du chemin :",exportSuccessLog:"Export des informations de variables de débogage d'appareil réussi, {path}",exportFailedLog:"Échec de l'export des informations de variables de débogage d'appareil, raison :",importFailedLog:"Échec de l'import des informations de variables de débogage d'appareil :",unsavedChanges:"Il y a des modifications non enregistrées, voulez-vous continuer l'actualisation ?",continueAutoRefresh:"Continuer l'actualisation automatique",tip:"Conseil",sequenceNumber:"Index"},backup:{sequence:"Index",title:"Sauvegarde d'appareil",savePath:"Chemin de sauvegarde",setPath:"Définir le chemin de sauvegarde",setPathTitle:"Définir le chemin",startBackup:"Démarrer la sauvegarde",cancelBackup:"Annuler la sauvegarde",backup:"Sauvegarder",backupType:"Type de sauvegarde",progress:"Progression",status:"État",operation:"Opération",backupTypes:{paramValue:"Sauvegarder les valeurs fixes de paramètres d'appareil",faultInfo:"Sauvegarder les informations de rapport de défaillance de l'appareil",cidConfigPrjLog:"Sauvegarder cid/ccd/device_config/debug_info/prj/log",waveReport:"Sauvegarder les fichiers de forme d'onde de l'appareil"},backupDesc:"Description du contenu de la sauvegarde",backupDescTypes:{paramValue:"Exporter les valeurs de paramètres de l'appareil (param export.xlsx)",faultInfo:"Exporter les informations de défaut de l'appareil (rapports d'événement/opération/défaut/audit)",cidConfigPrjLog:"Exporter les fichiers de configuration (CID/CCD, configuration XML, fichiers journaux)",waveReport:"Exporter les fichiers d'onde de l'appareil (/wave/comtrade)"},locateFolder:"Localiser le dossier",backupSuccess:"Sauvegarde réussie",openFolderFailed:"Échec de l'ouverture du dossier",backupFailed:"Échec de la sauvegarde",noTypeSelected:"Veuillez d'abord sélectionner le type de sauvegarde",cancelSuccess:"Annulation réussie",cancelFailed:"Échec de l'annulation"},operate:{title:"Opération d'appareil",manualWave:"Enregistrer manuellement les événements",resetDevice:"Réinitialiser l'appareil",clearReport:"Effacer le rapport",clearWave:"Effacer les événements",executing:"En cours d'exécution...",selectOperation:"Veuillez sélectionner l'opération",success:{manualWave:"Enregistrement manuel des événements réussi",resetDevice:"Réinitialisation de l'appareil réussie",clearReport:"Effacement du rapport réussi",clearWave:"Effacement des événements réussi"},fail:{manualWave:"Échec de l'enregistrement manuel des événements, raison :",resetDevice:"Échec de la réinitialisation de l'appareil, raison :",clearReport:"Échec de l'effacement du rapport, raison :",clearWave:"Échec de l'effacement des événements, raison :"}},time:{title:"Synchronisation d'appareil",currentTime:"Temps actuel",deviceTime:"Temps de l'appareil",selectDateTime:"Sélectionner la date et le temps",milliseconds:"Millisecondes",now:"Maintenant",read:"Lire",write:"Écrire",readSuccess:"Réussite de la lecture de l'heure de l'appareil.",readFailed:"Échec de la lecture de l'heure de l'appareil : {msg}",readFailedInvalidFormat:"Échec de la lecture de l'heure de l'appareil : Format de temps invalide",readFailedDataError:"Échec de la lecture de l'heure de l'appareil : Erreur de format des données",writeSuccess:"Écriture de l'heure de l'appareil réussie.",writeFailed:"Échec de l'écriture de l'heure de l'appareil : {msg}",writeFailedInvalidFormat:"Échec de l'écriture de l'heure de l'appareil : Format de temps invalide",millisecondsRangeError:"La plage de valeurs de millisecondes doit être comprise entre 0 et 999",unknownError:"Erreur inconnue"},reportOperate:{title:"Opération de rapport",date:"Date :",search:"Rechercher",save:"Enregistrer",clearList:"Effacer la liste",loading:"Chargement des données",progress:{title:"Informations de progression",loading:"Chargement",searching:"Recherche en cours {type}"},table:{reportId:"Numéro de rapport",name:"Nom",time:"Temps",operationAddress:"Adresse d'opération",operationParam:"Paramètre d'opération",value:"Valeur",step:"Étape",source:"Source",sourceType:"Type de source",result:"Résultat"},messages:{selectDateRange:"Veuillez sélectionner une plage de temps complète",noDataToSave:"Aucune donnée à enregistrer",saveSuccess:"Enregistrement réussi",saveReport:"Enregistrer le rapport"}},reportGroup:{title:"Groupe de rapport",date:"Date :",search:"Rechercher",save:"Enregistrer",clearList:"Effacer la liste",autoRefresh:"Actualisation automatique",loading:"Chargement des données",progress:{title:"Informations de progression",loading:"Chargement",searching:"Recherche en cours {type}"},table:{reportId:"Numéro de rapport",time:"Temps",description:"Description"},contextMenu:{uploadWave:"Invoquer les événements",getHistoryReport:"Obtenir le rapport historique",saveResult:"Enregistrer le résultat",clearContent:"Effacer le contenu de la page"},messages:{selectDateRange:"Veuillez sélectionner une plage de temps complète",noDataToSave:"Aucune donnée à enregistrer",noFileToUpload:"Aucun fichier à invoquer",saveSuccess:"Enregistrement réussi",saveReport:"Enregistrer le rapport",waveToolNotConfigured:"Chemin d'outil de forme d'onde non configuré",waveFileUploading:"Invoquer le fichier de forme d'onde",waveFileUploadComplete:"Invoquer le fichier de forme d'onde terminé",waveFileUploadCompleteWithPath:"Invoquer le fichier de forme d'onde terminé, chemin : {path}",openWaveFileConfirm:"Souhaitez-vous ouvrir le fichier de forme d'onde avec un outil tiers?",openWaveFileTitle:"Avertissement",confirm:"Confirmer",cancel:"Annuler"},refresh:{stop:"Arrêter l'actualisation",start:"Actualiser automatiquement"},hiddenItems:{show:"Afficher les éléments cachés",hide:"Ne pas afficher les éléments cachés"}},fileDownload:{title:"Téléchargement de fichier",deviceDirectory:"Répertoire de l'appareil",reboot:"Redémarrer",noReboot:"Ne pas redémarrer",selectFile:"Sélectionner le fichier",addDownloadFile:"Ajouter le fichier à télécharger",addDownloadFolder:"Ajouter le dossier à télécharger",addDownloadFilesAndFolders:"Ajouter les fichiers et dossiers",downloadFile:"Télécharger le fichier",cancelDownload:"Annuler le téléchargement",importList:"Importer la liste",exportList:"Exporter la liste",batchDelete:"Supprimer en lot",clearList:"Effacer la liste",download:"Télécharger",delete:"Supprimer",fileName:"Nom du fichier",fileSize:"Taille du fichier",filePath:"Chemin du fichier",lastModified:"Dernière modification",progress:"Progression",status:"État",operation:"Opération",folder:"Dossier",waitingDownload:"En attente de téléchargement",calculatingFileInfo:"Calculer les informations du fichier",downloadPreparing:"Préparation du téléchargement",downloading:"Téléchargement en cours...",downloadComplete:"Téléchargement terminé",downloadError:"Erreur de téléchargement :",userCancelled:"Utilisateur annulé",allFilesComplete:"Téléchargement terminé",fileExists:"Le fichier {path} existe déjà, échec de l'ajout !",selectValidFile:"Veuillez sélectionner un fichier valide pour effectuer l'opération de téléchargement",remotePathEmpty:"Chemin distant ne peut être vide",noDownloadTask:"Aucune tâche de téléchargement obtenue pour annuler",fileSizeZero:"Le fichier {fileName} a une taille de 0, impossible de télécharger",downloadCancelled:"Téléchargement de fichier {path} annulé terminé",downloadCancelledFailed:"Échec de l'annulation du téléchargement de fichier {path}, raison : {msg}",fileDeleted:"Suppression de fichier {path} terminée",exportSuccess:"Exportation de la liste de téléchargement de fichiers réussie",exportFailed:"Échec de l'exportation de la liste de téléchargement de fichiers",importSuccess:"Importation de la liste de téléchargement de fichiers réussie",importFailed:"Échec de l'importation de la liste de téléchargement de fichiers : {msg}",downloadList:"Liste de téléchargement de fichiers",exportTitle:"Exporter la liste de téléchargement de fichiers",importTitle:"Importer la liste de téléchargement de fichiers",error:"Erreur",tip:"Conseil",confirm:"Confirmer",sequence:"Index",confirmButton:"Confirmer",cancelButton:"Annuler",alertTitle:"Conseil",errorTitle:"Erreur",successTitle:"Succès",warningTitle:"Avertissement",loading:"Chargement",executing:"Exécution...",noData:"Aucune donnée",selectDateRange:"Veuillez sélectionner la plage de dates",search:"Rechercher",save:"Enregistrer",clear:"Effacer",refresh:"Actualiser",stop:"Arrêter",start:"Démarrer",show:"Afficher",hide:"Masquer",showHiddenItems:"Afficher les éléments masqués",hideHiddenItems:"Masquer les éléments",continue:"Continuer",cancel:"Annuler",confirmImport:"Confirmer l'importation",confirmExport:"Confirmer l'exportation",confirmDelete:"Confirmer la suppression",confirmClear:"Confirmer l'effacement",confirmCancel:"Confirmer l'annulation",confirmContinue:"Confirmer la continuation",confirmStop:"Confirmer l'arrêt",confirmStart:"Confirmer le démarrage",confirmShow:"Confirmer l'affichage",confirmHide:"Confirmer le masquage",confirmRefresh:"Confirmer l'actualisation",confirmSave:"Confirmer l'enregistrement",confirmSearch:"Confirmer la recherche",confirmClearList:"Confirmer l'effacement de la liste",confirmImportList:"Confirmer l'importation de la liste",confirmExportList:"Confirmer l'exportation de la liste",confirmBatchDelete:"Confirmer la suppression par lot",confirmDownload:"Confirmer le téléchargement",confirmCancelDownload:"Confirmer l'annulation du téléchargement",confirmDeleteFile:"Confirmer la suppression du fichier",confirmClearFiles:"Confirmer l'effacement des fichiers",confirmImportFiles:"Confirmer l'importation des fichiers",confirmExportFiles:"Confirmer l'exportation des fichiers",confirmBatchDeleteFiles:"Confirmer la suppression par lot des fichiers",confirmDownloadFiles:"Confirmer le téléchargement des fichiers",confirmCancelDownloadFiles:"Confirmer l'annulation du téléchargement des fichiers",rename:"Renommer pour le téléchargement",renamePlaceholder:"Renommer lors du téléchargement (optionnel)",renameCopyFailed:"Échec de la copie du fichier pour le renommage :",packageProgram:"Emballage de programme",selectSaveDir:"Sélectionner le répertoire de sauvegarde",packageBtn:"Emballer",locateDir:"Localiser le dossier",saveDirEmpty:"Veuillez d'abord sélectionner un répertoire de sauvegarde !",packageSuccess:"Emballage de programme terminé !",packageFailed:"Échec de l'emballage : {msg}",noFileSelected:"Veuillez sélectionner les fichiers à emballer !",zipPath:"Chemin du fichier ZIP : {zipPath}",addToDownload:"Ajouter au téléchargement",fileAdded:"Fichier ajouté à la liste de téléchargement : {path}",rebootSuccess:"Redémarrage de l'appareil réussi",rebootFailed:"Échec du redémarrage de l'appareil : {msg}"},fileUpload:{serialNumber:"Index",title:"Téléchargement de fichier",importList:"Importer la liste",exportList:"Exporter la liste",batchDelete:"Supprimer en lot",clearList:"Effacer la liste",upload:"Télécharger",cancelUpload:"Annuler le téléchargement",delete:"Supprimer",sequence:"Index",fileName:"Nom du fichier",fileSize:"Taille du fichier",filePath:"Chemin du fichier",lastModified:"Dernière modification",progress:"Progression",statusTitle:"État",status:{waiting:"En attente de téléchargement",preparing:"Préparation du téléchargement",uploading:"Téléchargement en cours...",completed:"Téléchargement terminé",error:"Erreur de téléchargement :",cancelled:"Utilisateur annulé"},operation:"Opération",calculatingFileInfo:"Calculer les informations du fichier",uploadPreparing:"Préparation du téléchargement",uploading:"Téléchargement en cours...",uploadComplete:"Téléchargement terminé",uploadError:"Erreur de téléchargement : {errorMsg}",userCancelled:"Utilisateur annulé",allFilesComplete:"Téléchargement terminé",fileExists:"Le fichier {path} existe déjà, échec de l'ajout !",invalidFile:"Veuillez sélectionner un fichier valide pour effectuer l'opération de téléchargement",emptySavePath:"Chemin de sauvegarde de fichier ne peut être vide",fileUploadComplete:"Téléchargement de fichier {fileName} terminé",selectPath:"Sélectionner le chemin",pathOptions:{shr:"/shr",configuration:"/shr/configuration",log:"/log",wave:"/wave",comtrade:"/wave/comtrade"},deviceDirectory:"Répertoire de l'appareil",savePath:"Chemin de sauvegarde",setPath:"Définir le chemin",getFiles:"Obtenir les fichiers",uploadFiles:"Télécharger les fichiers",errors:{invalidFile:"Veuillez sélectionner un fichier valide pour effectuer l'opération de téléchargement",emptySavePath:"Chemin de sauvegarde de fichier ne peut être vide",noUploadTask:"Aucune tâche de téléchargement obtenue pour annuler",getFilesFailed:"Échec de l'obtention des fichiers du répertoire de l'appareil",fileSizeZero:"Le fichier {fileName} a une taille de 0, impossible de télécharger"},messages:{uploadCompleted:"Téléchargement de fichier terminé",uploadCancelled:"Téléchargement de fichier annulé terminé",clearListSuccess:"Effacement de la liste de fichiers réussi"}},info:{title:"Informations d'appareil",export:"Exporter",exportSuccess:"Export des informations de base de l'appareil réussi",exportFailed:"Échec de l'export des informations de base de l'appareil",exportTip:"Conseil",confirm:"Confirmer",exportLoading:"Export des informations de base de l'appareil en cours...",getInfoFailed:"Échec de l'obtention des informations d'appareil. Raison :",dataEmpty:"Données vides !"},summary:{title:"Aperçu de groupe d'appareil",basicInfo:"Infos de base",settingTotal:"Total des valeurs fixes",telemetry:"Télémétrie",teleindication:"Téléindication",telecontrol:"Télécontrôle",driveOutput:"Sortie de commande",settingRatio:"Proportion des valeurs fixes"},dict:{refresh:"Actualiser",confirm:"Confirmer",import:"Importer",export:"Exporter",sequence:"Index",shortAddress:"Adresse courte",shortAddressTooltip:"Entrez l'adresse courte à rechercher",chinese:"Chinois",english:"Anglais",spanish:"Espagnol",french:"Français",operation:"Opération",confirmLog:"Confirmer le dictionnaire",importLog:"Importer le dictionnaire",exportLog:"Exporter le dictionnaire",refreshLog:"Actualiser le dictionnaire",newValueLog:"Nouvelle valeur :"},allParamCompare:{title:"Comparer la différence de valeur fixe d'importation",cancel:"Annuler",confirm:"Confirmer l'importation",groupName:"Nom de groupe",name:"Nom",description:"Description",minValue:"Valeur minimale",maxValue:"Valeur maximale",step:"Pas",unit:"Unité",address:"Adresse",oldValue:"Valeur ancienne",newValue:"Nouvelle valeur",sequence:"Index",searchName:"Entrez le nom de la valeur fixe pour rechercher",searchDescription:"Entrez la description de la valeur fixe pour rechercher",messages:{noSelection:"Aucune donnée sélectionnée",error:"Erreur"}},deviceForm:{title:{add:"Ajouter l'appareil",edit:"Modifier l'appareil"},name:"Nom de l'appareil",ip:"IP",port:"Port",connectTimeout:"Délai de connexion (millisecondes)",readTimeout:"Délai de requête globale (millisecondes)",paramTimeout:"Délai de modification de valeur fixe (millisecondes)",encrypted:"Connexion cryptée",advanced:{show:"Afficher les options avancées",hide:"Masquer les options avancées"},buttons:{cancel:"Annuler",confirm:"Confirmer"},messages:{nameRequired:"Veuillez entrer le nom de l'appareil",nameTooLong:"Le nom de l'appareil ne doit pas être trop long",invalidIp:"Veuillez entrer une IP valide",invalidPort:"Le port doit être compris entre 1 et 65535",timeoutTooShort:"Le délai ne doit pas être trop court"}},paramCompare:{title:"Comparer la différence de valeur fixe d'importation",cancel:"Annuler",confirm:"Confirmer l'importation",sequence:"Index",name:"Nom",description:"Description",minValue:"Valeur minimale",maxValue:"Valeur maximale",step:"Pas",unit:"Unité",address:"Adresse",oldValue:"Valeur ancienne",newValue:"Nouvelle valeur",searchName:"Entrez le nom de la valeur fixe pour rechercher",searchDescription:"Entrez la description de la valeur fixe pour rechercher",messages:{noSelection:"Aucune donnée sélectionnée",error:"Erreur"}},progress:{title:"Informations de progression",executing:"En cours d'exécution..."},remoteYm:{title:"Télémétrie à distance",sequence:"Index",shortAddress:"Adresse courte",description:"Description",value:"Valeur",operation:"Opération",inputShortAddressFilter:"Entrez l'adresse courte filtrée",inputDescriptionFilter:"Entrez la description filtrée",invalidData:"Valeur {name} de {value} non valide",error:"Erreur",success:"Succès",executeSuccess:"Exécution réussie",prompt:"Invite",confirmButton:"Confirmer",confirmExecute:"Confirmer l'exécution",confirm:"Confirmer",executeButton:"Exécuter",cancelButton:"Annuler"},remoteYt:{title:"Contrôle à distance",sequence:"Index",directControl:"Contrôle direct",selectControl:"Contrôle sélectionné",shortAddress:"Adresse courte",description:"Description",value:"Valeur",operation:"Opération",inputShortAddressFilter:"Entrez l'adresse courte filtrée",inputDescriptionFilter:"Entrez la description filtrée",invalidData:"Valeur {name} de {value} non valide",error:"Erreur",success:"Succès",executeSuccess:"Exécution réussie",prompt:"Invite",confirm:"Confirmer",errorInfo:"Information d'erreur",executeFailed:"Échec de l'exécution du télécontrôle à distance, raison : {msg}",executeSuccessLog:"{desc} Exécution du télécontrôle à distance réussie",cancelSuccess:"Annulation réussie",cancelFailed:"Échec de l'annulation du télécontrôle à distance, raison : {msg}",selectSuccess:"Sélection réussie, exécuter ?",confirmInfo:"Information de confirmation",execute:"Exécuter",cancel:"Annuler"},paramSetting:{title:"Valeurs fixes de paramètres d'appareil",autoRefresh:"Actualisation automatique",refresh:"Actualiser",confirm:"Confirmer",import:"Importer",export:"Exporter",currentEditArea:"Zone en cours d'exécution",selectEditArea:"Zone en cours de modification",noDataToImport:"Aucune donnée à importer",noDataToConfirm:"Aucune donnée à confirmer",importSuccess:"Import des valeurs fixes réussi",importFailed:"Échec de l'import des valeurs fixes",updateSuccess:"Mise à jour des valeurs fixes réussie",updateFailed:"Échec de la mise à jour des valeurs fixes",requestFailed:"Échec de la requête, veuillez réessayer plus tard",setEditArea:"Définir",setEditAreaTitle:"Définir la zone de modification",setEditAreaSuccess:"Définir la zone de modification réussie",modifiedWarning:"Il y a des modifications non enregistrées, voulez-vous continuer l'actualisation ?",autoRefreshWarning:"La modification des données n'est pas autorisée lorsque l'actualisation automatique est activée",autoRefreshDisabled:"La modification des données n'est pas autorisée lorsque l'actualisation automatique est activée",invalidValue:"La valeur {value} de la valeur fixe {name} n'est pas dans la plage valide",exportSuccess:"Exporter les valeurs fixes de paramètres d'appareil réussie",exportFailed:"Échec de l'exportation des valeurs fixes de paramètres d'appareil",noDiffData:"Aucune donnée de différence obtenue",table:{index:"Index",name:"Nom",description:"Description",value:"Valeur",minValue:"Valeur minimale",maxValue:"Valeur maximale",step:"Pas",address:"Adresse",unit:"Unité",operation:"Opération"},search:{namePlaceholder:"Entrez le nom de la valeur fixe pour rechercher",descPlaceholder:"Entrez la description de la valeur fixe pour rechercher"}},remoteControl:{title:"Contrôle",sequence:"Index",shortAddress:"Adresse courte",description:"Description",control:"Contrôle/Contrôle",type:"Type",operation:"Opération",directControl:"Contrôle direct",selectControl:"Contrôle sélectionné",controlClose:"Contrôle",controlOpen:"Contrôle",noCheck:"Ne pas vérifier",syncCheck:"Vérifier la synchronisation",deadCheck:"Vérifier la mort",confirmInfo:"Confirmer les informations",execute:"Exécuter",cancel:"Annuler",confirm:"Confirmer",success:"Succès",failed:"Échec",errorInfo:"Informations d'erreur",promptInfo:"Informations d'invite",confirmSuccess:"Sélection réussie, voulez-vous exécuter ?",executeSuccess:"Exécution réussie",cancelSuccess:"Annulation réussie",executeFailed:"Échec de l'exécution à distance, raison :",cancelFailed:"Échec de l'annulation à distance, raison :",remoteExecuteSuccess:"Exécution à distance réussie",remoteCancelSuccess:"Annulation à distance réussie"},remoteDrive:{action:"Action",executeSuccess:"Exécution réussie",executeFailed:"Échec de l'exécution",prompt:"Informations d'invite",error:"Informations d'erreur",confirm:"Confirmer",shortAddress:"Adresse courte",description:"Description",operation:"Opération",enterToFilter:"Entrez l'adresse courte filtrée",enterToFilterDesc:"Entrez la description filtrée",actionSuccess:"Exécution d'action réussie",actionFailed:"Échec de l'exécution d'action",failureReason:"Raison de l'échec",sequence:"Index"},remoteSignal:{autoRefresh:"Actualisation automatique",refresh:"Actualiser",export:"Exporter",sequence:"Index",name:"Nom",description:"Description",value:"Valeur",quality:"Qualité",searchName:"Entrez le nom pour rechercher",searchDesc:"Entrez la description pour rechercher",searchValue:"Entrez la valeur pour rechercher",exportTitle:"Exporter les informations d'indication à distance",exportSuccess:"Exporter les informations d'indication à distance réussie",exportFailed:"Échec de l'exportation des informations d'indication à distance",exportSuccessWithPath:"Exporter les informations d'indication à distance réussie,",exportFailedWithError:"Échec de l'exportation des informations d'indication à distance :",invalidData:"Données invalides :",errorInDataCallback:"Erreur de traitement de rappel de données :",errorFetchingData:"Erreur lors de la récupération des données :"},remoteTelemetry:{autoRefresh:"Actualisation automatique",refresh:"Actualiser",export:"Exporter",sequence:"Index",name:"Nom",description:"Description",value:"Valeur",unit:"Unité",quality:"Qualité",searchName:"Entrez le nom pour rechercher",searchDesc:"Entrez la description pour rechercher",searchValue:"Entrez la valeur pour rechercher",exportTitle:"Exporter les informations d'état d'appareil",exportSuccess:"Exporter les informations d'état d'appareil réussie",exportFailed:"Échec de l'exportation des informations d'état d'appareil",exportSuccessWithPath:"Exporter les informations d'état d'appareil réussie,",exportFailedWithError:"Échec de l'exportation des informations d'état d'appareil :",confirm:"Confirmer",tip:"Conseil",exportFileName:"Informations d'état d'appareil",selectPathLog:"Sélectionner le chemin :"},remote:{directControl:"Contrôle direct",selectControl:"Contrôle sélectionné",serialNumber:"Index",shortAddress:"Adresse courte",description:"Description",value:"Valeur",operation:"Opération",inputShortAddressFilter:"Entrez l'adresse courte filtrée",inputDescriptionFilter:"Entrez la description filtrée",invalidData:"Valeur {name} de {value} non valide",error:"Erreur",success:"Succès",executeSuccess:"Exécution réussie",prompt:"Informations d'invite",confirm:"Confirmer",errorInfo:"Informations d'erreur",executeFailed:"Échec de l'exécution à distance, raison : {msg}",executeSuccessLog:"{desc} Exécution à distance réussie",cancelSuccess:"Annulation réussie",cancelFailed:"Échec de l'annulation à distance, raison : {msg}",selectSuccess:"Sélection réussie, voulez-vous exécuter ?",confirmInfo:"Confirmer les informations",execute:"Exécuter",cancel:"Annuler"},report:{uploadWave:"Invoquer les événements",searchHistory:"Obtenir le rapport historique",saveResult:"Enregistrer le résultat",clearContent:"Effacer le contenu de la page",date:"Date",query:"Rechercher",save:"Enregistrer",autoRefresh:"Actualiser automatiquement",stopRefresh:"Arrêter l'actualisation",clearList:"Effacer la liste",progressInfo:"Informations de progression",loading:"Chargement des données",reportNo:"Numéro de rapport",time:"Temps",description:"Description",noFileToUpload:"Aucun fichier à invoquer",uploadSuccess:"Invoquer les événements terminé",uploadPath:"Invoquer les événements terminé, chemin :",noDataToSave:"Aucune donnée à enregistrer",saveSuccess:"Enregistrement réussi",saveReport:"Enregistrer le rapport",openWaveConfirm:"Souhaitez-vous ouvrir le fichier d'événements avec un outil tiers?",confirm:"Confirmer",cancel:"Annuler",waveToolNotConfigured:"Chemin d'outil de forme d'onde non configuré",pleaseSelectTimeRange:"Veuillez sélectionner une plage de temps complète",querying:"Recherche en cours",reportNumber:"Numéro de rapport",operationAddress:"Adresse d'opération",operationParams:"Paramètres d'opération",result:"Résultat",progress:"Informations de progression",loadingText:"Chargement",selectCompleteTimeRange:"Veuillez sélectionner une plage de temps complète",fileUploading:"Invoquer les événements",fileUploadComplete:"Invoquer les événements terminé"},customMenu:{addMenu:"Ajouter un menu personnalisé",editMenu:"Modifier le menu personnalisé",deleteMenu:"Supprimer le menu personnalisé",addReport:"Ajouter un rapport personnalisé",editReport:"Modifier le rapport personnalisé",deleteReport:"Supprimer le rapport personnalisé",addPointGroup:"Ajouter un groupe personnalisé (points)",editPointGroup:"Modifier le groupe personnalisé (points)",deletePointGroup:"Supprimer un groupe personnalisé (points)",selectedPoints:"Points sélectionnés",selectFc:"Sélectionner FC",selectGroupType:"Sélectionner le type de groupe",groupTypes:{ST:"Signal distant",MX:"Mesure distante",SP:"Réglage zone unique",SG:"Réglage zone multiple"},filterPlaceholder:"Filtrer par nom/description",loadingData:"Chargement des données...",noDataForFc:"Aucune donnée pour ce FC",noDataForGroupType:"Aucune donnée pour ce type de groupe",pleaseSelectFc:"Veuillez d'abord sélectionner FC",pleaseSelectGroupType:"Veuillez d'abord sélectionner le type de groupe",loadingGroupTypeData:"Chargement des données de type de groupe...",loadingGroupTypes:"Chargement des données de types de groupe...",loadedGroupTypes:"Types de groupe chargés",dataLoadComplete:"Chargement des données terminé",dataLoadFailed:"Échec du chargement des données",switchingToGroupType:"Basculement vers",loadingGroupTypeDataSingle:"Chargement des données...",loadGroupTypeFailed:"Échec du chargement des données",loadGroupTypeError:"Erreur lors du chargement des données",inputGroupName:"Veuillez saisir le nom du groupe",inputGroupDesc:"Veuillez saisir la description du groupe",selectGroupTypeFirst:"Veuillez d'abord sélectionner le type de groupe",menuName:"Nom du groupe",menuDesc:"Description",reportName:"Nom du rapport",reportDesc:"Description",reportKeyword:"Mot-clé",reportInherit:"Hériter du rapport",inputMenuName:"Veuillez saisir le nom du groupe",inputMenuDesc:"Veuillez saisir la description",inputReportName:"Veuillez saisir le nom du rapport",inputReportDesc:"Veuillez saisir la description",inputReportKeyword:"Veuillez saisir le mot-clé",selectReportInherit:"Veuillez sélectionner le rapport à hériter",cancel:"Annuler",confirm:"Confirmer",successAddMenu:"Menu personnalisé ajouté avec succès",successEditMenu:"Menu personnalisé modifié avec succès",successDeleteMenu:"Menu personnalisé supprimé avec succès",successAddReport:"Rapport personnalisé ajouté avec succès",successEditReport:"Rapport personnalisé modifié avec succès",successDeleteReport:"Rapport personnalisé supprimé avec succès",successDeletePointGroup:"Groupe personnalisé (points) supprimé avec succès",errorAction:"Échec de l'opération",errorDelete:"Échec de la suppression",confirmDeleteMenu:"Êtes-vous sûr de vouloir supprimer ce menu personnalisé ?",confirmDeleteReport:"Êtes-vous sûr de vouloir supprimer ce rapport personnalisé ?",confirmDeletePointGroup:"Êtes-vous sûr de vouloir supprimer ce groupe personnalisé (points) ?",tip:"Conseil"},tree:{inputGroupName:"Veuillez saisir le nom du groupe",expandAll:"Tout développer",collapseAll:"Tout réduire"}},ga={device:{configure:{selectConfigure:"Veuillez sélectionner le fichier de symbole de configuration !",loading:"Chargement",operating:"Opération en cours",loadFailed:"Échec du chargement, raison :",getCustomDeviceFailed:"Échec de l'obtention de l'appareil personnalisé",registerDataFailed:"Échec de l'enregistrement des données, raison :",variablesNotExist:"Certaines variables n'existent pas :",getDataError:"Erreur d'obtention des données",remoteSet:"Télécommande",remoteControlFailed:"Échec de la télécommande, raison :",remoteControlSuccess:"Télécommande réussie",noRemoteControlType:"Type de télécommande non configuré",symbolChangeReload:"Rechargement après changement de symbole",deviceChangeReload:"Rechargement après changement d'appareil",deviceConnectReload:"Connexion d'appareil réussie, rechargement"},configureList:{searchPlaceholder:"Rechercher par mot-clé",deviceMonitor:"Surveillance d'appareil",newProject:"Nouveau projet",addCustomComponent:"Ajouter un composant personnalisé",newConfigure:"Nouvelle configuration",renameProject:"Renommer le projet",deleteProject:"Supprimer le projet",editConfigure:"Modifier la configuration",renameConfigure:"Renommer la configuration",deleteConfigure:"Supprimer la configuration",openFolder:"Ouvrir le dossier",inputProjectName:"Veuillez entrer le nom du projet (10 caractères maximum, pas de caractères spéciaux)",inputConfigureName:"Veuillez entrer le nom de la configuration (10 caractères maximum, pas de caractères spéciaux)",confirm:"Confirmer",cancel:"Annuler",invalidName:"Longueur ou contenu du nom invalide",projectAddSuccess:"Projet : {name} ajouté avec succès",projectAddFailed:"Projet : {name} échec de l'ajout, raison :",configureAddSuccess:"Configuration : {name} ajoutée avec succès",configureAddFailed:"Configuration : {name} échec de l'ajout, raison :",renameSuccess:"Renommage réussi",renameFailed:"Échec du renommage, raison :",confirmDelete:"Êtes-vous sûr de vouloir supprimer ?",confirmBatchDelete:"Ce projet contient des configurations associées, êtes-vous sûr de vouloir supprimer en lot ?",deleteSuccess:"Suppression réussie",deleteFailed:"Échec de la suppression, raison :",remoteSet:"Confirmation de Suppression"},configures:{customComponent:"Composant personnalisé",selectDevice:"Veuillez sélectionner l'appareil associé !",edit:"Modifier :"},customConfigure:{getCustomDeviceFailed:"Échec de l'obtention de l'appareil personnalisé",saveDeviceFailed:"Échec de l'enregistrement du symbole d'appareil",saveSuccess:"Enregistrement réussi",deleteDeviceFailed:"Échec de la suppression du symbole d'appareil",deleteSuccess:"Suppression réussie",tip:"Message d'information"},deviceList:{unnamed:"Appareil Sans Nom",connect:"Connecter",disconnect:"Déconnecter",edit:"Modifier",delete:"Supprimer",notFound:"Aucun appareil trouvé",editWarn:"Veuillez d'abord vous déconnecter avant de modifier",deleteWarn:"Veuillez d'abord vous déconnecter avant de supprimer",connectSuccess:"Appareil {name} : Connexion réussie",connectExist:"Appareil {name} : Connexion déjà existante",connectFailed:"Appareil {name} : Échec de connexion",connectFailedReason:"Raison de l'échec de connexion de l'appareil : {reason}",disconnectSuccess:"Appareil {name} : Déconnecté",disconnectedNotify:"Appareil {name} connexion déconnectée",currentDisconnectedNotify:"Connexion de l'appareil actuel déconnectée",operateFailed:"Appareil {name} : Échec de l'opération",remove:"Supprimer"},deviceSearch:{searchPlaceholder:"Rechercher un appareil"},editConfigure:{saveSuccess:"Enregistrement réussi",saveFailed:"Échec de l'enregistrement, raison :",loadFailed:"Échec du chargement, raison :",getCustomDeviceFailed:"Échec de l'obtention de l'appareil personnalisé",tip:"Message d'information"},remoteSet:{inputValue:"Valeur d'entrée",write:"Écrire",cancel:"Annuler",setFailed:"Échec de la télécommande, raison :",operateSuccess:"Opération réussie",noSetType:"Type de télécommande non configuré"}},graph:{component:{electricSymbols:"Symboles électriques",customComponents:"Composants personnalisés",basicComponents:"Composants de base"},toolbar:{undo:"Annuler",redo:"Rétablir",bringToFront:"Mettre au premier plan",sendToBack:"Mettre à l'arrière-plan",ratio:"Mise à l'échelle proportionnelle",delete:"Supprimer",save:"Enregistrer"},contextMenu:{group:"Grouper",ungroup:"Dégrouper",linkData:"Associer les données",equipmentSaddr:"Adresse de l'appareil"},dialog:{dataConfig:"Configuration des données",tip:"Message d'information",selectOneGraph:"Veuillez sélectionner un graphique"},message:{waitForCanvasInit:"Veuillez attendre l'initialisation du canevas",loadEquipmentFailed:"Échec du chargement du symbole",loadEquipmentError:"Échec du chargement de l'appareil",equipmentLoaded:"Chargement de l'appareil terminé"},basic:{title:"Composants de base",components:{line:"Ligne",text:"Texte",rectangle:"Rectangle",circle:"Cercle",ellipse:"Ellipse",triangle:"Triangle",arc:"Arc"}},selectEquipment:{sequence:"Index",name:"Nom",type:"Type",symbol:"Symbole",operation:"Opération",reference:"Référence"},setSAddr:{telemetry:"Télécommunication/Télémétrie",format:"Formatage",factor:"Facteur",remoteControl:"Télécommande",controlType:"Type de télécommande",controlValue:"Valeur de télécommande",remoteSet:"Télécommande",setType:"Type de télécommande",displayConfig:"Configuration d'affichage",addRow:"Ajouter une ligne",sequence:"Index",type:"Type",originalValue:"Valeur originale",displayValue:"Valeur d'affichage",operation:"Opération",text:"Texte",symbol:"Symbole",selectSymbol:"Sélectionner le symbole",confirm:"Confirmer",cancel:"Annuler",confirmDelete:"Êtes-vous sûr de vouloir supprimer ?",tip:"Message d'information",selectControl:"Contrôle sélectif",directControl:"Contrôle direct",controlClose:"Contrôle fermé",controlOpen:"Contrôle ouvert",cancelDelete:"Annuler la suppression"},equipmentType:{CBR:"Disjoncteur",DIS:"Sectionneur",GDIS:"Sectionneur de terre",PTR2:"Transformateur 2 enroulements",PTR3:"Transformateur 3 enroulements",VTR:"Transformateur de tension",CTR:"Transformateur de courant",EFN:"Dispositif de mise à la terre du neutre",IFL:"Ligne de sortie",EnergyConsumer:"Charge",GND:"Terre",Arrester:"Parafoudre",Capacitor_P:"Condensateur en parallèle",Capacitor_S:"Condensateur en série",Reactor_P:"Réactance en parallèle",Reactor_S:"Réactance en série",Ascoil:"Bobine d'extinction d'arc",Fuse:"Fusible",BAT:"Batterie",BSH:"Bushing",CAB:"Câble",LIN:"Ligne aérienne",GEN:"Générateur",GIL:"Ligne isolée au gaz",RRC:"Élément réactif rotatif",TCF:"Convertisseur de fréquence à commande thyristor",TCR:"Élément réactif à commande thyristor",LTC:"Changeur de prise",IND:"Inducteur"},equipmentName:{breaker_vertical:"Disjoncteur-vertical",breaker_horizontal:"Disjoncteur-horizontal",breaker_invalid_vertical:"Disjoncteur-invalide-vertical",breaker_invalid_horizontal:"Disjoncteur-invalide-horizontal",disconnector_vertical:"Sectionneur-vertical",disconnector_horizontal:"Sectionneur-horizontal",disconnector_invalid_vertical:"Sectionneur-invalide-vertical",disconnector_invalid_horizontal:"Sectionneur-invalide-horizontal",hv_fuse:"Fusible haute tension",station_transformer_2w:"Transformateur de poste (2 enroulements)",transformer_y_d_11:"Transformateur (Y/△-11)",transformer_d_y_11:"Transformateur (△/Y-11)",transformer_d_d:"Transformateur (△/△)",transformer_y_y_11:"Transformateur (Y/Y-11)",transformer_y_y_12_d_11:"Transformateur (Y/Y-12/△-11)",transformer_y_d_11_d_11:"Transformateur (Y/△-11/△-11)",transformer_y_y_v:"Transformateur (Y/Y/V)",transformer_autotransformer:"Transformateur (auto-transformateur)",voltage_transformer_2w:"Transformateur de tension (2 enroulements)",voltage_transformer_3w:"Transformateur de tension (3 enroulements)",voltage_transformer_4w:"Transformateur de tension (4 enroulements)",arrester:"Parafoudre",capacitor_horizontal:"Condensateur-horizontal",capacitor_vertical:"Condensateur-vertical",reactor:"Réactance",split_reactor:"Réactance divisée",power_inductor:"Inducteur de puissance",feeder:"Ligne de sortie",ground:"Terre",tap_changer:"Changeur de prise",connection_point:"Point de connexion",transformer_y_y_12_d_11_new:"Transformateur(Y/Y-12/△-11)(nouveau)",pt:"PT",arrester_new:"Parafoudre(nouveau)",disconnector_vertical_new:"Sectionneur-vertical(nouveau)",disconnector_horizontal_new:"Sectionneur-horizontal(nouveau)",arrester_new_vertical:"Parafoudre(nouveau)-vertical",disconnector_vertical_left_new:"Sectionneur-vertical-gauche(nouveau)"}},graphProperties:{blank:{propertySetting:"Paramètres de propriété"},graph:{canvasSetting:"Paramètres du canevas",grid:"Grille",backgroundColor:"Couleur d'arrière-plan"},group:{groupProperty:"Propriété de groupe",basic:"Base",width:"Largeur",height:"Hauteur",x:"Position(X)",y:"Position(Y)",angle:"Angle de rotation"},node:{nodeProperty:"Propriété de nœud",style:"Style",backgroundColor:"Couleur d'arrière-plan",borderWidth:"Largeur de bordure",borderColor:"Couleur de bordure",borderDasharray:"Style de bordure",rx:"Bordure rx",ry:"Bordure ry",position:"Position",width:"Largeur",height:"Hauteur",x:"Position(X)",y:"Position(Y)",property:"Propriété",angle:"Angle de rotation",zIndex:"Niveau(z)",fontFamily:"Police",fontColor:"Couleur de police",fontSize:"Taille de police",text:"Texte"},pathLine:{lineSetting:"Paramètres de ligne",style:"Style",lineHeight:"Largeur",lineColor:"Couleur",borderDasharray:"Bordure",position:"Position",width:"Largeur",height:"Hauteur",x:"Position(X)",y:"Position(Y)",property:"Propriété",angle:"Angle de rotation",zIndex:"Niveau(z)"}},business:{hmi:{title:"Gestion d'écran",form:{add:"Ajouter un écran",edit:"Modifier l'écran",view:"Voir l'écran",name:"Nom de l'écran",type:"Type d'écran",template:"Modèle d'écran",description:"Description",cancel:"Annuler",confirm:"Confirmer",validation:{name:"Veuillez entrer le nom de l'écran",type:"Veuillez sélectionner le type d'écran",template:"Veuillez sélectionner le modèle d'écran"}},columns:{name:"Nom de l'écran",type:"Type d'écran",template:"Modèle d'écran",createTime:"Date de création",updateTime:"Date de mise à jour",status:"Statut",operation:"Opération"},type:{device:"Écran d'appareil",process:"Écran de processus",alarm:"Écran d'alarme",custom:"Écran personnalisé"},status:{draft:"Brouillon",published:"Publié",archived:"Archivé"},editor:{title:"Édition d'écran",save:"Enregistrer",preview:"Aperçu",publish:"Publier",cancel:"Annuler",tools:{select:"Sélectionner",rectangle:"Rectangle",circle:"Cercle",line:"Ligne",text:"Texte",image:"Image",device:"Appareil",alarm:"Alarme",chart:"Graphique"},properties:{title:"Propriétés",position:"Position",size:"Taille",style:"Style",data:"Données",event:"Événement"}},preview:{title:"Aperçu d'écran",fullscreen:"Plein écran",exit:"Quitter",zoom:{in:"Zoom avant",out:"Zoom arrière",fit:"Adapter"}},publish:{title:"Publier l'écran",version:"Numéro de version",description:"Description de publication",cancel:"Annuler",confirm:"Confirmer",validation:{version:"Veuillez entrer le numéro de version",description:"Veuillez entrer la description de publication"}},template:{title:"Modèle d'écran",add:"Ajouter un modèle",edit:"Modifier le modèle",delete:"Supprimer le modèle",name:"Nom du modèle",category:"Catégorie de modèle",description:"Description",preview:"Aperçu",cancel:"Annuler",confirm:"Confirmer",validation:{name:"Veuillez entrer le nom du modèle",category:"Veuillez sélectionner la catégorie de modèle"}}}}},fa={common:{date:"Date",search:"Rechercher",save:"Enregistrer",clear:"Effacer",loading:"Chargement...",reportNo:"Numéro de rapport",time:"Heure",description:"Description",progress:"Progression",selectDateRange:"Veuillez sélectionner la plage de dates",noData:"Aucune donnée",saveSuccess:"Enregistrement réussi",saveFailed:"Échec de l'enregistrement"},date:"Date",search:"Rechercher",filter:"Filtrer",save:"Enregistrer",clearList:"Effacer la liste",loading:"Chargement...",reportNumber:"Numéro de rapport",time:"Heure",description:"Description",progress:"Progression",loadingText:"Chargement...",querying:"Requête en cours",selectCompleteTimeRange:"Veuillez sélectionner la plage de temps complète",noDataToSave:"Aucune donnée à enregistrer",saveSuccess:"Enregistrement réussi",saveReport:"Enregistrer le rapport",fileUploading:"Téléchargement de fichier en cours",fileUploadComplete:"Téléchargement de fichier terminé",autoRefresh:"Actualisation automatique",showHiddenItems:"Afficher les éléments masqués",hideHiddenItems:"Masquer les éléments masqués",name:"Nom",operationAddress:"Adresse d'opération",operationParams:"Paramètres d'opération",value:"Valeur",step:"Étape",source:"Source",sourceType:"Type de source",result:"Résultat",searchType:"Type de recherche",total:"Total {num} éléments",sameSearch:"Recherche de contenu identique",sameFilter:"Filtrage de contenu identique",showHideTime:"Afficher/masquer la colonne de temps",selectRowToOperate:"Veuillez d'abord sélectionner la ligne à opérer",trip:{autoRefresh:"Actualisation automatique"},operate:{name:"Nom",operateAddress:"Adresse d'opération",operateParam:"Paramètre d'opération",value:"Valeur",step:"Étape",source:"Source",sourceType:"Type de source",result:"Résultat"},group:{uploadWave:"Télécharger la forme d'onde",searchHistory:"Historique de recherche",saveResult:"Enregistrer le résultat",clearContent:"Effacer le contenu",contextMenu:{uploadWave:"Télécharger la forme d'onde",getHistoryReport:"Obtenir le rapport historique",saveResult:"Enregistrer le résultat",clearContent:"Effacer le contenu"},date:"Date",search:"Rechercher",save:"Enregistrer",clearList:"Effacer la liste",loading:"Chargement...",table:{reportId:"ID du rapport",time:"Heure",description:"Description"},progress:{title:"Progression",searching:"Recherche {type}",loading:"Chargement..."},refresh:{start:"Démarrer l'actualisation",stop:"Arrêter l'actualisation"},hiddenItems:{show:"Afficher les éléments masqués"},messages:{noFileToUpload:"Aucun fichier à télécharger",selectDateRange:"Veuillez sélectionner la plage de dates",noDataToSave:"Aucune donnée à enregistrer",saveReport:"Enregistrer le rapport",saveSuccess:"Enregistrement réussi"}},entryID:"Indice",module:"Nom du module",msg:"Contenu",level:"Niveau",type:"Type",origin:"Origine",user:"Nom d'utilisateur",exporting:"Exportation en cours...",stopRefresh:"Arrêter l'Actualisation",searchProgress:"Recherche {type}",pleaseSelectSavePath:"Veuillez sélectionner le chemin d'enregistrement...",saveFailed:"Échec de l'enregistrement",exportLogSuccess:"Exportation réussie : {path}",exportLogFailed:"Échec de l'exportation : {msg}",exportLogCancelled:"L'utilisateur a annulé l'opération d'exportation"},ha={device:ma,hmi:ga,report:fa},va={common:{add:"Ajouter",index:"Index",delete:"Supprimer",clear:"Effacer",import:"Importer",export:"Exporter",execute:"Exécuter",moveUp:"Monter",moveDown:"Descendre",loading:"Chargement...",success:"Succès",failed:"Échec",confirm:"Confirmer",cancel:"Annuler",yes:"Oui",no:"Non",operation:"Opération",tips:"Astuce",title:"Conseil"},search:{placeholder:"Rechercher une fonction"},functionList:{unnamedDevice:"Appareil sans nom",batchDownload:{name:"Téléchargement en lot",desc:"Téléchargement de fichiers en lot multi-appareils et import de valeurs fixes en lot"},xmlFormatter:{name:"Formateur XML",desc:"Organisation hiérarchique rapide des données XML"},jsonFormatter:{name:"Formateur JSON",desc:"Formatage intelligent des données JSON avec validation syntaxique"},radixConverter:{name:"Convertisseur de base",desc:"Support de conversion entre binaire, décimal, hexadécimal et autres bases"},temperatureConverter:{name:"Convertisseur de température",desc:"Conversion intelligente entre Celsius, Fahrenheit, Kelvin et autres unités de température"},encryption:{name:"Chiffrement/déchiffrement de texte",desc:"Chiffrement et déchiffrement rapide de texte basé sur les algorithmes AES, RSA, Base64, etc."},packageProgram:{name:"Emballage du programme",desc:"Emballer les fichiers d'exécution de l'appareil pour l'exportation, supporte le répertoire de sauvegarde personnalisé et la localisation du dossier en un clic"}},matrixContent:{loading:"Chargement...",tabs:{deviceList:"Appareils",downloadConfig:"Fichiers",paramConfig:"Paramètres"}},taskSteps:{connect:"Connecter",download:"Télécharger",import:"Importer",disconnect:"Déconnecter",complete:"Terminer"},messages:{connectDevice:"Connecter l'Appareil",executeFileDownload:"Exécuter le Téléchargement de Fichier",downloadingFile:"Téléchargement de Fichier",downloadFileFailed:"Échec du Téléchargement de Fichier",fileDownloadCompleted:"Téléchargement de Fichier Terminé",executeParamImport:"Exécuter l'Importation de Paramètres",paramValidationFailed:"Validation des Paramètres Échouée",paramImportFailed:"Importation des Paramètres Échouée",paramImportCompleted:"Importation des Paramètres Terminée",taskCompleted:"Tâche Terminée",deviceConnectionFailed:"Connexion de l'Appareil Échouée",deviceRebootSuccess:"Redémarrage de l'Appareil Réussi"},deviceList:{title:"Appareils",deviceListExcel:"Liste des appareils.xlsx",exportDeviceList:"Exporter la liste des appareils",importDeviceList:"Importer la liste des appareils",exportSuccess:"Export de la liste des appareils réussi",exportFail:"Échec de l'export de la liste des appareils",importSuccess:"Import de la liste des appareils réussi",importFail:"Échec de l'import de la liste des appareils",exportSuccessMsg:"Export de la liste des appareils réussi",exportFailMsg:"Échec de l'export de la liste des appareils",importSuccessMsg:"Import de la liste des appareils réussi",importFailMsg:"Échec de l'import de la liste des appareils : {msg}",deviceName:"Nom de l'appareil",deviceAddress:"Adresse de l'appareil",devicePort:"Port de l'appareil",isEncrypted:"Chiffré",encrypted:"Chiffré",notEncrypted:"Non chiffré",status:"Statut",operation:"Opération",reboot:"Redémarrer",noReboot:"Ne pas redémarrer",addDevice:"Ajouter un appareil",deleteDevice:"Supprimer l'appareil",clearDevices:"Effacer les appareils",deviceExists:"L'appareil existe déjà",deviceDeleted:"Appareil {ip} supprimé",downloadFile:"Télécharger le fichier",importParam:"Importer les valeurs fixes",connectTimeout:"Délai d'expiration de connexion",paramTimeout:"Délai d'expiration de modification des valeurs fixes",readTimeout:"Délai d'expiration global des requêtes",progress:"Progression"},downList:{title:"Téléchargement",deviceDirectory:"Répertoire de l'appareil",fileName:"Nom du fichier",fileSize:"Taille du fichier",filePath:"Chemin du fichier",lastModified:"Dernière modification",addFile:"Ajouter un fichier à télécharger",addFolder:"Ajouter un dossier à télécharger",fileExists:"Le fichier {path} existe déjà, ajout échoué !",fileDeleted:"Fichier {path} supprimé",filesDeleted:"Fichiers supprimés",defaultExportFileName:"Liste de fichiers à télécharger.xlsx",exportTitle:"Exporter la liste des fichiers à télécharger",importTitle:"Importer la liste des fichiers à télécharger",exportSuccess:"Liste de fichiers exportée avec succès",exportFailed:"Échec de l'export de la liste de fichiers",importSuccess:"Liste de fichiers importée avec succès",importFailed:"Échec de l'import de la liste de fichiers",fileExistsMsg:"Le fichier {path} existe déjà, ajout échoué !"},paramList:{title:"Paramètres",paramGroup:"Groupe de valeurs fixes",groupName:"Nom du groupe",paramName:"Nom du paramètre",paramDesc:"Description du paramètre",paramValue:"Valeur du paramètre",minValue:"Valeur minimale",maxValue:"Valeur maximale",step:"Pas",unit:"Unité",searchParamName:"Nom du paramètre",searchParamDesc:"Description du paramètre",importSuccess:"Import des valeurs fixes de paramètres d'appareil réussi",importFailed:"Échec de l'import des valeurs fixes de paramètres d'appareil",exportSuccess:"Export de la liste des valeurs fixes d'appareil réussi",exportFailed:"Échec de l'export de la liste des valeurs fixes d'appareil",clearSuccess:"Liste des valeurs fixes effacée"},progressDialog:{title:"En cours de traitement",pleaseWait:"Veuillez patienter..."},packageProgram:{saveDir:"Répertoire de sauvegarde",selectSaveDir:"Sélectionner le répertoire de sauvegarde",packageBtn:"Emballer",locateDir:"Localiser le dossier",delete:"Supprimer",sequence:"N°",fileName:"Nom du fichier",fileSize:"Taille du fichier",filePath:"Chemin du fichier",lastModified:"Dernière modification",operation:"Opération",saveDirEmpty:"Veuillez d'abord sélectionner le répertoire de sauvegarde !",packageSuccess:"Emballage du programme terminé !",tip:"Astuce",confirmButton:"OK",defaultExportFileName:"Liste des fichiers du package programme.xlsx",exportTitle:"Exporter la liste des fichiers du package programme",importTitle:"Importer la liste des fichiers du package programme",exportSuccess:"Liste de fichiers exportée avec succès",exportFailed:"Échec de l'export de la liste de fichiers",importSuccess:"Liste de fichiers importée avec succès",importFailed:"Échec de l'import de la liste de fichiers",fileExists:"Le fichier {path} existe déjà, ajout échoué !",selectDirSuccess:"Répertoire sélectionné : {dir}",locateDirSuccess:"Répertoire localisé : {dir}",addFileStart:"Ouverture du sélecteur de fichiers...",addFileSuccess:"{count} fichiers/dossiers ajoutés avec succès",addFileNone:"Aucun nouveau fichier/dossier ajouté",deleteSuccess:"{count} fichiers/dossiers supprimés avec succès",clearSuccess:"Tous les fichiers/dossiers ont été effacés",moveUpSuccess:"Déplacé vers le haut : {name}",moveDownSuccess:"Déplacé vers le bas : {name}",noFileSelected:"Veuillez d'abord sélectionner les fichiers à emballer !",noDeviceSelected:"Veuillez d'abord sélectionner les appareils à emballer !",packageFailed:"Échec de l'emballage : {msg}",openFileButton:"Ouvrir le fichier",zipPath:"Chemin du zip : {zipPath}"}},Ca={search:{placeholder:"Rechercher un appareil",button:"Rechercher",success:"Recherche réussie"},device2:{search:{placeholder:"Rechercher un appareil",add:"Ajouter un appareil",duplicate:"Cette IP et ce port existent déjà, veuillez ne pas les ajouter en double"},list:{empty:"Aucun appareil trouvé",unnamed:"Appareil sans nom",status:{connected:"Connecté",disconnected:"Déconnecté"},contextMenu:{connect:"Se connecter",edit:"Modifier",disconnect:"Se déconnecter",remove:"Supprimer"},message:{disconnectFirst:"Veuillez d'abord vous déconnecter avant de modifier",disconnectFirstDelete:"Veuillez d'abord vous déconnecter avant de supprimer",connectSuccess:"Appareil {name} : Connexion réussie",connectExists:"Appareil {name} : Connexion déjà existante",connectFailed:"Appareil {name} : Échec de connexion",connectFailedReason:"Raison de l'échec de connexion de l'appareil : {reason}",disconnected:"Appareil {name} : Déconnecté",operationFailed:"Appareil {name} : Échec de l'opération"}},report:{group:{openWaveConfirm:"Voulez-vous ouvrir le fichier de forme d'onde avec un outil tiers ?",tips:"Conseils",noWaveTool:"Chemin de l'outil de forme d'onde tiers non configuré"},common:{selectRow:"Veuillez sélectionner la ligne à opérer"}},backup:{savePath:"Chemin de Sauvegarde",setPath:"Veuillez définir le chemin de sauvegarde",setPathTitle:"Définir le chemin de sauvegarde",locateFolder:"Localiser le Dossier",startBackup:"Démarrer la Sauvegarde",cancelBackup:"Annuler la Sauvegarde",backup:"Sauvegarde",sequence:"Séquence",backupType:"Type de Sauvegarde",backupDesc:"Description de la Sauvegarde",progress:"Progrès",status:"Statut",noTypeSelected:"Veuillez sélectionner le type de sauvegarde",backupSuccess:"Sauvegarde réussie",backupFailed:"Sauvegarde échouée",openFolderFailed:"Échec de l'ouverture du dossier",backupTypes:{paramValue:"Valeurs des Paramètres",faultInfo:"Informations de Défaut",cidConfigPrjLog:"Journal de Projet de Configuration CID",waveReport:"Rapport d'Onde"},backupDescTypes:{paramValue:"Sauvegarder toutes les valeurs de configuration des paramètres de l'appareil",faultInfo:"Sauvegarder les informations d'enregistrement de défaut de l'appareil",cidConfigPrjLog:"Sauvegarder les fichiers de configuration CID et les journaux de projet",waveReport:"Sauvegarder les fichiers de rapport d'analyse de forme d'onde"},backupStatus:{userCancelled:"Annulé par l'Utilisateur",transferring:"Transfert en cours"},console:{pathNotSet:"Chemin de sauvegarde non défini, impossible de démarrer la sauvegarde",noTypeSelected:"Aucun type de sauvegarde sélectionné, impossible de démarrer la sauvegarde",startBackup:"Démarrer la sauvegarde, types: {types}, chemin: {path}",backupException:"Exception de sauvegarde: {error}",pathSelected:"Chemin de sauvegarde sélectionné: {path}",pathNotSelected:"Aucun chemin de sauvegarde sélectionné",pathNotSetForLocate:"Chemin de sauvegarde non défini, impossible de localiser le dossier",folderOpened:"Dossier de sauvegarde ouvert: {path}",openFolderFailed:"Échec de l'ouverture du dossier de sauvegarde: {error}",taskCompleted:"Tâche terminée",taskCancelled:"Tâche annulée",typeError:"Type [{type}] erreur: {error}",typeCompleted:"Type [{type}] sauvegarde terminée",typeCancelled:"Type [{type}] annulé",typeFailed:"Type [{type}] échoué"}},remoteControl:{directControl:"Contrôle Direct",selectControl:"Contrôle Sélectif"}}},xa={search:{placeholder:"Rechercher par mot-clé"},categories:{title:"📦Outils IT",formatting:"📝Outils de formatage",xml:"🟡Formatage XML",json:"🟡Formatage JSON",conversion:"🔄Outils de conversion",radix:"🟢Conversion de base",temperature:"🟢Conversion de température",encryption:"🔑Outils de chiffrement",textEncryption:"🔵Chiffrement/déchiffrement de texte"},encryption:{title:"Chiffrement/déchiffrement de texte",description:"Chiffrer et déchiffrer du texte en clair en utilisant des algorithmes de chiffrement (comme AES, TripleDES, Rabbit ou RC4)",encrypt:"Chiffrer",inputText:"Texte à chiffrer :",inputPlaceholder:"Veuillez entrer le contenu du texte à chiffrer...",key:"Clé :",keyPlaceholder:"Veuillez entrer la clé de chiffrement",algorithm:"Algorithme de chiffrement :",outputText:"Texte chiffré :",outputPlaceholder:"Le résultat du chiffrement s'affichera ici...",decrypt:"Déchiffrer",decryptInputText:"Texte à déchiffrer :",decryptInputPlaceholder:"Veuillez entrer le texte chiffré à déchiffrer...",decryptKey:"Clé :",decryptAlgorithm:"Algorithme de déchiffrement :",decryptOutputText:"Texte déchiffré :",decryptError:"Impossible de déchiffrer le texte"},json:{title:"Formatage JSON",description:"Formater une chaîne JSON en format lisible et convivial",sortKeys:"Tri des champs",indentSize:"Taille d'indentation",inputLabel:"JSON à formater",inputPlaceholder:"Collez votre JSON...",outputLabel:"JSON formaté",invalid:"Ce document ne respecte pas la spécification JSON, veuillez vérifier"},xml:{title:"Formatage XML",description:"Formater une chaîne XML en format lisible et convivial",collapseContent:"Réduire le contenu :",indentSize:"Taille d'indentation :",inputLabel:"Entrer le XML",inputPlaceholder:"Collez votre XML...",outputLabel:"XML formaté",invalid:"Ce document ne respecte pas la spécification XML, veuillez vérifier"},temperature:{title:"Conversion de température",description:"Conversion entre Kelvin, Celsius, Fahrenheit, Rankine, Delisle, Newton, Réaumur et Rømer",kelvin:"Kelvin",kelvinUnit:"K",celsius:"Celsius",celsiusUnit:"°C",fahrenheit:"Fahrenheit",fahrenheitUnit:"°F",rankine:"Rankine",rankineUnit:"°R",delisle:"Delisle",delisleUnit:"°De",newton:"Newton",newtonUnit:"°N",reaumur:"Réaumur",reaumurUnit:"°Ré",romer:"Rømer",romerUnit:"°Rø"},radix:{title:"Conversion de base",description:"Convertir des nombres entre différentes bases (décimal, hexadécimal, binaire, octal, base64...)",inputLabel:"Nombre à convertir",inputPlaceholder:"Entrez un nombre (ex: 100)",outputLabel:"Résultat de conversion",binary:"Base 2 (2)",binaryPlaceholder:"Résultat binaire...",octal:"Base 8 (8)",octalPlaceholder:"Résultat octal...",decimal:"Base 10 (10)",decimalPlaceholder:"Résultat décimal...",hex:"Base 16 (16)",hexPlaceholder:"Résultat hexadécimal...",base64:"Base64 (64)",base64Placeholder:"Résultat Base64...",customBase:"Base personnalisée",customBasePlaceholder:"Résultat Base {{base}}..."},jsonViewer:{title:"Formatage JSON",description:"Formater une chaîne JSON en format lisible et convivial",sortKeys:"Tri des champs",indentSize:"Taille d'indentation",inputJson:"JSON à formater",formattedJson:"JSON formaté",placeholder:"Collez votre JSON...",validationError:"Ce document ne respecte pas la spécification JSON. Veuillez vérifier"}},Sa={matrix:va,debug:Ca,tools:xa},ya={checkCard:{default:"Par défaut"},chooseModule:{title:"Sélectionner l'application",noModule:"Aucun module trouvé !",setDefault:"Définir par défaut",cancel:"Annuler",confirm:"Confirmer"},closer:{title:"Confirmation de sortie",message:"Voulez-vous vraiment quitter ?",confirm:"Confirmer",minimize:"Minimiser dans la barre des tâches",cancel:"Annuler"},codeHighLight:{noCode:"Aucun"},cropUpload:{title:"Recadrage d'image",zoomIn:"Zoom avant",zoomOut:"Zoom arrière",rotateLeft:"Rotation vers la gauche",rotateRight:"Rotation vers la droite",uploadImage:"Cliquez pour télécharger une image",uploadTip:"Veuillez télécharger un fichier image, recommandé de ne pas dépasser 2M",cancel:"Annuler",confirm:"Confirmer"},error:{forbidden:"Désolé, vous n'avez pas accès à cette page~🙅‍♂️🙅‍♀️",notFound:"Désolé, la page que vous visitez n'existe pas~🤷‍♂️🤷‍♀️",serverError:"Désolé, votre réseau a disparu~🤦‍♂️🤦‍♀️",back:"Retour"},form:{input:{placeholder:"Veuillez remplir {label}"},select:{placeholder:"Veuillez sélectionner {label}"},button:{add:"Ajouter",edit:"Modifier",delete:"Supprimer",view:"Voir"},search:{inputPlaceholder:"Veuillez saisir",selectPlaceholder:"Veuillez sélectionner",rangeSeparator:"à",startPlaceholder:"Heure de début",endPlaceholder:"Heure de fin"}},selectIcon:{title:"Sélection d'icône",placeholder:"Veuillez sélectionner une icône",searchPlaceholder:"Rechercher une icône",noSearchResult:"Aucune icône trouvée pour votre recherche~",moreIcons:"Plus d'icônes",enterIconifyCode:"Veuillez entrer le code iconify de l'icône souhaitée, ex: mdi:home-variant",iconifyAddress:"Adresse iconify",localIcons:"Icônes locales"},selector:{add:"Ajouter",addCurrent:"Ajouter l'actuel",addSelected:"Ajouter la sélection",delete:"Supprimer",deleteCurrent:"Supprimer l'actuel",deleteSelected:"Supprimer la sélection",cancel:"Annuler",confirm:"Confirmer",selected:"Sélectionné",maxSelect:"Sélection maximale",singleSelectOnly:"Une seule sélection possible",maxSelectLimit:"Sélection maximale de {count} éléments",person:"personne"},upload:{view:"Voir",edit:"Modifier",delete:"Supprimer",uploadImage:"Veuillez télécharger une image",uploadSuccess:"Image téléchargée avec succès !",uploadFailed:"Échec du téléchargement de l'image, veuillez réessayer !",invalidFormat:"Le format de l'image téléchargée ne correspond pas au format requis !",fileSizeExceeded:"La taille de l'image téléchargée ne peut pas dépasser {size}M !",maxFilesExceeded:"Vous ne pouvez télécharger que {limit} images maximum, veuillez en supprimer avant de télécharger !",fileSizeZero:"Le fichier {fileName} a une taille de 0, impossible de télécharger !",tips:"Conseils"},treeFilter:{searchPlaceholder:"Entrez des mots-clés pour filtrer",expandAll:"Développer tout",collapseAll:"Réduire tout",all:"Tout"},proTable:{search:{reset:"Réinitialiser",search:"Rechercher",expand:"Développer",collapse:"Réduire"},pagination:{total:"Total {total} éléments",pageSize:"éléments/page",goto:"Aller à",page:"page"},colSetting:{title:"Paramètres de colonnes",fixedLeft:"Afficher",fixedRight:"Triable",cancelFixed:"Annuler la fixation",reset:"Restaurer par défaut",confirm:"Confirmer",cancel:"Annuler"},table:{empty:"Aucune donnée"}},basicComponent:{title:"Composants de base",line:"Ligne",text:"Texte",rect:"Rectangle",circle:"Cercle",ellipse:"Ellipse",triangle:"Triangle",arc:"Arc"}},ba={equipmentList:{sequence:"Index",name:"Nom",type:"Type",operation:"Opération",preview:"Aperçu",copy:"Copier",delete:"Supprimer",confirmDelete:"Êtes-vous sûr de vouloir supprimer ?",tip:"Message d'information",error:"Erreur"},graphComponent:{deviceType:"Type d'appareil",deviceName:"Nom de l'appareil",save:"Enregistrer"},contextMenu:{group:"Grouper",ungroup:"Dégrouper",setStatus:"Définir le statut",copy:"Copier",delete:"Supprimer",rename:"Renommer"},graphCreate:{needTwoDevices:"L'interrupteur ou le sectionneur nécessite la sélection de deux symboles d'appareil",needCorrectStatus:"Veuillez définir les propriétés de statut correctes pour l'interrupteur ou le sectionneur",needOneDevice:"Veuillez sélectionner un symbole d'appareil"},graphDefine:{waitCanvasInit:"Veuillez attendre l'initialisation du canevas",selectOneGraph:"Veuillez sélectionner un symbole",tip:"Conseil"},setStatus:{open:"Ouvert",close:"Fermé",none:"Aucun"},graphTools:{undo:"Annuler",redo:"Rétablir",front:"Mettre au premier plan",back:"Mettre à l'arrière-plan",delete:"Supprimer",save:"Enregistrer",equipmentList:"Liste d'Équipements"},graphEditor:{dataConfig:"Configuration des données",loadEquipmentFailed:"Échec du chargement du symbole"}},Da={more:{importPathNotExists:"Le chemin d'importation n'existe pas",exportPathNotExists:"Le chemin d'exportation n'existe pas",selectCorrectConfigFile:"Veuillez sélectionner le bon fichier de configuration",exportProjectConfigException:"Exception lors de l'exportation de la configuration du projet",importProjectConfigException:"Exception lors de l'importation de la configuration du projet"}},Pa={...ra,...ua,...ha,...Sa,components:ya,graphDefine:ba,services:Da,Home:"Accueil"},Fa={language:{title:"Язык",zh:"Упрощенный китайский",en:"Английский",es:"Испанский",fr:"Французский",ru:"Русский",tooltip:"Многоязычность"},about:{title:"О программе",introduction:"Введение",description:"Инструмент отладки визуализационной платформы нового поколения, разработанный на основе новейшего технологического стека Vue3, TypeScript, Vite4, Pinia, Element-Plus, Electron и др.",versionInfo:"Информация о версии",toolName:"Название инструмента",version:"Номер версии",machineCode:"Код машины",loading:"Загрузка...",machineCodeError:"Получение не удалось",copySuccess:"Код машины скопирован в буфер обмена",copyError:"Копирование не удалось",versionFeatures:"Особенности версии",features:{visualTool:"Включает подключение инструментов визуализации, просмотр информации об устройстве, установленные значения, аналоговые значения, значения состояния, телесигнализацию, телеизмерения, телеуправление, отчеты, синхронизацию времени устройства, импорт/экспорт установленных значений, функции отладки переменных",configTool:"Включает предварительный просмотр инструментов конфигурации, добавление, редактирование, пользовательские символы, функции связывания информации об устройстве",themeTool:"Включает настройку тем, IT-инструменты, функции импорта/экспорта конфигурации устройства"}},footer:{copyright:"{version}"},header:{minimize:"Свернуть",maximize:"Развернуть",restore:"Восстановить",close:"Закрыть",company:{name:"Siyuan Electric",englishName:"Sieyuan"},collapse:{expand:"Развернуть устройство",fold:"Свернуть устройство",expandTool:"Развернуть список инструментов",foldTool:"Свернуть список инструментов"},breadcrumb:{home:"Главная"},assemblySize:{title:"Настройка размера",default:"По умолчанию",large:"Большой",small:"Маленький"},avatar:{profile:"Личный кабинет",switchApp:"Переключить приложение",logout:"Выйти",logoutConfirm:{title:"Дружеское напоминание",message:"Вы уверены, что хотите выйти?",confirm:"Подтвердить",cancel:"Отмена"},logoutSuccess:"Выход выполнен успешно!"},changeModule:{title:"Переключить модуль"},enginConfig:{configType:"Тип конфигурации",openDirectory:"Открыть каталог файлов",cancel:"Отмена",confirm:"Подтвердить",all:"Все",deviceList:"Список устройств",configureList:"Список конфигураций",exportSuccess:"Экспорт конфигурации успешен",importSuccess:"Импорт конфигурации успешен",disconnectDeviceFirst:"Сначала отключите подключенное устройство",overrideConfirm:"Список конфигураций уже существует, перезаписать?",warmTips:"Дружеское напоминание",importConfigFile:"Импорт файла конфигурации"},userInfo:{title:"Личная информация",cancel:"Отмена",confirm:"Подтвердить"},password:{title:"Изменить пароль",cancel:"Отмена",confirm:"Подтвердить"},globalSetting:{title:"Settings",tooltip:"Settings"},moreInfo:{title:"Больше",tooltip:"Больше",items:{importConfig:"Импорт Config",printScreen:"Скриншот",search:"Поиск в меню",exportConfig:"Экспорт Config",about:"О программе",help:"Помощь"},importConfig:{title:"Импорт Config",placeholder:"Выберите файл конфигурации"},exportConfig:{title:"Экспорт Config",placeholder:"Выберите каталог для экспорта"}},searchMenu:{placeholder:"Поиск в меню: поддерживает названия меню, пути",empty:"Нет меню"},theme:{title:"Тема",tooltip:"Тема"}},main:{maximize:{exit:"Выйти из полноэкранного режима"}},theme:{title:"Layout",quickTheme:{title:"Theme"},layoutSettings:{title:"Layout"},layout:{title:"Стиль макета",columns:"Колонки",classic:"Классический",transverse:"Горизонтальный",vertical:"Вертикальный"},global:{title:"Глобальная тема",primary:"Цвет темы",dark:"Темный режим",grey:"Серый режим",weak:"Режим для дальтоников",special:"Специальный режим"},mode:{light:"Светлый",dark:"Темный"},interface:{title:"Interface",watermark:"Водяной знак",breadcrumb:"Хлебные крошки",breadcrumbIcon:"Иконка хлебных крошек",tabs:"Панель вкладок",tabsIcon:"Иконка панели вкладок",footer:"Нижний колонтитул",drawerForm:"Форма выдвижного ящика"},presetThemes:{title:"Предустановленные темы",default:{name:"Тема по умолчанию",description:"Классическая синяя тема"},dark:{name:"Темная тема",description:"Темный режим для защиты глаз"},techBlue:{name:"Технологический синий",description:"Современный технологический синий цвет"},deepBlue:{name:"Глубокий синий",description:"Глубокий стабильный синий цвет"},nature:{name:"Природная тема",description:"Свежая зеленая серия"},forestGreen:{name:"Лесной зеленый",description:"Глубокий лесной зеленый цвет"},warm:{name:"Теплая тема",description:"Теплая оранжевая серия"},sunsetOrange:{name:"Закатный оранжевый",description:"Теплый закатный оранжевый цвет"},elegant:{name:"Элегантная тема",description:"Благородная фиолетовая серия"},lavender:{name:"Лаванда",description:"Мягкий лавандовый фиолетовый"},sakura:{name:"Сакура розовый",description:"Романтический розовый цвет сакуры"},rose:{name:"Розовый красный",description:"Страстный розово-красный цвет"},lime:{name:"Лаймовый зеленый",description:"Энергичный лаймово-зеленый цвет"},skyBlue:{name:"Небесно-голубой",description:"Чистый небесно-голубой цвет"},eyeCare:{name:"Режим защиты глаз",description:"Серая тема для защиты глаз"}},colors:{techBlue:{name:"Технологический синий",description:"Современное технологическое ощущение"},natureGreen:{name:"Природный зеленый",description:"Свежий и естественный"},vibrantOrange:{name:"Яркий оранжевый",description:"Теплая энергия"},elegantPurple:{name:"Элегантный фиолетовый",description:"Благородная элегантность"},romanticPink:{name:"Романтический розовый",description:"Нежная романтика"},freshCyan:{name:"Свежий голубой",description:"Свежий и изящный"},brightYellow:{name:"Яркий желтый",description:"Яркий и живой"},warmOrange:{name:"Теплый оранжевый",description:"Теплый и комфортный"},limeGreen:{name:"Лаймовый зеленый",description:"Свежий лайм"},deepBlue:{name:"Глубокий синий",description:"Глубокий и стабильный"},golden:{name:"Золотой",description:"Классический золотой"},chinaRed:{name:"Китайский красный",description:"Традиционный красный"}}},tabs:{moreButton:{refresh:"Обновить",closeCurrent:"Закрыть текущую",closeLeft:"Закрыть левые",closeRight:"Закрыть правые",closeOthers:"Закрыть другие",closeAll:"Закрыть все"}}},Ta={confirm:"Подтвердить",cancel:"Отмена",save:"Сохранить",delete:"Удалить",remove:"Убрать",edit:"Редактировать",add:"Добавить",search:"Поиск",reset:"Сброс",export:"Экспорт",import:"Импорт",upload:"Загрузить",download:"Скачать",preview:"Предпросмотр",print:"Печать",refresh:"Обновить",back:"Назад",next:"Далее",submit:"Отправить",loading:"Загрузка...",success:"Успешно",error:"Ошибка",warning:"Предупреждение",info:"Информация",index:"Номер",title:"Заголовок",operation:"Операция",execute:"Выполнить",clear:"Очистить",moveUp:"Переместить вверх",moveDown:"Переместить вниз",status:{active:"Активный",inactive:"Неактивный",enabled:"Включен",disabled:"Отключен",online:"В сети",offline:"Не в сети",pending:"В ожидании",completed:"Завершено",failed:"Неудачно"},time:{today:"Сегодня",yesterday:"Вчера",thisWeek:"На этой неделе",lastWeek:"На прошлой неделе",thisMonth:"В этом месяце",lastMonth:"В прошлом месяце",custom:"Пользовательский диапазон"},pagination:{total:"Всего",items:"элементов",page:"страница",perPage:"на странице",showing:"Показано",to:"до",of:"из"},validation:{required:"Это поле обязательно для заполнения",email:"Пожалуйста, введите действительный адрес электронной почты",phone:"Пожалуйста, введите действительный номер телефона",number:"Пожалуйста, введите действительное число",integer:"Пожалуйста, введите действительное целое число",min:"Минимальное значение {min}",max:"Максимальное значение {max}",length:"Длина должна быть {length}",minLength:"Минимальная длина {min}",maxLength:"Максимальная длина {max}"},message:{saveSuccess:"Сохранение успешно",deleteSuccess:"Удаление успешно",updateSuccess:"Обновление успешно",operationSuccess:"Операция успешна",operationFailed:"Операция не удалась",confirmDelete:"Вы уверены, что хотите удалить?",noData:"Нет данных",loading:"Загрузка...",networkError:"Ошибка сети, попробуйте еще раз",copySuccess:"Копирование успешно"},languageSyncWarning:"Синхронизация языка с бэкендом не удалась, но язык фронтенда успешно изменен",customFileSelector:{title:"Выберите файлы и папки",searchPlaceholder:"Поиск файлов или папок...",selectedItems:"Выбранные элементы",clearAll:"Очистить все",noItemsSelected:"Элементы не выбраны",cancel:"Отмена",confirm:"Подтвердить",loading:"Загрузка...",error:{loadFailed:"Загрузка не удалась",accessDenied:"Доступ запрещен",notFound:"Путь не найден"}},test:{languageSwitch:{title:"Тест переключения языка",progressDialog:"Тест диалога прогресса",showProgress:"Показать диалог прогресса",temperatureConverter:"Тест конвертера температуры",temperatureDesc:"Следующие названия единиц температуры должны автоматически обновляться после переключения языка:",reportNames:"Тест названий отчетов",reportDesc:"Следующие названия, связанные с отчетами, должны автоматически обновляться после переключения языка:",autoRefresh:"Автообновление",showHidden:"Показать скрытые элементы",instructions:"Инструкции по тестированию",step1:"Нажмите кнопку переключения языка в правом верхнем углу",step2:"Выберите другой язык (например, английский, испанский, французский)",step3:"Наблюдайте, обновляется ли текст на странице немедленно на новый язык"},errorPageTest:{title:"Тест кнопок страницы ошибок",description:"Тестирование отображения кнопок страницы ошибок в различных языковых средах",languageSelector:"Выбрать язык",buttonPreview:"Предпросмотр кнопки",errorPage:"Страница ошибки",textLength:"Длина текста",testLinks:"Тестовые ссылки",goto404:"Перейти на страницу 404",goto403:"Перейти на страницу 403",goto500:"Перейти на страницу 500",comparisonTable:"Таблица сравнения многоязычности",language:"Язык",buttonText:"Текст кнопки",preview:"Предпросмотр"}}},Ea={loading:{checking:"Проверка авторизации...",loading:"Загрузка..."},auth:{invalid:"Недействительная авторизация: {msg}",unknownError:"Неизвестная ошибка",checkFailed:"Проверка авторизации не удалась, проверьте сетевое подключение"}},Aa={layout:Fa,common:Ta,app:Ea},Na={dataScope:{title:"Селектор диапазона данных",selectOrg:"Выбрать организацию",orgList:"Список организаций",cancel:"Отмена",confirm:"Подтвердить"},grantResource:{title:"Авторизация ресурсов",warning:"Роли, не являющиеся суперадминистраторами, не могут получить авторизацию на ресурсы меню системных модулей",firstLevel:"Каталог первого уровня",menu:"Меню",buttonAuth:"Авторизация кнопок",cancel:"Отмена",confirm:"Подтвердить",selectDataScope:"Пожалуйста, выберите диапазон данных"}},Ra={dashboard:"Панель управления",system:"Управление системой",user:"Управление пользователями",role:"Управление ролями",menu:"Управление меню",changeModule:{title:"Изменить модуль->",belongModule:"Принадлежащий модуль",requiredModule:"Пожалуйста, выберите принадлежащий модуль"},debug:{title:"Debug",description:"Debug"},configure:{title:"Config",description:"Config"},tool:{title:"Tools",description:"Tools"},sysConfig:{title:"Settings",description:"Settings"}},wa={limit:{module:{title:"Название модуля",icon:"Иконка",status:"Статус",sort:"Сортировка",description:"Описание",createTime:"Время создания",operation:"Операция",add:"Добавить модуль",edit:"Редактировать модуль",delete:"Удалить модуль",deleteConfirm:"Удалить выбранный модуль",deleteConfirmWithName:"Удалить модуль 【{name}】",form:{title:"Пожалуйста, введите название модуля",status:"Пожалуйста, выберите статус",sort:"Пожалуйста, введите сортировку",icon:"Пожалуйста, выберите иконку"}},menu:{title:"Название меню",icon:"Иконка меню",type:"Тип меню",component:"Название компонента",path:"Адрес маршрута",componentPath:"Путь компонента",sort:"Сортировка",status:"Статус",description:"Описание",operation:"Операция",add:"Добавить меню",edit:"Редактировать меню",delete:"Удалить меню",deleteConfirm:"Удалить выбранное меню",deleteConfirmWithName:"Удалить меню 【{name}】",form:{title:"Пожалуйста, введите название меню",parent:"Пожалуйста, выберите родительское меню",type:"Пожалуйста, выберите тип меню",path:"Пожалуйста, введите адрес маршрута",component:"Пожалуйста, введите адрес компонента",sort:"Пожалуйста, введите сортировку",icon:"Пожалуйста, выберите иконку",status:"Пожалуйста, выберите статус",link:"Пожалуйста, введите адрес ссылки"}},button:{title:"Название кнопки",code:"Код кнопки",sort:"Сортировка",description:"Описание",operation:"Операция",add:"Добавить кнопку",edit:"Редактировать кнопку",delete:"Удалить кнопку",deleteConfirm:"Удалить выбранную кнопку",deleteConfirmWithName:"Удалить кнопку 【{name}】",batch:{title:"Пакетное добавление кнопок",shortName:"Краткое название разрешения",codePrefix:"Префикс кода",form:{shortName:"Пожалуйста, введите краткое название разрешения",codePrefix:"Пожалуйста, введите префикс кода"}},form:{title:"Пожалуйста, введите название кнопки",code:"Пожалуйста, введите код кнопки",sort:"Пожалуйста, введите сортировку"}},role:{title:"Название роли",org:"Принадлежащая организация",category:"Тип роли",status:"Статус",sort:"Сортировка",description:"Описание",createTime:"Время создания",operation:"Операция",add:"Добавить роль",edit:"Редактировать роль",delete:"Удалить роль",deleteConfirm:"Удалить выбранную роль",deleteConfirmWithName:"Удалить роль 【{name}】",grant:{resource:"Авторизованные ресурсы",permission:"Авторизованные разрешения",dataScope:"Область данных"},form:{title:"Пожалуйста, введите название роли",org:"Пожалуйста, выберите принадлежащую организацию",category:"Пожалуйста, выберите тип роли",status:"Пожалуйста, выберите статус"}},spa:{title:"Название SPA",icon:"Иконка",type:"Тип SPA",path:"Адрес маршрута",component:"Путь компонента",sort:"Сортировка",description:"Описание",createTime:"Время создания",operation:"Операция",add:"Добавить SPA",edit:"Редактировать SPA",delete:"Удалить SPA",deleteConfirm:"Удалить выбранное SPA",deleteConfirmWithName:"Удалить SPA 【{name}】",form:{title:"Введите название SPA",type:"Выберите тип SPA",path:"Введите адрес маршрута",component:"Введите адрес компонента",sort:"Введите сортировку",icon:"Выберите иконку",link:"Заполните адрес ссылки, например: http://www.baidu.com"}}}},Ia={config:{title:"Конфигурация системы",paramTitle:"Конфигурация параметров",systemName:"Название системы",systemVersion:"Версия системы",waveToolPath:"Путь к стороннему инструменту анализа волн",waveToolPathPlaceholder:"Пожалуйста, выберите путь к стороннему инструменту анализа волн",openDirectory:"Открыть каталог файлов",save:"Сохранить",reset:"Сброс",saveSuccess:"Сохранение успешно",selectWaveTool:"Выбрать инструмент анализа волн",paramRefreshTime:"Интервал обновления установленных значений (мс)",reportRefreshTime:"Интервал обновления отчетов (мс)",stateRefreshTime:"Интервал обновления значений состояния (мс)",variRefreshTime:"Интервал обновления переменных (мс)",configTitle:"Конфигурация",configKey:"Ключ конфигурации",configValue:"Значение конфигурации",remark:"Примечание",sortCode:"Сортировка",operation:"Операция",deleteConfirm:"Удалить конфигурацию【{key}】"}},ka={machineCode:"Код машины",activationCode:"Код активации",activationCodePlaceholder:"Пожалуйста, введите код активации",reset:"Сброс",activate:"Активировать",success:{title:"Активация успешна",message:"Система успешно активирована"},error:{unknown:"Активация не удалась: {msg}",network:"Активация не удалась, пожалуйста, проверьте сетевое подключение"}},La={title:"Управление одностраничными приложениями",list:{title:"Список одностраничных приложений",add:"Добавить одностраничное приложение",deleteSelected:"Удалить выбранные",deleteConfirm:"Вы уверены, что хотите удалить одностраничное приложение {title}?"},form:{title:"{opt} одностраничное приложение",basicSettings:"Основные настройки",functionSettings:"Функциональные настройки",name:"Название одностраничного приложения",type:"Тип одностраничного приложения",icon:"Иконка",path:"Адрес маршрута",pathPlaceholder:"Пожалуйста, заполните адрес маршрута, например: /home/<USER>",componentName:"Название компонента",componentPath:"Адрес компонента",linkPath:"Адрес ссылки",linkPathPlaceholder:"Пожалуйста, заполните адрес ссылки, например: http://www.baidu.com",sort:"Сортировка",description:"Описание",isHome:"Установить как главную страницу",isHide:"Скрыть страницу",isFull:"Полноэкранная страница",isAffix:"Закрепить вкладку",isKeepAlive:"Кэш маршрута",cancel:"Отмена",confirm:"Подтвердить",nameRequired:"Пожалуйста, введите название одностраничного приложения",typeRequired:"Пожалуйста, выберите тип одностраничного приложения",pathRequired:"Пожалуйста, введите адрес маршрута",componentNameRequired:"Пожалуйста, введите название компонента",componentPathRequired:"Пожалуйста, введите адрес компонента",sortRequired:"Пожалуйста, введите сортировку",iconRequired:"Пожалуйста, выберите иконку"}},za={title:"Вход в систему",account:{title:"Вход по аккаунту",username:"Пожалуйста, введите имя пользователя",password:"Пожалуйста, введите пароль",captcha:"Пожалуйста, введите код подтверждения",tenant:"Пожалуйста, выберите арендатора"},phone:{title:"Вход по номеру телефона",phone:"Пожалуйста, введите номер телефона",smsCode:"Пожалуйста, введите SMS-код подтверждения",getCode:"Получить код подтверждения",machineVerify:"Машинная проверка",captcha:"Пожалуйста, введите код подтверждения",sendSuccess:"Код подтверждения отправлен успешно",sendFailed:"Не удалось отправить SMS-код подтверждения"},button:{reset:"Сброс",login:"Войти"},dialog:{cancel:"Отмена",confirm:"Подтвердить"}},Ma={title:"Центр помощи",subtitle:"Помощь и документация",catalog:"Каталог",searchPlaceholder:"Поиск содержимого помощи...",loadFail:"Не удалось загрузить документацию помощи, пожалуйста, попробуйте позже."},Va={role:Na,menu:Ra,limit:wa,sys:Ia,activate:ka,spa:La,login:za,help:Ma},qa={configure:{remoteSet:"Дистанционная настройка"},console:{title:"Консоль",clear:"Очистить",selectAll:"Выбрать все",copy:"Копировать",copySuccess:"Копирование успешно",noTextSelected:"Нет выбранного текста",copyFailed:"Копирование не удалось",clearSuccess:"Консоль очищена",collapse:"Свернуть",expand:"Развернуть"},groupInfo:{title:"Информация о группе",table:{id:"Номер",name:"Название",desc:"Описание",fc:"FC",count:"Количество"},messages:{fetchDataError:"Произошла ошибка при получении данных",fetchedData:"Полученные данные:"}},treeClickLog:"Клик по дереву treeClick : ",contentView:"Просмотр содержимого",emptyDeviceId:"Текущий ID устройства пуст",invalidResponseStructure:"Недействительная структура ответа",formattedMenuDataLog:"Отформатированные данные меню ===",allSettings:"Все установленные значения",allEditSpSettings:"Все установленные значения одной зоны",allEditSgSettings:"Все установленные значения нескольких зон",deviceTreeDataLog:"Данные дерева устройств",failedToLoadMenu:"Не удалось загрузить меню устройства:",innerTabs:{contentView:"Просмотр содержимого",fileUpload:"Загрузка файла",fileDownload:"Скачивание файла",deviceTime:"Синхронизация времени устройства",deviceOperate:"Операции устройства",variableDebug:"Отладка переменных",oneClickBackup:"Резервное копирование одним кликом",entryConfig:"Конфигурация записей",tabClickLog:"Клик по вкладке:"},devices:{notConnectedAlt:"Устройство не подключено",pleaseConnect:"Пожалуйста, сначала подключите устройство!"},list:{unnamedDevice:"Безымянное устройство",connected:"Подключено",disconnected:"Отключено",connect:"Подключить",edit:"Редактировать",disconnect:"Отключить",remove:"Удалить",noDeviceFound:"Устройство не найдено",handleClickLog:"Клик handleListClick:",disconnectBeforeEdit:"Пожалуйста, сначала отключите соединение перед редактированием",connectSuccess:"Устройство {name}: подключение успешно",connectExist:"Устройство {name}: соединение уже существует",connectFailed:"Устройство {name}: подключение не удалось",connectFailedReason:"Причина неудачи подключения устройства:",disconnectedSuccess:"Устройство {name}: отключено",disconnectedNotify:"Устройство {name} соединение разорвано",currentDisconnectedNotify:"Текущее устройство соединение разорвано",operateFailed:"Устройство {name}: операция не удалась",disconnectBeforeDelete:"Пожалуйста, сначала отключите соединение перед удалением",dataLog:"Данные:",ipPortExist:"Этот IP и порт уже существуют, пожалуйста, не добавляйте повторно",messageMonitor:"Мониторинг сообщений",connectFirst:"Пожалуйста, сначала подключите устройство",messageMonitorOpened:"Устройство {name}: мониторинг сообщений открыт"},messageMonitor:{title:"Мониторинг сообщений",start:"Начать мониторинг",stop:"Остановить мониторинг",clear:"Очистить",export:"Экспорт",expand:"Развернуть",collapse:"Свернуть",close:"Закрыть",messageType:"Сообщение",noMessages:"Нет данных сообщений",noMessagesToExport:"Нет данных сообщений для экспорта",startSuccess:"Начат мониторинг сообщений",stopSuccess:"Остановлен мониторинг сообщений",clearSuccess:"Очистка сообщений успешна",exportSuccess:"Экспорт сообщений успешен",exportFailed:"Экспорт сообщений не удался",toggleFailed:"Переключение состояния мониторинга не удалось",pauseScroll:"Приостановить прокрутку",resumeScroll:"Возобновить прокрутку",monitoring:"Мониторинг",copy:"Копировать",copySuccess:"Сообщение скопировано в буфер обмена",copyFailed:"Копирование не удалось",autoScrollEnabled:"Автопрокрутка включена",autoScrollDisabled:"Автопрокрутка приостановлена",send:"Отправить",receive:"Получить",message:"Сообщение"},search:{placeholder:"Поиск устройства",ipPortExist:"Этот IP и порт уже существуют, пожалуйста, не добавляйте повторно"},summaryPie:{other:"Другое",title:"Соотношение количества установленных значений",subtext:"Установленные значения группы"},deviceInfo:{title:"Информация об устройстве",export:"Экспорт",exportTitle:"Экспорт информации об устройстве",exportLoading:"Экспорт основной информации об устройстве...",exportSuccess:"Экспорт основной информации об устройстве успешен",exportFailed:"Экспорт основной информации об устройстве не удался",getInfoFailed:"Получение информации об устройстве не удалось. Причина неудачи: {msg}",getInfoFailedEmpty:"Получение информации об устройстве не удалось. Причина неудачи: данные пусты!",defaultFileName:"Информация об устройстве.xlsx",confirm:"Подтвердить",tip:"Подсказка"},allParamSetting:{title:"Все установленные значения",autoRefresh:"Автообновление",refresh:"Обновить",confirm:"Подтвердить",import:"Импорт",export:"Экспорт",groupTitle:"Группа установленных значений:",allGroups:"Все",noDataToImport:"Нет данных для импорта",importSuccess:"Импорт установленных значений успешен",importFailed:"Импорт установленных значений не удался: {msg}",requestFailed:"Запрос не удался, пожалуйста, попробуйте позже",queryFailed:"Запрос установленных значений не удался: {msg}",unsavedChanges:"Есть несохраненные изменения, продолжить обновление?",confirmButton:"Подтвердить",cancelButton:"Отмена",alertTitle:"Подсказка",errorTitle:"Ошибка",noDataToConfirm:"Нет данных для подтверждения",confirmSuccess:"Обновление установленных значений успешно",confirmFailed:"Обновление установленных значений не удалось: ",responseLog:"Данные ответа:",continueAutoRefresh:"Продолжить включение автообновления",settingGroup:"Группа установленных значений",all:"Все",minValue:"Минимальное значение",maxValue:"Максимальное значение",step:"Шаг",unit:"Единица измерения",searchNamePlaceholder:"Введите название установленного значения для поиска",searchDescPlaceholder:"Введите описание установленного значения для поиска",autoRefreshWarning:"Изменение данных не разрешено при включенном автообновлении",invalidValue:"Значение {value} установленного значения {name} не находится в допустимом диапазоне",exportFileName:"Параметры устройства_Все установленные значения.xlsx",selectPathLog:"Выбрать путь: ",exportSuccess:"Экспорт списка установленных значений успешен"},variable:{autoRefresh:"Автообновление",variableName:"Имя переменной",inputVariableName:"Пожалуйста, введите имя переменной для добавления",refresh:"Обновить",add:"Добавить",confirm:"Подтвердить",import:"Импорт",export:"Экспорт",delete:"Удалить",noDataToConfirm:"Нет данных для подтверждения",warning:"Предупреждение",variableModifiedSuccess:"Изменение переменной успешно",variableModifiedFailed:"Изменение переменной не удалось, причина неудачи:",requestFailed:"Запрос не удался, пожалуйста, попробуйте позже",error:"Ошибка",success:"Успешно",variableAddSuccess:"Добавление переменной успешно",variableAddFailed:"Добавление переменной не удалось, причина неудачи:",variableDeleteSuccess:"Удаление переменной успешно",variableDeleteFailed:"Удаление переменной не удалось, причина неудачи:",exportSuccess:"Экспорт информации об отладочных переменных устройства успешен",exportFailed:"Экспорт информации об отладочных переменных устройства не удался, причина неудачи:",importSuccess:"Импорт информации об отладочных переменных устройства успешен",importFailed:"Импорт информации об отладочных переменных устройства не удался:",confirmRefresh:"Есть несохраненные изменения, продолжить обновление?",confirmAutoRefresh:"Есть несохраненные изменения, продолжить включение автообновления?",pleaseInputVariableName:"Пожалуйста, заполните имя переменной",exportTitle:"Экспорт отладочных переменных устройства",importTitle:"Импорт отладочных переменных устройства",defaultExportPath:"Отладочные переменные устройства.xlsx",title:"Отладка переменных",variableNamePlaceholder:"Пожалуйста, введите имя переменной для добавления",batchDelete:"Пакетное удаление",modifySuccess:"Изменение переменной успешно",modifyFailed:"Изменение переменной не удалось, причина неудачи: {msg}",alertTitle:"Предупреждение",successTitle:"Подсказка",confirmButton:"Подтвердить",cancelButton:"Отмена",sequence:"Номер",id:"ID",name:"Название",value:"Значение",type:"Тип",description:"Описание",address:"Адрес",operation:"Операция",enterVariableName:"Пожалуйста, введите имя переменной для добавления",responseLog:"Данные ответа:",addSuccess:"Добавление переменной успешно",addFailed:"Добавление переменной не удалось, причина неудачи:",addFailedWithName:"Добавление переменной {name} не удалось: {reason}",exportFileName:"Отладочные переменные устройства.xlsx",selectPathLog:"Выбрать путь:",exportSuccessLog:"Экспорт информации об отладочных переменных устройства успешен, {path}",exportFailedLog:"Экспорт информации об отладочных переменных устройства не удался, причина неудачи:",importFailedLog:"Импорт информации об отладочных переменных устройства не удался:",unsavedChanges:"Есть несохраненные изменения, продолжить обновление?",continueAutoRefresh:"Продолжить включение автообновления",tip:"Подсказка",sequenceNumber:"Номер",autoRefreshEditForbidden:"Редактирование запрещено в режиме автообновления",warningTitle:"Предупреждение",invalidNumber:"Недопустимое числовое значение: {value}",cancel:"Отмена"},backup:{sequence:"Номер",title:"Резервное копирование устройства",savePath:"Путь сохранения",setPath:"Установить путь сохранения резервной копии",setPathTitle:"Установить путь",startBackup:"Начать резервное копирование",cancelBackup:"Отменить резервное копирование",backup:"Резервное копирование",backupType:"Тип резервного копирования",progress:"Прогресс",status:"Статус",operation:"Операция",backupTypes:{paramValue:"Резервное копирование параметров установленных значений устройства",faultInfo:"Резервное копирование информации о неисправностях устройства",cidConfigPrjLog:"Резервное копирование cid/ccd/device_config/debug_info/prj/log",waveReport:"Резервное копирование файлов записи волн устройства"},backupDesc:"Описание содержимого резервной копии",backupDescTypes:{paramValue:"Экспорт установленных значений устройства (Экспорт установленных значений.xlsx)",faultInfo:"Экспорт информации о неисправностях устройства (отчеты о событиях/операциях/неисправностях/аудите)",cidConfigPrjLog:"Экспорт файлов конфигурации (CID/CCD, конфигурация XML, файлы журналов)",waveReport:"Экспорт файлов записи волн устройства (/wave/comtrade)"},locateFolder:"Найти папку",backupSuccess:"Резервное копирование успешно",backupFailed:"Резервное копирование не удалось",openFolderFailed:"Не удалось открыть папку",noTypeSelected:"Пожалуйста, сначала выберите тип резервного копирования",cancelSuccess:"Отмена успешна",cancelFailed:"Отмена не удалась",noBackupToCancel:"В настоящее время нет выполняющихся задач резервного копирования",noTaskIdFound:"ID задачи не найден, отмена невозможна",backupStatus:{starting:"Начало резервного копирования",userCancelled:"Отменено пользователем",transferring:"Передача"},console:{pathNotSet:"Путь резервного копирования не установлен, невозможно начать резервное копирование",noTypeSelected:"Тип резервного копирования не выбран, невозможно начать резервное копирование",startBackup:"Начало резервного копирования, тип: {types}, путь: {path}",backupException:"Исключение резервного копирования: {error}",pathSelected:"Выбран путь резервного копирования: {path}",pathNotSelected:"Путь резервного копирования не выбран",pathNotSetForLocate:"Путь резервного копирования не установлен, невозможно найти папку",folderOpened:"Открыта папка резервного копирования: {path}",openFolderFailed:"Не удалось открыть папку резервного копирования: {error}",taskCompleted:"Обработка задачи завершена",taskCancelled:"Задача отменена",typeError:"Ошибка типа [{type}]: {error}",typeCompleted:"Резервное копирование типа [{type}] завершено",typeCancelled:"Тип [{type}] отменен",typeFailed:"Тип [{type}] не удался",attemptCancel:"Попытка отменить задачу резервного копирования",noTaskIdFound:"ID задачи не найден, невозможно отменить резервное копирование",cancelSuccess:"Задача резервного копирования отменена",cancelFailed:"Отмена резервного копирования не удалась: {error}",cancelException:"Исключение отмены резервного копирования: {error}",singleCancelSuccess:"Отмена типа [{type}] успешна",singleCancelFailed:"Отмена типа [{type}] не удалась: {error}",singleCancelException:"Исключение отмены типа [{type}]: {error}"}},operate:{title:"Операции устройства",manualWave:"Ручная запись волн",resetDevice:"Сброс устройства",clearReport:"Очистить отчеты",clearWave:"Очистить записи волн",executing:"Выполнение...",selectOperation:"Пожалуйста, выберите операцию",success:{manualWave:"Ручная запись волн успешна",resetDevice:"Сброс устройства успешен",clearReport:"Очистка отчетов успешна",clearWave:"Очистка записей волн успешна"},fail:{manualWave:"Ручная запись волн не удалась, причина неудачи:",resetDevice:"Сброс устройства не удался, причина неудачи:",clearReport:"Очистка отчетов не удалась, причина неудачи:",clearWave:"Очистка записей волн не удалась, причина неудачи:"}},time:{title:"Синхронизация времени устройства",currentTime:"Текущее время",deviceTime:"Время устройства",selectDateTime:"Выбрать дату и время",milliseconds:"Миллисекунды",now:"Сейчас",read:"Читать",write:"Записать",readSuccess:"Чтение времени устройства успешно.",readFailed:"Чтение времени устройства не удалось: {msg}",readFailedInvalidFormat:"Чтение времени устройства не удалось: недопустимый формат времени",readFailedDataError:"Чтение времени устройства не удалось: ошибка формата данных времени",writeSuccess:"Запись времени устройства успешна.",writeFailed:"Запись времени устройства не удалась: {msg}",writeFailedInvalidFormat:"Запись времени устройства не удалась: недопустимый формат времени",millisecondsRangeError:"Диапазон значений миллисекунд должен быть между 0-999",unknownError:"Неизвестная ошибка"},reportOperate:{title:"Операции с отчетами",date:"Дата:",search:"Запрос",save:"Сохранить",clearList:"Очистить список",loading:"Загрузка данных",progress:{title:"Информация о прогрессе",loading:"Загрузка",searching:"Запрос {type}"},table:{reportId:"Номер отчета",name:"Название",time:"Время",operationAddress:"Адрес операции",operationParam:"Параметр операции",value:"Значение",step:"Шаг",source:"Источник",sourceType:"Тип источника",result:"Результат"},messages:{selectDateRange:"Пожалуйста, выберите полный временной диапазон",noDataToSave:"Нет данных для сохранения",saveSuccess:"Сохранение успешно",saveReport:"Сохранить отчет"}},reportGroup:{title:"Группа отчетов",date:"Дата:",search:"Запрос",save:"Сохранить",clearList:"Очистить список",autoRefresh:"Автообновление",loading:"Загрузка данных",progress:{title:"Информация о прогрессе",loading:"Загрузка",searching:"Запрос {type}"},table:{reportId:"Номер отчета",time:"Время",description:"Описание"},contextMenu:{uploadWave:"Вызвать запись волн",getHistoryReport:"Получить исторический отчет",saveResult:"Сохранить результат",clearContent:"Очистить содержимое страницы"},messages:{selectDateRange:"Пожалуйста, выберите полный временной диапазон",noDataToSave:"Нет данных для сохранения",noFileToUpload:"Нет файлов для вызова",saveSuccess:"Сохранение успешно",saveReport:"Сохранить отчет",waveToolNotConfigured:"Путь к стороннему инструменту анализа волн не настроен",waveFileUploading:"Вызов файла записи волн",waveFileUploadComplete:"Вызов файла записи волн завершен",waveFileUploadCompleteWithPath:"Вызов файла записи волн завершен, путь: {path}",openWaveFileConfirm:"Открыть файл волн через сторонний инструмент?",openWaveFileTitle:"Дружеское напоминание",confirm:"Подтвердить",cancel:"Отмена"},refresh:{stop:"Остановить обновление",start:"Автообновление"},hiddenItems:{show:"Показать скрытые элементы",hide:"Не показывать скрытые элементы"}},fileDownload:{title:"Скачивание файлов",deviceDirectory:"Каталог устройства",reboot:"Перезагрузить",noReboot:"Не перезагружать",selectFile:"Выбрать файл",addDownloadFile:"Добавить файл для скачивания",addDownloadFolder:"Добавить папку для скачивания",addDownloadFilesAndFolders:"Добавить файлы и папки",downloadFile:"Скачать файл",cancelDownload:"Отменить скачивание",importList:"Импорт списка",exportList:"Экспорт списка",batchDelete:"Пакетное удаление",clearList:"Очистить список",download:"Скачать",delete:"Удалить",fileName:"Название файла",fileSize:"Размер файла",filePath:"Путь к файлу",lastModified:"Время последнего изменения",progress:"Прогресс",status:"Статус",operation:"Операция",folder:"Папка",waitingDownload:"Ожидание скачивания",calculatingFileInfo:"Вычисление информации о файле",downloadPreparing:"Подготовка к скачиванию",downloading:"Скачивание......",downloadComplete:"Скачивание завершено",downloadError:"Ошибка скачивания:",userCancelled:"Отменено пользователем",allFilesComplete:"Скачивание завершено",fileExists:"Файл {path} уже существует, добавление не удалось!",selectValidFile:"Пожалуйста, выберите допустимый файл для операции скачивания",remotePathEmpty:"Удаленный путь не может быть пустым",noDownloadTask:"Не получена задача скачивания, отмена невозможна",fileSizeZero:"Файл {fileName} имеет размер 0, скачивание невозможно",downloadCancelled:"Отмена скачивания файла {path} завершена",downloadCancelledFailed:"Отмена скачивания файла {path} не удалась, причина неудачи: {msg}",fileDeleted:"Удаление файла {path} завершено",exportSuccess:"Экспорт списка файлов для скачивания успешен",exportFailed:"Экспорт списка файлов для скачивания не удался",importSuccess:"Импорт списка файлов для скачивания успешен",importFailed:"Импорт списка файлов для скачивания не удался: {msg}",downloadList:"Список файлов для скачивания",exportTitle:"Экспорт списка файлов для скачивания",importTitle:"Импорт списка файлов для скачивания",error:"Ошибка",tip:"Подсказка",confirm:"Подтвердить",sequence:"Номер",confirmButton:"Подтвердить",cancelButton:"Отмена",alertTitle:"Подсказка",errorTitle:"Ошибка",successTitle:"Успешно",warningTitle:"Предупреждение",loading:"Загрузка",executing:"Выполнение...",noData:"Нет данных",selectDateRange:"Пожалуйста, выберите диапазон дат",search:"Поиск",save:"Сохранить",clear:"Очистить",refresh:"Обновить",stop:"Остановить",start:"Начать",show:"Показать",hide:"Скрыть",showHiddenItems:"Показать скрытые элементы",hideHiddenItems:"Скрыть элементы",continue:"Продолжить",cancel:"Отмена",confirmImport:"Подтвердить импорт",confirmExport:"Подтвердить экспорт",confirmDelete:"Подтвердить удаление",confirmClear:"Подтвердить очистку",confirmCancel:"Подтвердить отмену",confirmContinue:"Подтвердить продолжение",confirmStop:"Подтвердить остановку",confirmStart:"Подтвердить запуск",confirmShow:"Подтвердить показ",confirmHide:"Подтвердить скрытие",confirmRefresh:"Подтвердить обновление",confirmSave:"Подтвердить сохранение",confirmSearch:"Подтвердить поиск",confirmClearList:"Подтвердить очистку списка",confirmImportList:"Подтвердить импорт списка",confirmExportList:"Подтвердить экспорт списка",confirmBatchDelete:"Подтвердить пакетное удаление",confirmDownload:"Подтвердить скачивание",confirmCancelDownload:"Подтвердить отмену скачивания",confirmDeleteFile:"Подтвердить удаление файла",confirmClearFiles:"Подтвердить очистку файлов",confirmImportFiles:"Подтвердить импорт файлов",confirmExportFiles:"Подтвердить экспорт файлов",confirmBatchDeleteFiles:"Подтвердить пакетное удаление файлов",confirmDownloadFiles:"Подтвердить скачивание файлов",confirmCancelDownloadFiles:"Подтвердить отмену скачивания файлов",rename:"Переименование при скачивании",renamePlaceholder:"Переименовать при скачивании (необязательно)",renameCopyFailed:"Переименование копии файла не удалось:",packageProgram:"Упаковка программы",selectSaveDir:"Выбрать каталог сохранения",packageBtn:"Упаковать",locateDir:"Найти папку",saveDirEmpty:"Пожалуйста, сначала выберите каталог сохранения!",packageSuccess:"Упаковка программы завершена!",packageFailed:"Упаковка не удалась: {msg}",noFileSelected:"Пожалуйста, сначала выберите файлы для упаковки!",zipPath:"Путь упаковки: {zipPath}",addToDownload:"Добавить в интерфейс скачивания",fileAdded:"Файл добавлен в список скачивания: {path}",rebootSuccess:"Перезагрузка устройства успешна",rebootFailed:"Перезагрузка устройства не удалась: {msg}"},fileUpload:{serialNumber:"Номер",title:"Загрузка файлов",importList:"Импорт списка",exportList:"Экспорт списка",batchDelete:"Пакетное удаление",clearList:"Очистить список",upload:"Загрузить",cancelUpload:"Отменить загрузку",delete:"Удалить",sequence:"Номер",fileName:"Название файла",fileSize:"Размер файла",filePath:"Путь к файлу",lastModified:"Время последнего изменения",progress:"Прогресс",statusTitle:"Статус",status:{waiting:"Ожидание загрузки",preparing:"Подготовка к загрузке",uploading:"Загрузка......",completed:"Загрузка завершена",error:"Ошибка загрузки:",cancelled:"Отменено пользователем"},operation:"Операция",calculatingFileInfo:"Вычисление информации о файле",uploadPreparing:"Подготовка к загрузке",uploading:"Загрузка......",uploadComplete:"Загрузка завершена",uploadError:"Ошибка загрузки: {errorMsg}",userCancelled:"Отменено пользователем",allFilesComplete:"Загрузка завершена",fileExists:"Файл {path} уже существует, добавление не удалось!",invalidFile:"Пожалуйста, выберите допустимый файл для операции загрузки",emptySavePath:"Путь сохранения файла не может быть пустым",fileUploadComplete:"Загрузка файла {fileName} завершена",selectPath:"Выбрать путь",pathOptions:{shr:"/shr",configuration:"/shr/configuration",log:"/log",wave:"/wave",comtrade:"/wave/comtrade"},deviceDirectory:"Каталог устройства",savePath:"Путь сохранения",setPath:"Установить путь",getFiles:"Получить файлы",uploadFiles:"Загрузить файлы",errors:{invalidFile:"Пожалуйста, выберите допустимый файл для операции загрузки",emptySavePath:"Путь сохранения файла не может быть пустым",noUploadTask:"Не получена задача загрузки, отмена невозможна",getFilesFailed:"Не удалось получить файлы каталога устройства",fileSizeZero:"Файл {fileName} имеет размер 0, загрузка невозможна"},messages:{uploadCompleted:"Загрузка файла завершена",uploadCancelled:"Отмена загрузки файла завершена",clearListSuccess:"Очистка списка файлов успешна"}},info:{title:"Информация об устройстве",export:"Экспорт",exportSuccess:"Экспорт основной информации об устройстве успешен",exportFailed:"Экспорт основной информации об устройстве не удался",exportTip:"Подсказка",confirm:"Подтвердить",exportLoading:"Экспорт основной информации об устройстве...",getInfoFailed:"Получение информации об устройстве не удалось. Причина неудачи:",dataEmpty:"Данные пусты!"},summary:{title:"Обзор группировки устройств",basicInfo:"Основная информация",settingTotal:"Общее количество установленных значений",telemetry:"Телеизмерения",teleindication:"Телесигнализация",telecontrol:"Телеуправление",driveOutput:"Выходная передача",settingRatio:"Соотношение установленных значений"},dict:{refresh:"Обновить",confirm:"Подтвердить",import:"Импорт",export:"Экспорт",sequence:"Номер",shortAddress:"Короткий адрес",shortAddressTooltip:"Введите короткий адрес для поиска",chinese:"Китайский",english:"Английский",spanish:"Испанский",french:"Французский",russian:"Русский",operation:"Операция",confirmLog:"Подтвердить словарь",importLog:"Импорт словаря",exportLog:"Экспорт словаря",refreshLog:"Обновить словарь",newValueLog:"Новое значение:",loadSuccess:"Загрузка записей успешна",loadFailed:"Загрузка записей не удалась",saveSuccess:"Сохранение записей успешно",saveFailed:"Сохранение записей не удалось",partialFailed:"Сохранение части записей не удалось",noChanges:"Нет измененных записей",confirmMessage:"Вы уверены, что хотите сохранить измененные записи?"},allParamCompare:{title:"Сравнение различий импорта всех установленных значений",cancel:"Отмена",confirm:"Подтвердить импорт",groupName:"Название группы",name:"Название",description:"Описание",minValue:"Минимальное значение",maxValue:"Максимальное значение",step:"Шаг",unit:"Единица измерения",address:"Адрес",oldValue:"Старое значение",newValue:"Новое значение",sequence:"Номер",searchName:"Введите название установленного значения для поиска",searchDescription:"Введите описание установленного значения для поиска",messages:{noSelection:"Не выбраны данные",error:"Ошибка"}},deviceForm:{title:{add:"Добавить устройство",edit:"Редактировать устройство"},name:"Название устройства",ip:"IP-адрес",port:"Порт",connectTimeout:"Время ожидания подключения (мс)",readTimeout:"Глобальное время ожидания запроса (мс)",paramTimeout:"Время ожидания изменения установленного значения (мс)",encrypted:"Зашифрованное соединение",advanced:{show:"Развернуть дополнительные опции",hide:"Свернуть дополнительные опции"},buttons:{cancel:"Отмена",confirm:"Подтвердить"},messages:{nameRequired:"Пожалуйста, введите название устройства",nameTooLong:"Название устройства не должно быть слишком длинным",invalidIp:"Пожалуйста, введите действительный IP-адрес",invalidPort:"Номер порта должен быть между 1-65535",timeoutTooShort:"Время ожидания не должно быть слишком коротким"}},paramCompare:{title:"Сравнение различий импорта установленных значений",cancel:"Отмена",confirm:"Подтвердить импорт",sequence:"Номер",name:"Название",description:"Описание",minValue:"Минимальное значение",maxValue:"Максимальное значение",step:"Шаг",unit:"Единица измерения",address:"Адрес",oldValue:"Старое значение",newValue:"Новое значение",searchName:"Введите название установленного значения для поиска",searchDescription:"Введите описание установленного значения для поиска",messages:{noSelection:"Не выбраны данные",error:"Ошибка"}},progress:{title:"Информация о прогрессе",executing:"Выполнение..."},remoteYm:{title:"Дистанционные импульсы",sequence:"Номер",shortAddress:"Короткий адрес",description:"Описание",value:"Значение",operation:"Операция",inputShortAddressFilter:"Введите фильтр по короткому адресу",inputDescriptionFilter:"Введите фильтр по описанию",invalidData:"Значение {value} данных {name} недопустимо",error:"Ошибка",success:"Успешно",executeSuccess:"Выполнение успешно",prompt:"Подсказка",confirmButton:"Подтвердить",confirmExecute:"Подтвердить выполнение",confirm:"Подтвердить",executeButton:"Выполнить",cancelButton:"Отмена"},remoteYt:{title:"Дистанционное регулирование",sequence:"Номер",directControl:"Прямое управление",selectControl:"Селективное управление",shortAddress:"Короткий адрес",description:"Описание",value:"Значение",operation:"Операция",inputShortAddressFilter:"Введите фильтр по короткому адресу",inputDescriptionFilter:"Введите фильтр по описанию",invalidData:"Значение {value} данных {name} недопустимо",error:"Ошибка",success:"Успешно",executeSuccess:"Выполнение успешно",prompt:"Подсказка",confirm:"Подтвердить",errorInfo:"Информация об ошибке",executeFailed:"Выполнение дистанционного регулирования не удалось, причина неудачи: {msg}",executeSuccessLog:"Дистанционное регулирование {desc} выполнено успешно",cancelSuccess:"Отмена успешна",cancelFailed:"Отмена дистанционного регулирования не удалась, причина неудачи: {msg}",selectSuccess:"Выбор успешен, выполнить?",confirmInfo:"Информация подтверждения",execute:"Выполнить",cancel:"Отмена"},paramSetting:{title:"Параметры установленных значений устройства",autoRefresh:"Автообновление",refresh:"Обновить",confirm:"Подтвердить",import:"Импорт",export:"Экспорт",currentEditArea:"Текущая рабочая область",selectEditArea:"Текущая область редактирования",noDataToImport:"Нет данных для импорта",noDataToConfirm:"Нет данных для подтверждения",importSuccess:"Импорт установленных значений успешен",importFailed:"Импорт установленных значений не удался",updateSuccess:"Обновление установленных значений успешно",updateFailed:"Обновление установленных значений не удалось",requestFailed:"Запрос не удался, пожалуйста, попробуйте позже",setEditArea:"Настроить",setEditAreaTitle:"Настроить область редактирования",setEditAreaSuccess:"Настройка области редактирования успешна",modifiedWarning:"Есть несохраненные изменения, продолжить обновление?",autoRefreshWarning:"Есть несохраненные изменения, продолжить включение автообновления?",autoRefreshDisabled:"Изменение данных не разрешено при включенном автообновлении",invalidValue:"Значение {value} установленного значения {name} не находится в допустимом диапазоне",exportSuccess:"Экспорт параметров установленных значений устройства успешен",exportFailed:"Экспорт параметров установленных значений устройства не удался",noDiffData:"Не получены данные различий",table:{index:"Номер",name:"Название",description:"Описание",value:"Значение",minValue:"Минимальное значение",maxValue:"Максимальное значение",step:"Шаг",address:"Адрес",unit:"Единица измерения",operation:"Операция"},search:{namePlaceholder:"Введите название установленного значения для поиска",descPlaceholder:"Введите описание установленного значения для поиска"}},remoteControl:{title:"Дистанционное управление",sequence:"Номер",shortAddress:"Короткий адрес",description:"Описание",control:"Управление размыканием/замыканием",type:"Тип",operation:"Операция",directControl:"Прямое управление",selectControl:"Селективное управление",controlClose:"Управление размыканием",controlOpen:"Управление замыканием",noCheck:"Без проверки",syncCheck:"Проверка синхронизации",deadCheck:"Проверка отсутствия напряжения",confirmInfo:"Информация подтверждения",execute:"Выполнить",cancel:"Отмена",confirm:"Подтвердить",success:"Успешно",failed:"Не удалось",errorInfo:"Информация об ошибке",promptInfo:"Информация подсказки",confirmSuccess:"Выбор успешен, выполнить?",executeSuccess:"Выполнение успешно",cancelSuccess:"Отмена успешна",executeFailed:"Выполнение дистанционного управления не удалось, причина неудачи:",cancelFailed:"Отмена дистанционного управления не удалась, причина неудачи:",remoteExecuteSuccess:"Выполнение дистанционного управления успешно",remoteCancelSuccess:"Отмена дистанционного управления успешна"},remoteDrive:{action:"Действие",executeSuccess:"Выполнение успешно",executeFailed:"Выполнение не удалось",prompt:"Информация подсказки",error:"Информация об ошибке",confirm:"Подтвердить",shortAddress:"Короткий адрес",description:"Описание",operation:"Операция",enterToFilter:"Введите фильтр по короткому адресу",enterToFilterDesc:"Введите фильтр по описанию",actionSuccess:"Выполнение действия успешно",actionFailed:"Выполнение действия не удалось",failureReason:"Причина неудачи",sequence:"Номер"},remoteSignal:{autoRefresh:"Автообновление",refresh:"Обновить",export:"Экспорт",sequence:"Номер",name:"Название",description:"Описание",value:"Значение",quality:"Качество",searchName:"Введите название для поиска",searchDesc:"Введите описание для поиска",searchValue:"Введите значение для поиска",exportTitle:"Экспорт информации о телесигнализации устройства",exportSuccess:"Экспорт информации о телесигнализации устройства успешен",exportFailed:"Экспорт информации о телесигнализации устройства не удался",exportSuccessWithPath:"Экспорт информации о телесигнализации устройства успешен,",exportFailedWithError:"Экспорт информации о телесигнализации устройства не удался:",invalidData:"Недопустимые данные:",errorInDataCallback:"Ошибка обработки обратного вызова данных:",errorFetchingData:"Ошибка при получении данных:"},remoteTelemetry:{autoRefresh:"Автообновление",refresh:"Обновить",export:"Экспорт",sequence:"Номер",name:"Название",description:"Описание",value:"Значение",unit:"Единица измерения",quality:"Качество",searchName:"Введите название для поиска",searchDesc:"Введите описание для поиска",searchValue:"Введите значение для поиска",exportTitle:"Экспорт информации о состоянии устройства",exportSuccess:"Экспорт информации о состоянии устройства успешен",exportFailed:"Экспорт информации о состоянии устройства не удался",exportSuccessWithPath:"Экспорт информации о состоянии устройства успешен,",exportFailedWithError:"Экспорт информации о состоянии устройства не удался:",confirm:"Подтвердить",tip:"Подсказка",exportFileName:"Информация о состоянии устройства",selectPathLog:"Выбрать путь:"},remote:{directControl:"Прямое управление",selectControl:"Селективное управление",serialNumber:"Номер",shortAddress:"Короткий адрес",description:"Описание",value:"Значение",operation:"Операция",inputShortAddressFilter:"Введите фильтр по короткому адресу",inputDescriptionFilter:"Введите фильтр по описанию",invalidData:"Значение {value} данных {name} недопустимо",error:"Ошибка",success:"Успешно",executeSuccess:"Выполнение успешно",prompt:"Информация подсказки",confirm:"Подтвердить",errorInfo:"Информация об ошибке",executeFailed:"Выполнение дистанционного регулирования не удалось, причина неудачи: {msg}",executeSuccessLog:"Дистанционное регулирование {desc} выполнено успешно",cancelSuccess:"Отмена успешна",cancelFailed:"Отмена дистанционного регулирования не удалась, причина неудачи: {msg}",selectSuccess:"Выбор успешен, выполнить?",confirmInfo:"Информация подтверждения",execute:"Выполнить",cancel:"Отмена"},report:{uploadWave:"Вызвать запись волн",searchHistory:"Получить исторический отчет",saveResult:"Сохранить результат",clearContent:"Очистить содержимое страницы",date:"Дата",query:"Запрос",save:"Сохранить",autoRefresh:"Автообновление",stopRefresh:"Остановить обновление",clearList:"Очистить список",progressInfo:"Информация о прогрессе",loading:"Загрузка данных",reportNo:"Номер отчета",time:"Время",description:"Описание",noFileToUpload:"Нет файлов для вызова",uploadSuccess:"Вызов файла записи волн завершен",uploadPath:"Вызов файла записи волн завершен, путь:",noDataToSave:"Нет данных для сохранения",saveSuccess:"Сохранение успешно",saveReport:"Сохранить отчет",openWaveConfirm:"Открыть файл волн через сторонний инструмент?",confirm:"Подтвердить",cancel:"Отмена",waveToolNotConfigured:"Путь к стороннему инструменту анализа волн не настроен",pleaseSelectTimeRange:"Пожалуйста, выберите полный временной диапазон",querying:"Выполняется запрос",reportNumber:"Номер отчета",operationAddress:"Адрес операции",operationParams:"Параметры операции",result:"Результат",progress:"Информация о прогрессе",loadingText:"Загрузка",selectCompleteTimeRange:"Пожалуйста, выберите полный временной диапазон",fileUploading:"Вызов файла записи волн",fileUploadComplete:"Вызов файла завершен"},customMenu:{addMenu:"Создать пользовательское меню",editMenu:"Редактировать пользовательское меню",deleteMenu:"Удалить пользовательское меню",addReport:"Создать пользовательский отчет",editReport:"Редактировать пользовательский отчет",deleteReport:"Удалить пользовательский отчет",addPointGroup:"Создать пользовательскую группу",editPointGroup:"Редактировать пользовательскую группу",deletePointGroup:"Удалить пользовательскую группу",selectedPoints:"Выбранные точки",selectFc:"Выбрать FC",selectGroupType:"Выбрать тип группы",groupTypes:{ST:"Телесигнализация",MX:"Телеизмерения",SP:"Установленные значения одной зоны",SG:"Установленные значения нескольких зон"},filterPlaceholder:"Фильтр по названию/описанию",loadingData:"Загрузка данных...",noDataForFc:"Нет данных для этого FC",noDataForGroupType:"Нет данных для этого типа группы",pleaseSelectFc:"Пожалуйста, сначала выберите FC",pleaseSelectGroupType:"Пожалуйста, сначала выберите тип группы",loadingGroupTypeData:"Получение данных типа группы...",loadingGroupTypes:"Загрузка данных типов групп...",loadedGroupTypes:"Загружены типы групп",dataLoadComplete:"Загрузка данных завершена",dataLoadFailed:"Загрузка данных не удалась",switchingToGroupType:"Переключение на",loadingGroupTypeDataSingle:"Загрузка данных...",loadGroupTypeFailed:"Загрузка данных не удалась",loadGroupTypeError:"Произошла ошибка при загрузке данных",inputGroupName:"Пожалуйста, введите название группы",inputGroupDesc:"Пожалуйста, введите описание группы",selectGroupTypeFirst:"Пожалуйста, сначала выберите тип группы",nameValidationFailed:"Проверка названия не удалась, пожалуйста, попробуйте снова",nameConflictWithSystem:"Название конфликтует с системным меню, пожалуйста, используйте другое название",nameConflictWithCustom:"Название конфликтует с существующим пользовательским меню, пожалуйста, используйте другое название",refreshData:"Обновить данные",menuName:"Название группы",menuDesc:"Описание",reportName:"Название отчета",reportDesc:"Описание",reportKeyword:"Ключевое слово",reportInherit:"Наследовать отчет",inputMenuName:"Пожалуйста, введите название группы",inputMenuDesc:"Пожалуйста, введите описание",inputReportName:"Пожалуйста, введите название отчета",inputReportDesc:"Пожалуйста, введите описание",inputReportKeyword:"Пожалуйста, введите ключевое слово",selectReportInherit:"Пожалуйста, выберите наследуемый отчет",cancel:"Отмена",confirm:"Подтвердить",successAddMenu:"Создание пользовательского меню успешно",successEditMenu:"Редактирование пользовательского меню успешно",successDeleteMenu:"Удаление пользовательского меню успешно",successAddReport:"Создание пользовательского отчета успешно",successEditReport:"Редактирование пользовательского отчета успешно",successDeleteReport:"Удаление пользовательского отчета успешно",successDeletePointGroup:"Удаление пользовательской группы успешно",errorAction:"Операция не удалась",errorDelete:"Удаление не удалось",confirmDeleteMenu:"Вы уверены, что хотите удалить это пользовательское меню?",confirmDeleteReport:"Вы уверены, что хотите удалить этот пользовательский отчет?",confirmDeletePointGroup:"Вы уверены, что хотите удалить эту пользовательскую группу?",tip:"Подсказка"},tree:{inputGroupName:"Пожалуйста, введите название группы",expandAll:"Развернуть все",collapseAll:"Свернуть все"}},Ba={device:{configure:{selectConfigure:"Пожалуйста, выберите файл символов конфигурации!",loading:"Загрузка",operating:"В процессе операции",loadFailed:"Загрузка не удалась, причина:",getCustomDeviceFailed:"Не удалось получить пользовательское устройство",registerDataFailed:"Регистрация данных не удалась, причина:",variablesNotExist:"Некоторые переменные не существуют:",getDataError:"Ошибка получения данных",remoteSet:"Дистанционная настройка",remoteControlFailed:"Дистанционное управление не удалось, причина:",remoteControlSuccess:"Дистанционное управление успешно",noRemoteControlType:"Тип дистанционного управления не настроен",symbolChangeReload:"Изменение символа, перезагрузка",deviceChangeReload:"Изменение устройства, перезагрузка",deviceConnectReload:"Устройство подключено успешно, перезагрузка"},configureList:{searchPlaceholder:"Поиск по ключевым словам",deviceMonitor:"Мониторинг устройства",newProject:"Новый проект",addCustomComponent:"Добавить пользовательский компонент",newConfigure:"Новая конфигурация",renameProject:"Переименовать проект",deleteProject:"Удалить проект",editConfigure:"Редактировать конфигурацию",renameConfigure:"Переименовать конфигурацию",deleteConfigure:"Удалить конфигурацию",openFolder:"Открыть папку расположения",inputProjectName:"Пожалуйста, введите название проекта (не более 10 символов, без специальных символов)",inputConfigureName:"Пожалуйста, введите название конфигурации (не более 10 символов, без специальных символов)",confirm:"Подтвердить",cancel:"Отмена",invalidName:"Недопустимая длина или содержимое названия",projectAddSuccess:"Проект: {name} добавлен успешно",projectAddFailed:"Проект: {name} не удалось добавить, причина:",configureAddSuccess:"Конфигурация: {name} добавлена успешно",configureAddFailed:"Конфигурация: {name} не удалось добавить, причина:",renameSuccess:"Переименование успешно",renameFailed:"Переименование не удалось, причина:",confirmDelete:"Вы уверены, что хотите удалить?",confirmBatchDelete:"В этом проекте есть связанное содержимое конфигурации, вы уверены, что хотите удалить все?",deleteSuccess:"Удаление успешно",deleteFailed:"Удаление не удалось, причина:",remoteSet:"Подтверждение удаления"},configures:{customComponent:"Пользовательский компонент",selectDevice:"Пожалуйста, выберите связанное устройство!",edit:"Редактировать:"},customConfigure:{getCustomDeviceFailed:"Не удалось получить пользовательское устройство",saveDeviceFailed:"Не удалось сохранить символ устройства",saveSuccess:"Сохранение успешно",deleteDeviceFailed:"Не удалось удалить символ устройства",deleteSuccess:"Удаление успешно",tip:"Информация подсказки"},deviceList:{unnamed:"Безымянное устройство",connect:"Подключить",disconnect:"Отключить",edit:"Редактировать",delete:"Удалить",notFound:"Устройство не найдено",editWarn:"Пожалуйста, сначала отключите соединение перед редактированием",deleteWarn:"Пожалуйста, сначала отключите соединение перед удалением",connectSuccess:"Устройство {name}: подключение успешно",connectExist:"Устройство {name}: соединение уже существует",connectFailed:"Устройство {name}: подключение не удалось",connectFailedReason:"Причина неудачи подключения устройства: {reason}",disconnectSuccess:"Устройство {name}: отключено",disconnectedNotify:"Устройство {name} соединение разорвано",currentDisconnectedNotify:"Текущее устройство соединение разорвано",operateFailed:"Устройство {name}: операция не удалась",remove:"Удалить"},deviceSearch:{searchPlaceholder:"Поиск устройства"},editConfigure:{saveSuccess:"Сохранение успешно",saveFailed:"Сохранение не удалось, причина:",loadFailed:"Загрузка не удалась, причина:",getCustomDeviceFailed:"Не удалось получить пользовательское устройство",tip:"Информация подсказки"},remoteSet:{inputValue:"Введите значение",write:"Записать",cancel:"Отмена",setFailed:"Дистанционная настройка не удалась, причина:",operateSuccess:"Операция успешна",noSetType:"Тип дистанционной настройки не настроен"}},graph:{component:{electricSymbols:"Электрические символы",customComponents:"Пользовательские компоненты",basicComponents:"Базовые компоненты"},toolbar:{undo:"Отменить",redo:"Повторить",bringToFront:"На передний план",sendToBack:"На задний план",ratio:"Пропорциональное масштабирование",delete:"Удалить",save:"Сохранить"},contextMenu:{group:"Группировать",ungroup:"Разгруппировать",linkData:"Связать данные",equipmentSaddr:"Адрес устройства"},dialog:{dataConfig:"Конфигурация данных",tip:"Информация подсказки",selectOneGraph:"Пожалуйста, выберите один график"},message:{waitForCanvasInit:"Пожалуйста, дождитесь завершения инициализации холста",loadEquipmentFailed:"Не удалось загрузить символ",loadEquipmentError:"Не удалось загрузить устройство",equipmentLoaded:"Загрузка устройства завершена"},basic:{title:"Базовые компоненты",components:{line:"Линия",text:"Текст",rectangle:"Прямоугольник",circle:"Круг",ellipse:"Эллипс",triangle:"Треугольник",arc:"Дуга"}},selectEquipment:{sequence:"Номер",name:"Название",type:"Тип",symbol:"Символ",operation:"Операция",reference:"Ссылка"},setSAddr:{telemetry:"Телесигнализация/Телеизмерения",format:"Форматирование",factor:"Коэффициент",remoteControl:"Дистанционное управление",controlType:"Способ дистанционного управления",controlValue:"Значение дистанционного управления",remoteSet:"Дистанционная настройка",setType:"Способ дистанционной настройки",displayConfig:"Конфигурация отображения",addRow:"Добавить строку",sequence:"Номер",type:"Тип",originalValue:"Исходное значение",displayValue:"Отображаемое значение",operation:"Операция",text:"Текст",symbol:"Символ",selectSymbol:"Выбрать символ",confirm:"Подтвердить",cancel:"Отмена",confirmDelete:"Вы уверены, что хотите удалить?",tip:"Информация подсказки",selectControl:"Селективное управление",directControl:"Прямое управление",controlClose:"Управление замыканием",controlOpen:"Управление размыканием",cancelDelete:"Отменить удаление"},equipmentType:{CBR:"Выключатель",DIS:"Разъединитель",GDIS:"Заземляющий разъединитель",PTR2:"2-обмоточный трансформатор",PTR3:"3-обмоточный трансформатор",VTR:"Трансформатор напряжения",CTR:"Трансформатор тока",EFN:"Устройство заземления нейтрали",IFL:"Отходящая линия",EnergyConsumer:"Нагрузка",GND:"Заземление",Arrester:"Разрядник",Capacitor_P:"Параллельный конденсатор",Capacitor_S:"Последовательный конденсатор",Reactor_P:"Параллельный реактор",Reactor_S:"Последовательный реактор",Ascoil:"Дугогасящая катушка",Fuse:"Предохранитель",BAT:"Батарея",BSH:"Втулка",CAB:"Кабель",LIN:"Воздушная линия",GEN:"Генератор",GIL:"Газоизолированная линия",RRC:"Вращающийся реактивный элемент",TCF:"Тиристорный управляемый преобразователь частоты",TCR:"Тиристорный управляемый реактивный элемент",LTC:"Переключатель ответвлений",IND:"Индуктор"},equipmentName:{breaker_vertical:"Выключатель-вертикальный",breaker_horizontal:"Выключатель-горизонтальный",breaker_invalid_vertical:"Выключатель-недействительный-вертикальный",breaker_invalid_horizontal:"Выключатель-недействительный-горизонтальный",disconnector_vertical:"Разъединитель-вертикальный",disconnector_horizontal:"Разъединитель-горизонтальный",disconnector_invalid_vertical:"Разъединитель-недействительный-вертикальный",disconnector_invalid_horizontal:"Разъединитель-недействительный-горизонтальный",hv_fuse:"Высоковольтный предохранитель",station_transformer_2w:"Станционный трансформатор (двухобмоточный)",transformer_y_d_11:"Трансформатор (Y/△-11)",transformer_d_y_11:"Трансформатор (△/Y-11)",transformer_d_d:"Трансформатор (△/△)",transformer_y_y_11:"Трансформатор (Y/Y-11)",transformer_y_y_12_d_11:"Трансформатор (Y/Y-12/△-11)",transformer_y_d_11_d_11:"Трансформатор (Y/△-11/△-11)",transformer_y_y_v:"Трансформатор (Y/Y/V)",transformer_autotransformer:"Трансформатор (автотрансформатор)",voltage_transformer_2w:"Трансформатор напряжения (двухобмоточный)",voltage_transformer_3w:"Трансформатор напряжения (трехобмоточный)",voltage_transformer_4w:"Трансформатор напряжения (четырехобмоточный)",arrester:"Разрядник",capacitor_horizontal:"Конденсатор-горизонтальный",capacitor_vertical:"Конденсатор-вертикальный",reactor:"Реактор",split_reactor:"Расщепленный реактор",power_inductor:"Силовой индуктор",feeder:"Отходящая линия",ground:"Заземление",tap_changer:"Переключатель ответвлений",connection_point:"Точка подключения",transformer_y_y_12_d_11_new:"Трансформатор(Y/Y-12/△-11)(новый)",pt:"ТН",arrester_new:"Разрядник(новый)",disconnector_vertical_new:"Разъединитель-вертикальный(новый)",disconnector_horizontal_new:"Разъединитель-горизонтальный(новый)",arrester_new_vertical:"Разрядник(новый)-вертикальный",disconnector_vertical_left_new:"Разъединитель-вертикальный-левый(новый)"}},graphProperties:{blank:{propertySetting:"Настройка свойств"},graph:{canvasSetting:"Настройка холста",grid:"Сетка",backgroundColor:"Цвет фона"},group:{groupProperty:"Свойства группы",basic:"Основные",width:"Ширина",height:"Высота",x:"Позиция(X)",y:"Позиция(Y)",angle:"Угол поворота"},node:{nodeProperty:"Свойства узла",style:"Стиль",backgroundColor:"Цвет фона",borderWidth:"Ширина границы",borderColor:"Цвет границы",borderDasharray:"Стиль границы",rx:"Граница rx",ry:"Граница ry",position:"Позиция",width:"Ширина",height:"Высота",x:"Позиция(X)",y:"Позиция(Y)",property:"Свойство",angle:"Угол поворота",zIndex:"Уровень(z)",fontFamily:"Шрифт",fontColor:"Цвет шрифта",fontSize:"Размер шрифта",text:"Текст"},pathLine:{lineSetting:"Настройка линии",style:"Стиль",lineHeight:"Ширина",lineColor:"Цвет",borderDasharray:"Граница",position:"Позиция",width:"Ширина",height:"Высота",x:"Позиция(X)",y:"Позиция(Y)",property:"Свойство",angle:"Угол поворота",zIndex:"Уровень(z)"}},business:{hmi:{title:"Управление экранами",form:{add:"Добавить экран",edit:"Редактировать экран",view:"Просмотр экрана",name:"Название экрана",type:"Тип экрана",template:"Шаблон экрана",description:"Описание",cancel:"Отмена",confirm:"Подтвердить",validation:{name:"Пожалуйста, введите название экрана",type:"Пожалуйста, выберите тип экрана",template:"Пожалуйста, выберите шаблон экрана"}},columns:{name:"Название экрана",type:"Тип экрана",template:"Шаблон экрана",createTime:"Время создания",updateTime:"Время обновления",status:"Статус",operation:"Операция"},type:{device:"Экран устройства",process:"Технологический экран",alarm:"Экран аварийных сигналов",custom:"Пользовательский экран"},status:{draft:"Черновик",published:"Опубликовано",archived:"Архивировано"},editor:{title:"Редактор экрана",save:"Сохранить",preview:"Предварительный просмотр",publish:"Опубликовать",cancel:"Отмена",tools:{select:"Выбрать",rectangle:"Прямоугольник",circle:"Круг",line:"Линия",text:"Текст",image:"Изображение",device:"Устройство",alarm:"Аварийный сигнал",chart:"График"},properties:{title:"Свойства",position:"Позиция",size:"Размер",style:"Стиль",data:"Данные",event:"Событие"}},preview:{title:"Предварительный просмотр экрана",fullscreen:"Полный экран",exit:"Выход",zoom:{in:"Увеличить",out:"Уменьшить",fit:"По размеру"}},publish:{title:"Опубликовать экран",version:"Номер версии",description:"Описание публикации",cancel:"Отмена",confirm:"Подтвердить",validation:{version:"Пожалуйста, введите номер версии",description:"Пожалуйста, введите описание публикации"}},template:{title:"Шаблон экрана",add:"Добавить шаблон",edit:"Редактировать шаблон",delete:"Удалить шаблон",name:"Название шаблона",category:"Категория шаблона",description:"Описание",preview:"Предварительный просмотр",cancel:"Отмена",confirm:"Подтвердить",validation:{name:"Пожалуйста, введите название шаблона",category:"Пожалуйста, выберите категорию шаблона"}}}}},Oa={common:{date:"Дата",search:"Поиск",save:"Сохранить",clear:"Очистить",loading:"Загрузка...",reportNo:"Номер отчета",time:"Время",description:"Описание",progress:"Прогресс",selectDateRange:"Пожалуйста, выберите диапазон дат",noData:"Нет данных",saveSuccess:"Сохранение успешно",saveFailed:"Сохранение не удалось"},date:"Дата",search:"Поиск",filter:"Фильтр",save:"Сохранить",clearList:"Очистить список",loading:"Загрузка...",reportNumber:"Номер отчета",time:"Время",description:"Описание",progress:"Прогресс",loadingText:"Загрузка...",querying:"Выполняется запрос",selectCompleteTimeRange:"Пожалуйста, выберите полный временной диапазон",noDataToSave:"Нет данных для сохранения",saveSuccess:"Сохранение успешно",saveReport:"Сохранить отчет",fileUploading:"Загрузка файла",fileUploadComplete:"Загрузка файла завершена",autoRefresh:"Автообновление",showHiddenItems:"Показать скрытые элементы",hideHiddenItems:"Скрыть скрытые элементы",name:"Название",operationAddress:"Адрес операции",operationParams:"Параметры операции",value:"Значение",step:"Шаг",source:"Источник",sourceType:"Тип источника",result:"Результат",searchType:"Тип поиска",total:"Всего {num} записей",sameSearch:"Поиск одинакового содержимого",sameFilter:"Фильтр одинакового содержимого",showHideTime:"Показать/скрыть столбец времени",selectRowToOperate:"Пожалуйста, сначала выберите строку для операции",trip:{autoRefresh:"Автообновление"},operate:{name:"Название",operateAddress:"Адрес операции",operateParam:"Параметр операции",value:"Значение",step:"Шаг",source:"Источник",sourceType:"Тип источника",result:"Результат"},group:{uploadWave:"Загрузить волну",searchHistory:"История поиска",saveResult:"Сохранить результат",clearContent:"Очистить содержимое",contextMenu:{uploadWave:"Загрузить волну",getHistoryReport:"Получить исторический отчет",saveResult:"Сохранить результат",clearContent:"Очистить содержимое"},date:"Дата",search:"Поиск",save:"Сохранить",clearList:"Очистить список",loading:"Загрузка...",table:{reportId:"ID отчета",time:"Время",description:"Описание"},progress:{title:"Прогресс",searching:"Выполняется запрос {type}",loading:"Загрузка..."},refresh:{start:"Начать обновление",stop:"Остановить обновление"},hiddenItems:{show:"Показать скрытые элементы"},messages:{noFileToUpload:"Нет файлов для загрузки",selectDateRange:"Пожалуйста, выберите диапазон дат",noDataToSave:"Нет данных для сохранения",saveReport:"Сохранить отчет",saveSuccess:"Сохранение успешно"}},exporting:"Экспорт...",stopRefresh:"Остановить обновление",searchProgress:"Выполняется запрос {type}",exportLogSuccess:"Экспорт успешен: {path}",exportLogFailed:"Экспорт не удался: {msg}",exportLogCancelled:"Пользователь отменил операцию экспорта",entryID:"Номер",module:"Название компонента",msg:"Содержимое",level:"Уровень",type:"Тип",origin:"Источник",user:"Имя пользователя",pleaseSelectSavePath:"Пожалуйста, выберите путь сохранения...",items:"записей"},_a={device:qa,hmi:Ba,report:Oa},Ga={common:{add:"Добавить",index:"Номер",delete:"Удалить",clear:"Очистить",import:"Импорт",export:"Экспорт",execute:"Выполнить",moveUp:"Переместить вверх",moveDown:"Переместить вниз",loading:"Загрузка...",success:"Успешно",failed:"Не удалось",confirm:"Подтвердить",cancel:"Отмена",yes:"Да",no:"Нет",operation:"Операция",tips:"Подсказки",title:"Подсказка"},search:{placeholder:"Поиск функций"},functionList:{unnamedDevice:"Безымянное устройство",batchDownload:{name:"Пакетное скачивание",desc:"Пакетное скачивание файлов и импорт параметров"},xmlFormatter:{name:"Форматирование XML",desc:"Быстрая иерархическая организация XML-данных"},jsonFormatter:{name:"Форматирование JSON",desc:"Интеллектуальное форматирование JSON-данных с поддержкой проверки синтаксиса"},radixConverter:{name:"Преобразование систем счисления",desc:"Поддержка взаимного преобразования двоичных, десятичных, шестнадцатеричных и других систем счисления"},temperatureConverter:{name:"Преобразование температуры",desc:"Интеллектуальное преобразование различных единиц температуры: Цельсий, Фаренгейт, Кельвин и др."},encryption:{name:"Шифрование/дешифрование текста",desc:"Быстрое шифрование и дешифрование текста на основе алгоритмов AES, RSA, Base64 и др."},packageProgram:{name:"Упаковка программы",desc:"Упаковка и экспорт файлов программы устройства с поддержкой пользовательского каталога сохранения и быстрого поиска папки"}},matrixContent:{loading:"Загрузка...",tabs:{deviceList:"Список устройств",downloadConfig:"Config скачивания",paramConfig:"Config параметров"}},taskSteps:{connect:"Подключение",download:"Скачивание",import:"Импорт",disconnect:"Отключение",complete:"Завершено"},messages:{connectDevice:"Подключение устройства",executeFileDownload:"Выполнение скачивания файла",downloadingFile:"Скачивание файла",downloadFileFailed:"Скачивание файла не удалось",fileDownloadCompleted:"Скачивание файла завершено",executeParamImport:"Выполнение импорта параметров",paramValidationFailed:"Проверка параметров не удалась",paramImportFailed:"Импорт параметров не удался",paramImportCompleted:"Импорт параметров завершен",taskCompleted:"Задача завершена",deviceConnectionFailed:"Подключение устройства не удалось",deviceRebootSuccess:"Перезагрузка устройства успешна"},deviceList:{title:"Список устройств",deviceListExcel:"Список устройств.xlsx",exportDeviceList:"Экспорт списка устройств",importDeviceList:"Импорт списка устройств",exportSuccess:"Экспорт списка устройств успешен",exportFail:"Экспорт списка устройств не удался",importSuccess:"Импорт списка устройств успешен",importFail:"Импорт списка устройств не удался",exportSuccessMsg:"Экспорт списка устройств успешен",exportFailMsg:"Экспорт списка устройств не удался",importSuccessMsg:"Импорт списка устройств успешен",importFailMsg:"Импорт списка устройств не удался: {msg}",deviceName:"Название устройства",deviceAddress:"Адрес устройства",devicePort:"Порт устройства",isEncrypted:"Зашифровано ли",encrypted:"Зашифровано",notEncrypted:"Не зашифровано",status:"Статус",operation:"Операция",reboot:"Перезагрузить",noReboot:"Не перезагружать",addDevice:"Добавить устройство",deleteDevice:"Удалить устройство",clearDevices:"Очистить устройства",deviceExists:"Это устройство уже существует",deviceDeleted:"Устройство {ip} удалено",downloadFile:"Скачать файл?",importParam:"Импортировать параметры?",connectTimeout:"Время ожидания подключения",paramTimeout:"Время ожидания изменения параметров",readTimeout:"Глобальное время ожидания запроса",progress:"Прогресс"},downList:{title:"Config скачивания",deviceDirectory:"Каталог устройства",fileName:"Название файла",fileSize:"Размер файла",filePath:"Путь к файлу",lastModified:"Время последнего изменения",addFile:"Добавить файл для скачивания",addFolder:"Добавить папку для скачивания",fileExists:"Файл {path} уже существует, добавление не удалось!",fileDeleted:"Файл {path} удален",filesDeleted:"Файлы удалены",defaultExportFileName:"Список файлов для скачивания.xlsx",exportTitle:"Экспорт списка файлов для скачивания",importTitle:"Импорт списка файлов для скачивания",exportSuccess:"Экспорт списка файлов успешен",exportFailed:"Экспорт списка файлов не удался",importSuccess:"Импорт списка файлов успешен",importFailed:"Импорт списка файлов не удался",fileExistsMsg:"Файл {path} уже существует, добавление не удалось!"},paramList:{title:"Config параметров",paramGroup:"Группа параметров",groupName:"Название группы",paramName:"Название параметра",paramDesc:"Описание параметра",paramValue:"Значение параметра",minValue:"Мин. значение",maxValue:"Макс. значение",step:"Шаг",unit:"Единица измерения",searchParamName:"Название параметра",searchParamDesc:"Описание параметра",importSuccess:"Импорт параметров успешен",importFailed:"Импорт параметров не удался",exportSuccess:"Экспорт параметров успешен",exportFailed:"Экспорт параметров не удался",clearSuccess:"Очистка параметров успешна"},progressDialog:{title:"Обработка",pleaseWait:"Пожалуйста, подождите..."},packageProgram:{saveDir:"Каталог сохранения",selectSaveDir:"Выбрать каталог сохранения",packageBtn:"Упаковать",locateDir:"Найти папку",delete:"Удалить",sequence:"Номер",fileName:"Название файла",fileSize:"Размер файла",filePath:"Путь к файлу",lastModified:"Время последнего изменения",operation:"Операция",saveDirEmpty:"Пожалуйста, сначала выберите каталог сохранения!",packageSuccess:"Упаковка программы завершена!",tip:"Подсказка",confirmButton:"Подтвердить",defaultExportFileName:"Список файлов упаковки программы.xlsx",exportTitle:"Экспорт списка файлов упаковки программы",importTitle:"Импорт списка файлов упаковки программы",exportSuccess:"Экспорт списка файлов успешен",exportFailed:"Экспорт списка файлов не удался",importSuccess:"Импорт списка файлов успешен",importFailed:"Импорт списка файлов не удался",fileExists:"Файл {path} уже существует, добавление не удалось!",selectDirSuccess:"Выбор каталога успешен: {dir}",locateDirSuccess:"Поиск каталога успешен: {dir}",addFileStart:"Открытие селектора файлов...",addFileSuccess:"Успешно добавлено {count} файлов/папок",addFileNone:"Не добавлено новых файлов/папок",deleteSuccess:"Успешно удалено {count} файлов/папок",clearSuccess:"Очищены все файлы/папки",moveUpSuccess:"Перемещение вверх: {name}",moveDownSuccess:"Перемещение вниз: {name}",noFileSelected:"Пожалуйста, сначала выберите файлы для упаковки!",noDeviceSelected:"Пожалуйста, сначала выберите устройство для упаковки!",packageFailed:"Упаковка не удалась: {msg}",zipPath:"Путь упаковки: {zipPath}",openFileButton:"Открыть файл"}},Ua={search:{placeholder:"Поиск устройства",button:"Поиск",success:"Поиск успешен"},device2:{search:{placeholder:"Поиск устройства",add:"Добавить устройство",duplicate:"Этот IP и порт уже существуют, пожалуйста, не добавляйте повторно"},list:{empty:"Устройство не найдено",unnamed:"Безымянное устройство",status:{connected:"Подключено",disconnected:"Отключено"},contextMenu:{connect:"Подключить",edit:"Редактировать",disconnect:"Отключить",remove:"Удалить"},message:{disconnectFirst:"Пожалуйста, сначала отключите соединение перед редактированием",disconnectFirstDelete:"Пожалуйста, сначала отключите соединение перед удалением",connectSuccess:"Устройство {name}: подключение успешно",connectExists:"Устройство {name}: соединение уже существует",connectFailed:"Устройство {name}: подключение не удалось",connectFailedReason:"Причина неудачи подключения устройства: {reason}",disconnected:"Устройство {name}: отключено",operationFailed:"Устройство {name}: операция не удалась"}},report:{group:{openWaveConfirm:"Открыть файл волн через сторонний инструмент?",tips:"Дружеское напоминание",noWaveTool:"Путь к стороннему инструменту анализа волн не настроен"},common:{selectRow:"Пожалуйста, выберите строку для операции"}},backup:{savePath:"Путь сохранения",setPath:"Пожалуйста, установите путь резервного копирования",setPathTitle:"Установить путь резервного копирования",locateFolder:"Найти папку",startBackup:"Начать резервное копирование",cancelBackup:"Отменить резервное копирование",backup:"Резервное копирование",sequence:"Номер",backupType:"Тип резервного копирования",backupDesc:"Описание резервного копирования",progress:"Прогресс",status:"Статус",noTypeSelected:"Пожалуйста, выберите тип резервного копирования",backupSuccess:"Резервное копирование успешно",backupFailed:"Резервное копирование не удалось",openFolderFailed:"Не удалось открыть папку",backupTypes:{paramValue:"Значения параметров",faultInfo:"Информация о неисправностях",cidConfigPrjLog:"Журнал проекта конфигурации CID",waveReport:"Отчет о волнах"},backupDescTypes:{paramValue:"Резервное копирование всех установленных значений параметров устройства",faultInfo:"Резервное копирование информации о записи неисправностей устройства",cidConfigPrjLog:"Резервное копирование файлов конфигурации CID и журналов проекта",waveReport:"Резервное копирование файлов отчетов анализа волн"},backupStatus:{userCancelled:"Отменено пользователем",transferring:"Передача"},console:{pathNotSet:"Путь резервного копирования не установлен, невозможно начать резервное копирование",noTypeSelected:"Тип резервного копирования не выбран, невозможно начать резервное копирование",startBackup:"Начало резервного копирования, тип: {types}, путь: {path}",backupException:"Исключение резервного копирования: {error}",pathSelected:"Выбран путь резервного копирования: {path}",pathNotSelected:"Путь резервного копирования не выбран",pathNotSetForLocate:"Путь резервного копирования не установлен, невозможно найти папку",folderOpened:"Открыта папка резервного копирования: {path}",openFolderFailed:"Не удалось открыть папку резервного копирования: {error}",taskCompleted:"Обработка задачи завершена",taskCancelled:"Задача отменена",typeError:"Ошибка типа [{type}]: {error}",typeCompleted:"Резервное копирование типа [{type}] завершено",typeCancelled:"Тип [{type}] отменен",typeFailed:"Тип [{type}] не удался"}},remoteControl:{directControl:"Прямое управление",selectControl:"Селективное управление"},messageMonitor:{title:"Мониторинг сообщений",start:"Начать мониторинг",stop:"Остановить мониторинг",clear:"Очистить",export:"Экспорт",expand:"Развернуть",collapse:"Свернуть",close:"Закрыть",messageType:"Сообщение",noMessages:"Нет данных сообщений",noMessagesToExport:"Нет данных сообщений для экспорта",startSuccess:"Начат мониторинг сообщений",stopSuccess:"Остановлен мониторинг сообщений",clearSuccess:"Очистка сообщений успешна",exportSuccess:"Экспорт сообщений успешен",exportFailed:"Экспорт сообщений не удался",toggleFailed:"Переключение состояния мониторинга не удалось"}}},Wa={search:{placeholder:"Поиск по ключевым словам"},categories:{title:"📦IT-инструменты",formatting:"📝Инструменты форматирования",xml:"🟡Форматирование XML",json:"🟡Форматирование JSON",conversion:"🔄Инструменты конвертации",radix:"🟢Конвертация систем счисления",temperature:"🟢Конвертация температуры",encryption:"🔑Инструменты шифрования/дешифрования",textEncryption:"🔵Шифрование/дешифрование текста"},encryption:{title:"Шифрование/дешифрование текста",description:"Шифрование и дешифрование открытого текста с использованием алгоритмов шифрования (таких как AES, TripleDES, Rabbit или RC4)",encrypt:"Шифровать",inputText:"Текст для шифрования:",inputPlaceholder:"Пожалуйста, введите текст для шифрования...",key:"Ключ:",keyPlaceholder:"Пожалуйста, введите ключ шифрования",algorithm:"Алгоритм шифрования:",outputText:"Зашифрованный текст:",outputPlaceholder:"Результат шифрования будет отображен здесь...",decrypt:"Расшифровать",decryptInputText:"Текст для расшифровки:",decryptInputPlaceholder:"Пожалуйста, введите зашифрованный текст...",decryptKey:"Ключ:",decryptAlgorithm:"Алгоритм расшифровки:",decryptOutputText:"Расшифрованный текст:",decryptError:"Не удается расшифровать текст"},json:{title:"Форматирование JSON",description:"Форматирование строки JSON в удобочитаемый формат",sortKeys:"Сортировка полей",indentSize:"Размер отступа",inputLabel:"JSON для форматирования",inputPlaceholder:"Пожалуйста, вставьте ваш JSON...",outputLabel:"Отформатированный JSON",invalid:"Документ не соответствует спецификации JSON, пожалуйста, проверьте"},xml:{title:"Форматирование XML",description:"Форматирование строки XML в удобочитаемый формат",collapseContent:"Свернуть содержимое:",indentSize:"Размер отступа:",inputLabel:"XML для форматирования",inputPlaceholder:"Пожалуйста, вставьте ваш XML...",outputLabel:"Отформатированный XML",invalid:"Документ не соответствует спецификации XML, пожалуйста, проверьте"},temperature:{title:"Конвертация температуры",description:"Конвертация температуры между Кельвином, Цельсием, Фаренгейтом, Ранкином, Делилем, Ньютоном, Реомюром и Рёмером",kelvin:"Кельвин",kelvinUnit:"K",celsius:"Цельсий",celsiusUnit:"°C",fahrenheit:"Фаренгейт",fahrenheitUnit:"°F",rankine:"Ранкин",rankineUnit:"°R",delisle:"Делиль",delisleUnit:"°De",newton:"Ньютон",newtonUnit:"°N",reaumur:"Реомюр",reaumurUnit:"°Ré",romer:"Рёмер",romerUnit:"°Rø"},radix:{title:"Конвертация систем счисления",description:"Конвертация чисел между различными системами счисления (десятичная, шестнадцатеричная, двоичная, восьмеричная, base64...)",inputLabel:"Число для конвертации",inputPlaceholder:"Пожалуйста, введите число (например: 100)",outputLabel:"Результат конвертации",binary:"2-ичная(2)",binaryPlaceholder:"Двоичный результат...",octal:"8-ичная(8)",octalPlaceholder:"Восьмеричный результат...",decimal:"10-ичная(10)",decimalPlaceholder:"Десятичный результат...",hex:"16-ичная(16)",hexPlaceholder:"Шестнадцатеричный результат...",base64:"Base64(64)",base64Placeholder:"Результат Base64...",customBase:"Пользовательская система счисления",customBasePlaceholder:"Результат Base {{base}}..."},jsonViewer:{title:"Форматирование JSON",description:"Форматирование строки JSON в удобочитаемый формат",sortKeys:"Сортировка полей",indentSize:"Размер отступа",inputJson:"JSON для форматирования",formattedJson:"Отформатированный JSON",placeholder:"Пожалуйста, вставьте ваш JSON...",validationError:"Документ не соответствует спецификации JSON. Пожалуйста, проверьте"}},ja={matrix:Ga,debug:Ua,tools:Wa},Ha={checkCard:{default:"По умолчанию"},chooseModule:{title:"Выбрать приложение",noModule:"Модуль не найден!",setDefault:"Установить по умолчанию",cancel:"Отмена",confirm:"Подтвердить"},closer:{title:"Подтверждение выхода",message:"Вы хотите выйти?",confirm:"Подтвердить",minimize:"Свернуть в трей",cancel:"Отмена"},codeHighLight:{noCode:"Нет"},cropUpload:{title:"Обрезка изображения",zoomIn:"Увеличить",zoomOut:"Уменьшить",rotateLeft:"Повернуть влево",rotateRight:"Повернуть вправо",uploadImage:"Нажмите для загрузки изображения",uploadTip:"Пожалуйста, загрузите файл изображения, рекомендуется не более 2M",cancel:"Отмена",confirm:"Подтвердить"},error:{forbidden:"Извините, у вас нет доступа к этой странице~🙅‍♂️🙅‍♀️",notFound:"Извините, страница, которую вы ищете, не существует~🤷‍♂️🤷‍♀️",serverError:"Извините, ваша сеть пропала~🤦‍♂️🤦‍♀️",back:"Вернуться на предыдущую страницу"},form:{input:{placeholder:"Пожалуйста, заполните {label}"},select:{placeholder:"Пожалуйста, выберите {label}"},button:{add:"Добавить",edit:"Редактировать",delete:"Удалить",view:"Просмотр"},search:{inputPlaceholder:"Пожалуйста, введите",selectPlaceholder:"Пожалуйста, выберите",rangeSeparator:"до",startPlaceholder:"Время начала",endPlaceholder:"Время окончания"}},selectIcon:{title:"Выбор иконки",placeholder:"Пожалуйста, выберите иконку",searchPlaceholder:"Поиск иконки",noSearchResult:"Иконка, которую вы ищете, не найдена~",moreIcons:"Больше иконок",enterIconifyCode:"Пожалуйста, введите код иконки iconify, например mdi:home-variant",iconifyAddress:"Адрес iconify",localIcons:"Локальные иконки"},selector:{add:"Добавить",addCurrent:"Добавить текущий",addSelected:"Добавить выбранные",delete:"Удалить",deleteCurrent:"Удалить текущий",deleteSelected:"Удалить выбранные",cancel:"Отмена",confirm:"Подтвердить",selected:"Выбрано",maxSelect:"Максимум выбрать",singleSelectOnly:"Можно выбрать только один",maxSelectLimit:"Максимум выбрать {count}",person:"человек"},upload:{view:"Просмотр",edit:"Редактировать",delete:"Удалить",uploadImage:"Пожалуйста, загрузите изображение",uploadSuccess:"Изображение загружено успешно!",uploadFailed:"Загрузка изображения не удалась, пожалуйста, загрузите снова!",invalidFormat:"Загруженное изображение не соответствует требуемому формату!",fileSizeExceeded:"Размер загружаемого изображения не может превышать {size}M!",maxFilesExceeded:"В настоящее время можно загрузить максимум {limit} изображений, пожалуйста, удалите и загрузите снова!",fileSizeZero:"Файл {fileName} имеет размер 0, загрузка невозможна!",tips:"Дружеское напоминание"},treeFilter:{searchPlaceholder:"Введите ключевые слова для фильтрации",expandAll:"Развернуть все",collapseAll:"Свернуть все",all:"Все"},proTable:{search:{reset:"Сброс",search:"Поиск",expand:"Развернуть",collapse:"Свернуть"},pagination:{total:"Всего {total} записей",pageSize:"записей/страница",goto:"Перейти к",page:"странице"},colSetting:{title:"Настройки столбцов",fixedLeft:"Показывать ли",fixedRight:"Сортировать ли",cancelFixed:"Отменить фиксацию",reset:"Восстановить по умолчанию",confirm:"Подтвердить",cancel:"Отмена"},table:{empty:"Нет данных"}},basicComponent:{title:"Базовые компоненты",line:"Линия",text:"Текст",rect:"Прямоугольник",circle:"Круг",ellipse:"Эллипс",triangle:"Треугольник",arc:"Дуга"}},$a={equipmentList:{sequence:"Номер",name:"Название",type:"Тип",operation:"Операция",preview:"Предпросмотр",copy:"Копировать",delete:"Удалить",confirmDelete:"Вы уверены, что хотите удалить?",tip:"Информация подсказки",error:"Ошибка"},graphComponent:{deviceType:"Тип устройства",deviceName:"Название устройства",save:"Сохранить"},contextMenu:{group:"Группировать",ungroup:"Разгруппировать",setStatus:"Установить статус",copy:"Копировать",delete:"Удалить",rename:"Переименовать"},graphCreate:{needTwoDevices:"Переключатель или разъединитель требует выбора двух графических устройств",needCorrectStatus:"Пожалуйста, установите правильные атрибуты состояния для переключателя или разъединителя",needOneDevice:"Пожалуйста, выберите одно графическое устройство"},graphDefine:{waitCanvasInit:"Пожалуйста, дождитесь завершения инициализации холста",selectOneGraph:"Пожалуйста, выберите один символ",tip:"Подсказка"},setStatus:{open:"Открыть",close:"Закрыть",none:"Нет"},graphTools:{undo:"Отменить",redo:"Повторить",front:"На передний план",back:"На задний план",delete:"Удалить",save:"Сохранить",equipmentList:"Список оборудования"},graphEditor:{dataConfig:"Конфигурация данных",loadEquipmentFailed:"Не удалось загрузить символ"}},Ya={more:{importPathNotExists:"Путь импорта не существует",exportPathNotExists:"Путь экспорта не существует",selectCorrectConfigFile:"Пожалуйста, выберите правильный файл конфигурации",exportProjectConfigException:"Исключение при экспорте конфигурации проекта",importProjectConfigException:"Исключение при импорте конфигурации проекта"}},Ka={...Aa,...Va,..._a,...ja,components:Ha,graphDefine:$a,services:Ya,Home:"Главная"},re={zh:rt,en:At,es:Zt,fr:Pa,ru:Ka},Ja=()=>{const t=localStorage.getItem("language");return t&&Object.keys(re).includes(t)?t:"zh"},Xa=()=>{const t=Ja();return localStorage.setItem("language",t),t},G=ae.createI18n({legacy:!1,locale:Xa(),fallbackLocale:"zh",messages:re,sync:!0,silentTranslationWarn:!0,silentFallbackWarn:!0});class Qa extends k{constructor(a){super();m(this,"data");this.options=a,this.data=a.data,this.data.invalidTriggerRef||(this.data.invalidTriggerRef=new Map)}get disabled(){return this.options.enabled!==!0}increaseInvalidTrigger(a){var o,i;const r=this.data.invalidTriggerRef.get(a);r?(o=this.data.invalidTriggerRef)==null||o.set(a,r+1):(i=this.data.invalidTriggerRef)==null||i.set(a,1)}validTrigger(a){var o,i;const r=(o=this.data.invalidTriggerRef)==null?void 0:o.get(a);return r&&r>0?((i=this.data.invalidTriggerRef)==null||i.set(a,r-1),!1):!0}enable(){this.disabled&&(this.options.enabled=!0)}disable(){this.disabled||(this.options.enabled=!1)}dispose(){console.log("dispose")}}class Za extends k{constructor(a){super();m(this,"name","common");m(this,"dataImpl");m(this,"options");this.options={enabled:!0,...a}}init(a){this.dataImpl=new Qa({...this.options,graph:a})}isEnabled(){return!this.dataImpl.disabled}enable(){this.dataImpl.enable()}disable(){this.dataImpl.disable()}getData(){return this.dataImpl.data}getPluginImpl(){return this.dataImpl}dispose(){this.dataImpl.dispose()}}class er extends k{constructor(a){super();m(this,"data");this.options=a,this.data=a.data}get graph(){return this.options.graph}get disabled(){return this.options.enabled!==!0}enable(){this.disabled&&(this.options.enabled=!0)}disable(){this.disabled||(this.options.enabled=!1)}dispose(){console.log("dispose")}}class tr extends k{constructor(a){super();m(this,"name","data");m(this,"dataImpl");m(this,"options");this.options={enabled:!0,...a}}init(a){this.dataImpl=new er({...this.options,graph:a})}isEnabled(){return!this.dataImpl.disabled}enable(){this.dataImpl.enable()}disable(){this.dataImpl.disable()}getData(){return this.dataImpl.data}dispose(){this.dataImpl.dispose()}}const ar=`
.line.x6-widget-transform .x6-widget-transform-resize[data-position='top-left'],
.line.x6-widget-transform .x6-widget-transform-resize[data-position='top'],
.line.x6-widget-transform .x6-widget-transform-resize[data-position='top-right'],
.line.x6-widget-transform .x6-widget-transform-resize[data-position='bottom-right'],
.line.x6-widget-transform .x6-widget-transform-resize[data-position='bottom'],
.line.x6-widget-transform .x6-widget-transform-resize[data-position='bottom-left'] {
  display: none;
}
  `,f=G.global.t,U={COMMON_PLUGIN_NAME:"common"};var E=(t=>(t.UNSELECT="unselect",t.SELECT="select",t))(E||{}),q=(t=>(t.TEXT="text",t.EQUIPMENT="equipment",t))(q||{}),C=(t=>(t.GROUP="group",t.UNGROUP="ungroup",t.CLONE="clone",t.DELETE="delete",t.EQUIPMENT_STATUS="equipmentStatus",t.EQUIPMENT_SADDR="equipmentSAddr",t))(C||{});class rr{constructor(e){m(this,"code");m(this,"msg");m(this,"data");this.code=e??1,this.msg=""}isSuccess(){return this.code===1}setSuccess(){this.code=1}}var p=(t=>(t.OPEN="open",t.CLOSE="close",t.NONE="",t))(p||{}),N=(t=>(t.CBR="CBR",t.DIS="DIS",t.GDIS="GDIS",t.PTR2="PTR2",t.PTR3="PTR3",t.VTR="VTR",t.CTR="CTR",t.EFN="EFN",t.IFL="IFL",t.EnergyConsumer="EnergyConsumer",t.GND="GND",t.Arrester="Arrester",t.Capacitor_P="Capacitor_P",t.Capacitor_S="Capacitor_S",t.Reactor_P="Reactor_P",t.Reactor_S="Reactor_S",t.Ascoil="Ascoil",t.Fuse="Fuse",t.BAT="BAT",t.BSH="BSH",t.CAB="CAB",t.LIN="LIN",t.GEN="GEN",t.GIL="GIL",t.RRC="RRC",t.TCF="TCF",t.TCR="TCR",t.LTC="LTC",t.IND="IND",t))(N||{});f("hmi.graph.equipmentType.CBR"),f("hmi.graph.equipmentType.DIS"),f("hmi.graph.equipmentType.GDIS"),f("hmi.graph.equipmentType.PTR2"),f("hmi.graph.equipmentType.PTR3"),f("hmi.graph.equipmentType.VTR"),f("hmi.graph.equipmentType.CTR"),f("hmi.graph.equipmentType.EFN"),f("hmi.graph.equipmentType.IFL"),f("hmi.graph.equipmentType.EnergyConsumer"),f("hmi.graph.equipmentType.GND"),f("hmi.graph.equipmentType.Arrester"),f("hmi.graph.equipmentType.Capacitor_P"),f("hmi.graph.equipmentType.Capacitor_S"),f("hmi.graph.equipmentType.Reactor_P"),f("hmi.graph.equipmentType.Reactor_S"),f("hmi.graph.equipmentType.Ascoil"),f("hmi.graph.equipmentType.Fuse"),f("hmi.graph.equipmentType.BAT"),f("hmi.graph.equipmentType.BSH"),f("hmi.graph.equipmentType.CAB"),f("hmi.graph.equipmentType.LIN"),f("hmi.graph.equipmentType.GEN"),f("hmi.graph.equipmentType.GIL"),f("hmi.graph.equipmentType.RRC"),f("hmi.graph.equipmentType.TCF"),f("hmi.graph.equipmentType.TCR"),f("hmi.graph.equipmentType.LTC"),f("hmi.graph.equipmentType.IND");const Fo="EventTypeParamsName",To="EquipmentManager";var or=(t=>(t.BACKGROUND_COLOR="background-color",t.BORDER_COLOR="border-color",t.BORDER_WIDTH="border-width",t.BORDER_DASHARRAY="border-dasharray",t.WIDTH="width",t.HEIGHT="height",t.X="x",t.Y="y",t.RX="rx",t.RY="ry",t.ANGLE="angle",t.ZINDEX="z-index",t.FONT_FAMILY="font-family",t.FONT_SIZE="font-size",t.FONT_COLOR="font-color",t.TEXT="text",t.POINTS="points",t))(or||{}),B=(t=>(t.NONE="none",t.BLANK_CLICK="blank:click",t.NODE_CLICK="node:click",t))(B||{}),ir=(t=>(t.SELECT="select",t.DIRECT="direct",t.NONE="none",t))(ir||{}),nr=(t=>(t[t.SWITCH_CLOSE=2]="SWITCH_CLOSE",t[t.SWITCH_OPEN=1]="SWITCH_OPEN",t[t.NONE=0]="NONE",t))(nr||{});class sr{constructor(e){m(this,"graph");m(this,"groupAngle",0);this.graph=e}canGroup(){const e=this.graph.getSelectedCells();return!e||e.length<2?!1:e.find(r=>r.getParentId()==null?r.getChildCount()==0:!1)!=null}group(){if(!this.canGroup())return;const e=Q(this.graph),a=document.getElementsByClassName("x6-widget-selection-inner");if(!a||a.length==0)return;const r=a[0],o=this.graph.addNode({shape:"rect",x:w(r,"left"),y:w(r,"top"),width:w(r,"width"),height:w(r,"height"),attrs:{body:{class:"graph-group",fill:"transparent",stroke:"none"}},data:{type:"group"}}),i=[];for(const n of e)o.addChild(n),i.push(n.id);ie(o,i),this.setChildInfo(o),I(this.graph,E.UNSELECT),this.graph.unselect(e),I(this.graph,E.SELECT),this.graph.select(o)}ungroup(){const e=Q(this.graph);if(!e||e.length!=1)return;const a=e[0];if(!x(a))return;const r=a.getChildren();if(r)for(const o of r)o.setParent(null);a.setChildren(null),this.graph.removeCells([a])}setChildInfo(e){e.getChildren()&&this.cellGroupStyle(e)}cellGroupStyle(e){const a=e.getChildren();if(!a)return;const r=e.getBBox();a.forEach(o=>{const i=o.getBBox();let n=o.getData();n||(n={}),n.groupStyle={left:this.toPrecent((i.x-r.x)/r.width),top:this.toPrecent((i.top-r.top)/r.height),width:this.toPrecent(i.width/r.width),height:this.toPrecent(i.height/r.height)},o.setData(n,{silent:!0})})}toPrecent(e){return e*100}resize(e){const a=[{cells:e.getChildren()||[],parent:e}];for(;a.length>0;){const r=a.pop(),o=r.cells,i=r.parent.getBBox();o.forEach(n=>{const s=n,l=n.getData();if(!l)return;const c=l.groupStyle;s.setSize(i.width*c.width/100,i.height*c.height/100),s.setPosition(i.width*c.left/100+i.x,i.height*c.top/100+i.y);const g=s.getChildren();g&&g.length>0&&x(s)&&a.push({cells:g,parent:s})})}}rotate(e){this.groupAngle=e.getAngle()}rotating(e){const a=[{cells:e.getChildren()||[],parent:e,parentStartAngle:0}],r=e.getBBox(),o=e.getAngle(),i=o-this.groupAngle;for(this.groupAngle=o;a.length>0;)a.pop().cells.forEach(s=>{const l=s,c={cells:l.getChildren()||[],parent:l,parentStartAngle:0};if(a.push(c),!s.getData())return;let h=i;h=me.normalize(h+l.getAngle()),l.rotate(h,{absolute:!0,center:{x:r.center.x,y:r.center.y}})})}}const Eo=t=>t.shape=="text-block",Ao=t=>{let e=t.getAttrByPath("body/fill");(!e||e=="none")&&(e="");let a=t.getAttrByPath("body/stroke");(!a||a=="none")&&(a="");let r=t.getAttrByPath("body/strokeWidth");const o=t.getSize(),i=t.getPosition();let n,s;return n=t.getAttrByPath("label/text"),s={text:n||"",fontSize:F(t.getAttrByPath("text/fontSize"),12),fontColor:t.getAttrByPath("text/fill"),fontFamily:t.getAttrByPath("text/fontFamily")},{bgColor:e,borderWidth:P(F(r,1)),borderColor:a,borderDasharray:t.getAttrByPath("body/strokeDasharray"),width:P(o.width),height:P(o.height),x:P(i.x),y:P(i.y),rx:F(t.getAttrByPath("body/rx"),0),ry:F(t.getAttrByPath("body/ry"),0),angle:t.getAngle(),zIndex:P(F(t.getZIndex(),0)),fontValue:s,lineHeight:P(F(t.getAttrByPath("body/strokeWidth"),1)),lineColor:t.getAttrByPath("body/stroke")}},P=t=>parseInt(t+""),F=(t,e)=>{if(t==null||t==null)return e;try{return Number(t)}catch{return e}},J=t=>(t%360+360)%360,v=(t,e,a,r)=>({x:t,y:e,width:a,height:r}),b=(t,e,a,r,o,i,n)=>{let s=i+o;o=J(o),s=J(s);const l=a/2,c=r/2,g=Math.PI/180*o,h=Math.PI/180*s,R=t+l+l*Math.cos(g),A=e+c-c*Math.sin(g),D=t+l+l*Math.cos(h),ce=e+c-c*Math.sin(h);let L=s-o;L<0&&(L+=360);const de=L>180?1:0;let H=s>o?1:0;return n!=null&&(H=n),`M ${R} ${A} A ${l} ${c} 0 ${de} ${H} ${D} ${ce}`},S=(t,e,a,r,o)=>o=="south"?cr(t,e,a,r):o=="east"?dr(t,e,a,r):o=="west"?pr(t,e,a,r):lr(t,e,a,r),lr=(t,e,a,r)=>{const o=t+a/2,i=e,n=t,s=e+r,l=t+a,c=e+r;return`${o},${i} ${n},${s} ${l},${c}`},cr=(t,e,a,r)=>{const o=t+a/2,i=e+r/2,n=t,s=e,l=t+a;return`${o},${i} ${n},${s} ${l},${e}`},dr=(t,e,a,r)=>{const o=t,i=e+r/2,n=t+a,s=e,l=t+a,c=e+r;return`${o},${i} ${n},${s} ${l},${c}`},pr=(t,e,a,r)=>{const o=t+a,i=e+r/2,n=t,s=e,l=t,c=e+r;return`${o},${i} ${n},${s} ${l},${c}`},d=(t,e,a,r)=>{const o=a/2,i=r/2,n=t+o,s=e+i;return{cx:n,cy:s,rx:o,ry:i}},y=(t,e)=>{if(t.setVisible(e),x(t)){const a=t.getChildren();a&&a.forEach(r=>{r.setVisible(e)})}},oe=t=>N.DIS==t||N.GDIS==t||N.CBR==t||N.Fuse==t,x=t=>t&&t.data&&t.data.type==="group",ie=(t,e)=>{t.setData({children:e})},O=(t,e,a,r)=>{const o=[];e.sort((l,c)=>{const g=l.getZIndex()?l.getZIndex():0,h=c.getZIndex()?c.getZIndex():0;return g>h?1:g<h?-1:0});const i=[];let n=0;for(let l=0;l<e.length;l++){const g=e[l].clone();g.setPosition({x:0,y:0}),g.removeZIndex();const h=t.addNode(g);a.addChild(h),i.push(h.id),n=h.getZIndex(),o.push(h),r||h.setVisible(!1)}return ie(a,i),a.setZIndex(n+1),new sr(t).resize(a),o},No=(t,e)=>{t.setData({equipmentStatus:e})},X=t=>{const e=t.getData();return e&&e.equipmentStatus?e.equipmentStatus:p.NONE},Q=t=>{const e=[],a=t.getSelectedCells();for(const r of a)r.getParentId()==null&&e.push(r);return e},W=t=>{const e=[],a=t.getSelectedCells();for(const r of a){if(r.getParentId()!=null)continue;const o={value:r,descendantCells:[]};e.push(o);const i=r.getDescendants();i&&(o.descendantCells=i)}return e},Z=(t,e)=>{const a=new Set,r=t.components;if(!r)return a;for(const o of r)if(o.data){const i=ur(o.data),n=e.parseJSON(i);a.add(n)}return a},ur=t=>{const e=[];return e.push(t.value),t.descendantCells&&t.descendantCells.forEach(a=>{e.push(a)}),e},mr=t=>{const e=t.getData();return e&&e.equipmentConfig?e.equipmentConfig:null},Ro=(t,e)=>{t.setData({equipmentData:e})},ee=t=>{const e=t.getData();return e&&e.equipmentData?e.equipmentData:null},wo=(t,e)=>{if(t){if(e.data)t.fromJSON(e.data);else{t.fromJSON({}),t.clearBackground();return}e.graph&&(e.graph.background&&e.graph.background.trim().length>0?t.drawBackground({color:e.graph.background}):t.clearBackground(),e.graph.grid&&(e.graph.grid.show?(e.graph.grid.size>0&&t.setGridSize(e.graph.grid.size),t.showGrid()):t.hideGrid()))}},Io=t=>{let e="";return t.options.background&&t.options.background.color&&(e=t.options.background.color),{data:t.toJSON(),graph:{background:e,grid:{show:t.options.grid.visible,size:t.options.grid.size}}}},gr=t=>{const e=t.getGraphArea(),a=t.getPlugin(U.COMMON_PLUGIN_NAME);if(a){const r=a;r.getData().graphArea=e}ne(t)},ne=t=>{const e=t.getContentBBox(),a=10;let r,o;const i=t.getGraphArea(),n=t.getPlugin(U.COMMON_PLUGIN_NAME);let s;if(n){const g=n;g.getData().graphArea&&(s=g.getData().graphArea)}const l=e.x+e.width+a,c=e.y+e.height+a;if(l>=i.x+i.width&&(r=l),c>i.y+i.height&&(o=c),s&&(r==null&&l<s.x+s.width&&(r=s.width),o==null&&c<s.y+s.height&&(o=s.height)),n){const g=n;g.getData().resizeRefCount?g.getData().resizeRefCount=1+g.getData().resizeRefCount:g.getData().resizeRefCount=1}t.resize(r,o)},se=t=>{if(!t)return console.log("check isLine cell is null",t),!1;const e=t.getData();return!!(e&&e.isLine==!0)},ko=(t,e)=>{e.getAttrByPath("body/strokeWidth"),e.setAttrByPath("body/strokeWidth",t);const a=e.getSize();a.height=10+t,e.setSize(a)},I=(t,e)=>{const a=t.getPlugin("common");a&&a.getPluginImpl().increaseInvalidTrigger(e)},fr=(t,e)=>{const a=t.getPlugin("common");return a?a.getPluginImpl().validTrigger(e):!0};class hr{constructor(e,a,r){m(this,"view");m(this,"container");m(this,"options");m(this,"node");m(this,"graph");m(this,"eventListeners",new Map);this.options=e,this.node=a,this.graph=r,this.initializeTransform()}initializeTransform(){this.view=this.graph.findViewByCell(this.node),this.container=document.createElement("div"),this.container.className="x6-widget-transform",this.graph.container&&this.graph.container.appendChild(this.container)}renderHandles(){const e=this.view;if(e){const a=e.cell;se(a)&&this.container&&T(this.container,"line")}}on(e,a){return this.eventListeners.has(e)||this.eventListeners.set(e,[]),this.eventListeners.get(e).push(a),this}off(e,a){if(!e)this.eventListeners.clear();else if(!a)this.eventListeners.delete(e);else{const r=this.eventListeners.get(e);if(r){const o=r.indexOf(a);o>-1&&r.splice(o,1)}}return this}trigger(e,...a){const r=this.eventListeners.get(e);r&&r.forEach(i=>i(...a));const o=this.eventListeners.get("*");return o&&o.forEach(i=>i(e,...a)),this}dispose(){this.eventListeners.clear(),this.container&&this.container.parentNode&&this.container.parentNode.removeChild(this.container)}}class vr extends ge{constructor(a={}){super(a);m(this,"currGraph");fe(`${this.name}-line`,ar)}init(a){super.init(a),this.currGraph=a}createTransform(a){const r=this.getTransformOptions(a);if(r.resizable||r.rotatable){const o=new hr(r,a,this.currGraph);if(o.view){const i=o.view.cell;i&&se(i)&&o.renderHandles()}return o}return null}}class Cr{constructor(e){m(this,"graph");this.graph=e,this.bindEvent(e)}bindEvent(e){e.bindKey("ctrl+c",()=>{this.copy()}),e.bindKey("ctrl+v",()=>{this.paste()}),e.bindKey("delete",()=>{this.delete()}),e.bindKey("up",()=>{this.moveUp(1)}),e.bindKey("right",()=>{this.moveRight(1)}),e.bindKey("down",()=>{this.moveDown(1)}),e.bindKey("left",()=>{this.moveLeft(1)})}copy(){const e=this.graph.getSelectedCells();return e.length>0&&this.graph.copy(e),!1}paste(){this.graph.isClipboardEmpty()||this.graph.paste({offset:32})}delete(){const e=this.graph.getSelectedCells();e.length>0&&this.graph.removeCells(e)}moveUp(e){const a=this.graph.getSelectedCells();let r,o;for(const i of a)r=i,o=r.getPosition(),r.setPosition({x:o.x,y:o.y-e})}moveRight(e){const a=this.graph.getSelectedCells();let r,o;for(const i of a)r=i,o=r.getPosition(),r.setPosition({x:o.x+e,y:o.y})}moveDown(e){const a=this.graph.getSelectedCells();let r,o;for(const i of a)r=i,o=r.getPosition(),r.setPosition({x:o.x,y:o.y+e})}moveLeft(e){const a=this.graph.getSelectedCells();let r,o;for(const i of a)r=i,o=r.getPosition(),r.setPosition({x:o.x-e,y:o.y})}}class Lo{constructor(e,a,r){m(this,"graph");m(this,"contextMenuContainer");m(this,"contextMenuParentContainer");m(this,"graphGroup");m(this,"emit");m(this,"contextmenu");this.graph=e,this.contextmenu=a,r&&(this.emit=r),e.getPlugin("data")&&(this.graphGroup=e.getPlugin("data").getData().group),new Cr(this.graph),this.bindEvent()}bindEvent(){this.graph.on("resize",()=>{const e=this.graph.getPlugin(U.COMMON_PLUGIN_NAME);if(e){const a=e;let r=a.getData().resizeRefCount;if(r&&r>0){a.getData().resizeRefCount=r-1;return}}this.onResize()}),this.graph.on("blank:click",e=>{this.onBlankClick(),this.emit&&this.emit({type:B.BLANK_CLICK,eventParam:{...e,graph:this.graph}})}),this.graph.on("cell:contextmenu",e=>{this.onContextMenu(e)}),this.graph.on("node:moved",()=>{ne(this.graph)}),this.graph.on("node:selected",e=>{fr(this.graph,E.SELECT)&&(this.removeContextMenu(),!(this.graph.getSelectedCellCount()>1)&&this.emit&&this.emit({type:B.NODE_CLICK,eventParam:{...e,graph:this.graph}}))}),this.graph.on("node:resizing",e=>{this.onNodeResizing(e)}),this.graph.on("node:rotate",e=>{this.onNodeRotate(e)}),this.graph.on("node:rotating",e=>{this.onNodeRotating(e)}),this.graph.on("node:added",e=>{this.onNodeAdd(e,this.graph)})}onBlankClick(){this.removeContextMenu()}onResize(){gr(this.graph)}onNodeResizing(e){const a=e.node;x(a)&&this.graphGroup.resize(a)}onNodeRotate(e){const a=e.node;x(a)&&this.graphGroup.rotate(a)}onNodeRotating(e){const a=e.node;x(a)&&this.graphGroup.rotating(a)}onContextMenu(e){const a=this.graph.getSelectedCells();let r=!1;a&&(r=a.includes(e.cell)),r||(this.graph.clearTransformWidgets(),I(this.graph,E.UNSELECT),this.graph.unselect(a,{silent:!1}),I(this.graph,E.SELECT),this.graph.resetSelection(e.cell,{silent:!1})),this.createContextMenu(e)}onContextMenuItemClick(e,a){!a.type||!a.enable||(this.removeContextMenu(),this.contextmenu.trigger(a,this.graph,this.graphGroup))}createContextMenu(e){const a=this.contextmenu.create(this.graph);if(a.length==0){console.log("contextmenu items size less 1, no display");return}this.contextMenuParentContainer||(this.contextMenuParentContainer=z("div"),T(this.contextMenuParentContainer,"graph-contextmenu-container"),he(this.contextMenuParentContainer,"id","graph-contextmenu-container"),this.graph.container.appendChild(this.contextMenuParentContainer)),$(this.contextMenuParentContainer,"graph-contextmenu-container-hide")&&ve(this.contextMenuParentContainer,"graph-contextmenu-container-hide"),this.contextMenuParentContainer.hasChildNodes()&&this.contextMenuParentContainer.removeChild(this.contextMenuContainer),this.contextMenuContainer=z("div"),Ce(this.contextMenuParentContainer,this.contextMenuContainer),T(this.contextMenuContainer,"graph-contextmenu");for(const r of a){const o=z("div");o.innerHTML=r.title,T(o,"graph-contextmenu-item"),r.enable||T(o,"graph-contextmenu-item-disabled"),Y(o,{"data-type":r.type}),o.addEventListener("click",i=>{this.onContextMenuItemClick(i,r)}),xe(this.contextMenuContainer,o)}Y(this.contextMenuParentContainer,{style:"left:"+e.x+"px;top:"+e.y+"px"})}removeContextMenu(){this.contextMenuParentContainer&&this.contextMenuParentContainer.hasChildNodes()&&(this.contextMenuParentContainer.removeChild(this.contextMenuContainer),$(this.contextMenuParentContainer,"graph-contextmenu-container-hide")||T(this.contextMenuParentContainer,"graph-contextmenu-container-hide"))}onNodeAdd(e,a){this.handleGroupDndAdd(e.node,a)}handleGroupDndAdd(e,a){const r=e.getData();if(!r||!r.dndCells)return;let o=r.dndCells;if(e.setData({dndCells:null}),o.length<2)return;const i=o.slice(1);O(a,i,e,!0)}}class zo{toFront(e){const a=e.getSelectedCells();if(!a||a.length==0)return;let r;for(const o of a){if(r=o.getChildren(),r!=null){const i=[];for(const n of r)i.push(n);i.sort((n,s)=>{let l=n.zIndex?n.zIndex:0,c=s.zIndex?s.zIndex:0;return l>c?1:l<c?-1:0}),i.forEach(n=>{n.toFront()})}o.toFront()}}toBack(e){const a=e.getSelectedCells();if(!a||a.length==0)return;let r;for(const o of a)if(o.toBack(),r=o.getChildren(),r!=null){const i=[];for(const n of r)i.push(n);i.sort((n,s)=>{let l=n.zIndex?n.zIndex:0,c=s.zIndex?s.zIndex:0;return l>c?-1:l<c?1:0}),i.forEach(n=>{n.toBack()})}}resize(e,a){x(e)&&a.trigger("node:resizing",{node:e})}rotate(e,a){x(e)&&a.rotate(e)}rotating(e,a){x(e)&&a.rotating(e)}deleteSelectCells(e){const a=e.getSelectedCells();a.length>0&&e.removeCells(a)}}class Mo{constructor(e){m(this,"graph");m(this,"dnd");this.graph=e}use(e){const a=this.graph;a.use(new vr({resizing:{enabled:!0,orthogonal:!0},rotating:{enabled:!0,grid:1}})),a.use(new Se({enabled:!0})),this.dnd=new ye({target:a,scaled:!1,getDropNode(r,o){const i=o.sourceNode.getData();if(i&&i.dndMock===!0&&i.dndCells){const n=i.dndCells[0].clone();return n.setData({dndCells:i.dndCells}),n.setPosition(r.getPosition()),n}return r.clone()}}),a.use(new be({enabled:!0})),a.use(new De({enabled:!0})),a.use(new Pe({enabled:!0})),a.use(new Fe({enabled:!0,showNodeSelectionBox:!0,showEdgeSelectionBox:!0,rubberband:!0,strict:!0})),a.use(new Te),a.use(new tr({data:e})),a.use(new Za({enabled:!0,data:{}}))}}class Vo{constructor(){m(this,"equipmentDataMap");this.equipmentDataMap=new Map}render(e,a){a.forEach(i=>{this.equipmentDataMap.set(i.id,i)});const r=e.getCells(),o={yxMap:new Map};return this.handleSaddrCells(r,e,o),o}handleSaddrCells(e,a,r){for(const o of e)this.handleSaddrCell(o,a,r)}handleSaddrCell(e,a,r){const o=mr(e);o&&this.handleYx(e,a,o,r)}handleYx(e,a,r,o){var c,g;const i=e,n={cell:e,equipmentConfig:r,show:[]};let s=o.yxMap.get(r.saddr);if(s||(s=[],o.yxMap.set(r.saddr,s)),s.push(n),r.showCfg&&r.showCfg.length>0){for(const h of r.showCfg)if(h.type==q.EQUIPMENT){const A=[...Z(h.showValue,a)][0],D=A[0];D.setPosition(i.position()),D.setVisible(!1),a.addCell(D),A.length>1&&O(a,A.slice(1),D,!1),(c=n.show)==null||c.push({value:h.value,showValue:D})}else h.type==q.TEXT&&((g=n.show)==null||g.push({value:h.value,showValue:h.showValue}));return}const l=ee(e);l&&oe(l.equipmentType)&&this.appendCbrDisCell(a,e,l,n)}appendCbrDisCell(e,a,r,o){var g;const i=ee(a);if(!i){console.log("组件data属性中未找到equipemntData数据",a);return}const n=this.equipmentDataMap.get(i.equipmentId);if(!n||n.components.length<2){console.log("开关或刀闸组件不存在或个数<2",n,a.id);return}const s=r.equipmentIndex==0?1:0;let l=[];if(r.loadType=="create"){const h=n.components[s],R=e.createNode(h.data.value);l.push(R)}else l=[...Z(n,e)][s];const c=l[0];if(c.setPosition(a.position()),c.setVisible(!1),e.addCell(c),l.length>1){const h=l.slice(1);O(e,h,c,!1)}(g=o.show)==null||g.push({value:n.components[s].equipmentStatus,showValue:c,isCbrDis:!0})}}class qo{constructor(e,a){m(this,"graph");m(this,"t");this.graph=e,this.t=a}async create(e){const a=new rr(0),r=W(this.graph);if(oe(e.type)){if(r.length!=2)return a.msg=this.t("graphDefine.graphCreate.needTwoDevices"),a;let n=!1,s=!1;for(const l of r){const c=X(l.value);c==p.OPEN?n=!0:c==p.CLOSE&&(s=!0)}if(!(n&&s))return a.msg=this.t("graphDefine.graphCreate.needCorrectStatus"),a}else if(r.length!=1)return a.msg=this.t("graphDefine.graphCreate.needOneDevice"),a;const o=[];for(const n of r){const s=await this.getCellImage(n.value);o.push({data:n,img:s,equipmentStatus:X(n.value)})}const i={id:"",name:e.name,type:e.type,components:o};return a.data=i,a.setSuccess(),a}getCellImage(e){return new Promise(r=>{const o={preserveDimensions:!0,backgroundColor:"#fff"};o.viewBox=e.getBBox(),this.graph.toJPEG(i=>{r(i)},o)})}}let Bo=class{constructor(e){m(this,"t");this.t=e}createContextMenu(e){const a=W(e);if(!a||a.length==0)return[];const r=[];let o=!1;a.length>1&&(o=!0);let i=!1;if(a.length==1){for(const n of a)if(x(n.value)){i=!0;break}}return r.push({title:this.t("graphDefine.contextMenu.group"),type:C.GROUP,enable:o}),r.push({title:this.t("graphDefine.contextMenu.ungroup"),type:C.UNGROUP,enable:i}),r.push({title:this.t("graphDefine.contextMenu.setStatus"),type:C.EQUIPMENT_STATUS,enable:!0}),r}trigger(e,a,r){switch(e.type){case C.GROUP:r.group();break;case C.UNGROUP:r.ungroup();break}}};const j=(t,e)=>(t.install=a=>{for(const r of[t,...Object.values({})])a.component(r.name,r)},t),_o=j(Ne),xr=[{id:"0100",type:"CBR",name:"hmi.graph.equipmentName.breaker_vertical"},{id:"0101",type:"CBR",name:"hmi.graph.equipmentName.breaker_vertical"},{id:"0200",type:"CBR",name:"hmi.graph.equipmentName.breaker_horizontal"},{id:"0201",type:"CBR",name:"hmi.graph.equipmentName.breaker_horizontal"},{id:"0300",type:"CBR",name:"hmi.graph.equipmentName.breaker_invalid_vertical"},{id:"0301",type:"CBR",name:"hmi.graph.equipmentName.breaker_invalid_vertical"},{id:"0400",type:"CBR",name:"hmi.graph.equipmentName.breaker_invalid_horizontal"},{id:"0401",type:"CBR",name:"hmi.graph.equipmentName.breaker_invalid_horizontal"},{id:"0500",type:"DIS",name:"hmi.graph.equipmentName.disconnector_vertical"},{id:"0501",type:"DIS",name:"hmi.graph.equipmentName.disconnector_vertical"},{id:"0600",type:"DIS",name:"hmi.graph.equipmentName.disconnector_horizontal"},{id:"0601",type:"DIS",name:"hmi.graph.equipmentName.disconnector_horizontal"},{id:"0700",type:"DIS",name:"hmi.graph.equipmentName.disconnector_invalid_vertical"},{id:"0701",type:"DIS",name:"hmi.graph.equipmentName.disconnector_invalid_vertical"},{id:"0800",type:"DIS",name:"hmi.graph.equipmentName.disconnector_invalid_horizontal"},{id:"0801",type:"DIS",name:"hmi.graph.equipmentName.disconnector_invalid_horizontal"},{id:"0900",type:"Fuse",name:"hmi.graph.equipmentName.hv_fuse"},{id:"0901",type:"Fuse",name:"hmi.graph.equipmentName.hv_fuse"},{id:"0A00",type:"PTR2",name:"hmi.graph.equipmentName.station_transformer_2w"},{id:"0B00",type:"PTR2",name:"hmi.graph.equipmentName.transformer_y_d_11"},{id:"0C00",type:"PTR2",name:"hmi.graph.equipmentName.transformer_d_y_11"},{id:"0D00",type:"PTR2",name:"hmi.graph.equipmentName.transformer_d_d"},{id:"0E00",type:"PTR2",name:"hmi.graph.equipmentName.transformer_y_y_11"},{id:"0F00",type:"PTR3",name:"hmi.graph.equipmentName.transformer_y_y_12_d_11"},{id:"1000",type:"PTR3",name:"hmi.graph.equipmentName.transformer_y_d_11_d_11"},{id:"1100",type:"PTR3",name:"hmi.graph.equipmentName.transformer_y_y_v"},{id:"1200",type:"PTR3",name:"hmi.graph.equipmentName.transformer_autotransformer"},{id:"1300",type:"VTR",name:"hmi.graph.equipmentName.voltage_transformer_2w"},{id:"1400",type:"VTR",name:"hmi.graph.equipmentName.voltage_transformer_3w"},{id:"1500",type:"VTR",name:"hmi.graph.equipmentName.voltage_transformer_4w"},{id:"1600",type:"Arrester",name:"hmi.graph.equipmentName.arrester"},{id:"1700",type:"Capacitor_S",name:"hmi.graph.equipmentName.capacitor_horizontal"},{id:"1800",type:"Capacitor_S",name:"hmi.graph.equipmentName.capacitor_vertical"},{id:"1900",type:"Reactor_S",name:"hmi.graph.equipmentName.reactor"},{id:"1A00",type:"Reactor_S",name:"hmi.graph.equipmentName.split_reactor"},{id:"1B00",type:"IND",name:"hmi.graph.equipmentName.power_inductor"},{id:"1C00",type:"IFL",name:"hmi.graph.equipmentName.feeder"},{id:"1D00",type:"GND",name:"hmi.graph.equipmentName.ground"},{id:"1E00",type:"LTC",name:"hmi.graph.equipmentName.tap_changer"},{id:"1F00",type:"P",name:"hmi.graph.equipmentName.connection_point"},{id:"3000",type:"PTR3",name:"hmi.graph.equipmentName.transformer_y_y_12_d_11_new"},{id:"3200",type:"VTR",name:"hmi.graph.equipmentName.pt"},{id:"3300",type:"Arrester",name:"hmi.graph.equipmentName.arrester_new"},{id:"3400",type:"DIS",name:"hmi.graph.equipmentName.disconnector_vertical_new"},{id:"3401",type:"DIS",name:"hmi.graph.equipmentName.disconnector_vertical_new"},{id:"3500",type:"DIS",name:"hmi.graph.equipmentName.disconnector_horizontal_new"},{id:"3501",type:"DIS",name:"hmi.graph.equipmentName.disconnector_horizontal_new"},{id:"3600",type:"Arrester",name:"hmi.graph.equipmentName.arrester_new_vertical"},{id:"3700",type:"DIS",name:"hmi.graph.equipmentName.disconnector_vertical_left_new"},{id:"3701",type:"DIS",name:"hmi.graph.equipmentName.disconnector_vertical_left_new"}],Sr={data:xr},yr={shape:"0A00",width:17,height:30,markup:[{tagName:"ellipse",groupSelector:"e",attrs:{...d(0,0,17,17)}},{tagName:"ellipse",groupSelector:"e",attrs:{...d(0,12,17,17)}}],attrs:{e:{fill:"transparent",stroke:"#000"}}},br={shape:"0B00",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,0,23,22.5)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,15.86,23,22.5)}},{tagName:"line",groupSelector:"line",attrs:{x1:6.33,y1:4.63,x2:12,y2:9.33}},{tagName:"line",groupSelector:"line",attrs:{x1:16.33,y1:4.33,x2:11,y2:9.33}},{tagName:"line",groupSelector:"line",attrs:{x1:11.33,y1:8.75,x2:11.33,y2:14.33}},{tagName:"polygon",groupSelector:"polygon",attrs:{points:S(6.67,26.33,9.33,7.33)}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"},polygon:{fill:"transparent",stroke:"#000"}}},Dr={shape:"0C00",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,0,23,22.5)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,15.86,23,22.5)}},{tagName:"line",groupSelector:"line",attrs:{x1:6.67,y1:25.63,x2:12.33,y2:30.33}},{tagName:"line",groupSelector:"line",attrs:{x1:16.67,y1:25.33,x2:11.33,y2:30.33}},{tagName:"line",groupSelector:"line",attrs:{x1:11.67,y1:29.75,x2:11.67,y2:35.33}},{tagName:"polygon",groupSelector:"triangle",attrs:{points:S(6.67,4.33,9.33,7.33)}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"},triangle:{fill:"transparent",stroke:"#000"}}},Pr={shape:"0D00",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,0,23,22.5)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,15.86,23,22.5)}},{tagName:"polygon",groupSelector:"polygon",attrs:{points:S(6.67,4.67,9.33,7.33)}},{tagName:"polygon",groupSelector:"polygon",attrs:{points:S(6.67,25.33,9.33,7.33)}}],attrs:{ellipse:{fill:"transparent",stroke:"#000"},polygon:{fill:"transparent",stroke:"#000"}}},Fr={shape:"0E00",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,0,23,22.5)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,15.86,23,22.5)}},{tagName:"line",groupSelector:"line",attrs:{x1:6.33,y1:4.63,x2:12.57,y2:9.33}},{tagName:"line",groupSelector:"line",attrs:{x1:17.33,y1:4.33,x2:11.47,y2:9.33}},{tagName:"line",groupSelector:"line",attrs:{x1:11.83,y1:8.75,x2:11.83,y2:14.33}},{tagName:"line",groupSelector:"line",attrs:{x1:6.33,y1:25.3,x2:12.38,y2:30.17}},{tagName:"line",groupSelector:"line",attrs:{x1:17,y1:25,x2:11.31,y2:30.17}},{tagName:"line",groupSelector:"line",attrs:{x1:11.67,y1:29.56,x2:11.67,y2:35.33}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"}}},Tr={shape:"0F00",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(15.33,8.2,23,22.5)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,0,23,22.5)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,15.86,23,22.5)}},{tagName:"line",groupSelector:"line",attrs:{x1:6.33,y1:4.63,x2:12,y2:9.33}},{tagName:"line",groupSelector:"line",attrs:{x1:16.33,y1:4.33,x2:11,y2:9.33}},{tagName:"line",groupSelector:"line",attrs:{x1:11.33,y1:8.75,x2:11.33,y2:14.33}},{tagName:"polygon",groupSelector:"triangle",attrs:{points:S(6.67,26.33,9.33,7.33)}},{tagName:"line",groupSelector:"line",attrs:{x1:23.33,y1:15.96,x2:29,y2:20.67}},{tagName:"line",groupSelector:"line",attrs:{x1:33.33,y1:15.67,x2:28,y2:20.67}},{tagName:"line",groupSelector:"line",attrs:{x1:28.33,y1:20.08,x2:28.33,y2:25.67}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"},triangle:{fill:"transparent",stroke:"#000"}}},Er={shape:"1A00",markup:[{tagName:"path",groupSelector:"arc",attrs:{d:b(5,6.67,16,16,-66.93991158951788,315.9759887326147)}},{tagName:"line",groupSelector:"line",attrs:{x1:10.33,y1:15,x2:0,y2:15}},{tagName:"line",groupSelector:"line",attrs:{x1:.33,y1:28,x2:.33,y2:15}},{tagName:"line",groupSelector:"line",attrs:{x1:12.67,y1:6.83,x2:12.67,y2:0}},{tagName:"line",groupSelector:"line",attrs:{x1:25,y1:28,x2:25,y2:15}},{tagName:"line",groupSelector:"line",attrs:{x1:10.33,y1:22,x2:10.33,y2:15}},{tagName:"line",groupSelector:"line",attrs:{x1:15.33,y1:22,x2:15.33,y2:15}},{tagName:"line",groupSelector:"line",attrs:{x1:25,y1:15,x2:15,y2:15}}],attrs:{line:{stroke:"#000"},arc:{fill:"transparent",stroke:"#000"}}},Ar={shape:"1B00",markup:[{tagName:"path",groupSelector:"arc",attrs:{d:b(0,0,9,9,0,180,0)}},{tagName:"path",groupSelector:"arc",attrs:{d:b(9,0,9,9,0,180,0)}},{tagName:"line",groupSelector:"line",attrs:{x1:0,y1:4,x2:0,y2:26}},{tagName:"line",groupSelector:"line",attrs:{x1:36,y1:3,x2:36,y2:25}},{tagName:"path",groupSelector:"arc",attrs:{d:b(18,0,9,9,0,180,0)}},{tagName:"path",groupSelector:"arc",attrs:{d:b(27,0,9,9,0,180,0)}}],attrs:{line:{stroke:"#000"},arc:{fill:"transparent",stroke:"#000"}}},Nr={shape:"1C00",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:3,y1:42.33,x2:3,y2:9.67}},{tagName:"polygon",groupSelector:"triangle",attrs:{points:S(0,0,6,9.33)}}],attrs:{line:{stroke:"#000"},triangle:{stroke:"#000"}}},Rr={shape:"1D00",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:7.5,y1:0,x2:7.5,y2:5}},{tagName:"line",groupSelector:"line",attrs:{x1:0,y1:5,x2:15,y2:5}},{tagName:"line",groupSelector:"line",attrs:{x1:2.5,y1:8.17,x2:12.5,y2:8.17}},{tagName:"line",groupSelector:"line",attrs:{x1:4.33,y1:11.33,x2:10.33,y2:11.33}}],attrs:{line:{stroke:"#000000"},triangle:{stroke:"#000"}}},wr={shape:"1E00",markup:[{tagName:"polygon",groupSelector:"triangle",attrs:{points:S(38.33,0,6,6)}},{tagName:"line",groupSelector:"line",attrs:{x1:41.67,y1:0,x2:41.67,y2:40.67}}],attrs:{line:{stroke:"#000"},triangle:{stroke:"#000",transform:"rotate(360)"}}},Ir={shape:"1F00",markup:[{tagName:"ellipse",groupSelector:"e",attrs:{...d(0,0,4,4)}},{tagName:"ellipse",groupSelector:"e",attrs:{...d(0,0,4,4)}}],attrs:{width:4,height:4,e:{fill:"transparent",stroke:"#000"}}},kr={shape:"0100",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:30,x2:5,y2:35}},{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:0,x2:5,y2:4}},{tagName:"rect",groupSelector:"rect",attrs:{...v(0,4.6,10,25)}}],attrs:{line:{stroke:"#000"},rect:{fill:"transparent",stroke:"#000"}}},Lr={shape:"0101",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:30,x2:5,y2:35}},{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:0,x2:5,y2:4}},{tagName:"rect",groupSelector:"rect",attrs:{...v(0,4.6,10,25)}}],attrs:{line:{stroke:"#000"},rect:{fill:"#000",stroke:"#000"}}},zr={shape:"0200",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:5,x2:0,y2:5}},{tagName:"line",groupSelector:"line",attrs:{x1:35,y1:5,x2:31,y2:5}},{tagName:"rect",groupSelector:"rect",attrs:{...v(5.5,0,25,10)}}],attrs:{line:{stroke:"#000"},rect:{fill:"transparent",stroke:"#000"}}},Mr={shape:"0201",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:5,x2:0,y2:5}},{tagName:"line",groupSelector:"line",attrs:{x1:35,y1:5,x2:31,y2:5}},{tagName:"rect",groupSelector:"rect",attrs:{...v(5.5,0,25,10)}}],attrs:{line:{stroke:"#000"},rect:{fill:"#000",stroke:"#000"}}},Vr={shape:"0300",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:28,x2:5,y2:33}},{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:0,x2:5,y2:5.5}},{tagName:"rect",groupSelector:"rect",attrs:{...v(0,5,10,23.5)}},{tagName:"line",groupSelector:"line",attrs:{x1:.67,y1:5,x2:9.67,y2:28}}],attrs:{line:{stroke:"#000"},rect:{fill:"transparent",stroke:"#000"}}},qr={shape:"0301",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:28,x2:5,y2:33}},{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:0,x2:5,y2:6}},{tagName:"rect",groupSelector:"rect",attrs:{...v(0,5,10,23.5)}},{tagName:"line",groupSelector:"line",attrs:{x1:.17,y1:5,x2:9.5,y2:28}},{tagName:"line",groupSelector:"line",attrs:{x1:9.83,y1:5,x2:.5,y2:28}}],attrs:{line:{stroke:"#000"},rect:{fill:"transparent",stroke:"#000"}}},Br={shape:"0400",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:5,x2:0,y2:5}},{tagName:"line",groupSelector:"line",attrs:{x1:33,y1:5,x2:27.5,y2:5}},{tagName:"rect",groupSelector:"rect",attrs:{...v(4.5,0,23.5,10)}},{tagName:"line",groupSelector:"line",attrs:{x1:28,y1:.67,x2:5,y2:9.67}}],attrs:{line:{stroke:"#000"},rect:{fill:"transparent",stroke:"#000"}}},Or={shape:"0401",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:5,x2:0,y2:5}},{tagName:"line",groupSelector:"line",attrs:{x1:33,y1:5,x2:28,y2:5}},{tagName:"rect",groupSelector:"rect",attrs:{...v(4.5,0,23.5,10)}},{tagName:"line",groupSelector:"line",attrs:{x1:28,y1:.17,x2:5,y2:9.5}},{tagName:"line",groupSelector:"line",attrs:{x1:28,y1:9.83,x2:5,y2:.5}}],attrs:{line:{stroke:"#000"},rect:{fill:"transparent",stroke:"#000"}}},_r={shape:"0500",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:3,y1:28.83,x2:11,y2:11.83}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(.33,4,3.33,3.67)}},{tagName:"line",groupSelector:"line",attrs:{x1:2,y1:0,x2:2,y2:4}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,28,3.33,3.67)}},{tagName:"line",groupSelector:"line",attrs:{x1:1.67,y1:35.67,x2:1.67,y2:31.67}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"}}},Gr={shape:"0501",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,4,3.33,3.67)}},{tagName:"line",groupSelector:"line",attrs:{x1:1.67,y1:0,x2:1.67,y2:4}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,28,3.33,3.67)}},{tagName:"line",groupSelector:"line",attrs:{x1:1.67,y1:35.67,x2:1.67,y2:31.67}},{tagName:"line",groupSelector:"line",attrs:{x1:3.33,y1:5.67,x2:3.33,y2:29.33}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"}}},Ur={shape:"0600",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:19,y1:0,x2:6.33,y2:8.5}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(28,8.33,3.67,3.33)}},{tagName:"line",groupSelector:"line",attrs:{x1:35.67,y1:10,x2:31.67,y2:10}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(4,8,3.67,3.33)}},{tagName:"line",groupSelector:"line",attrs:{x1:0,y1:9.67,x2:4,y2:9.67}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"}}},Wr={shape:"0601",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(4,0,3.67,3.33)}},{tagName:"line",groupSelector:"line",attrs:{x1:0,y1:1.66,x2:4,y2:1.66}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(28,0,3.67,3.33)}},{tagName:"line",groupSelector:"line",attrs:{x1:35.67,y1:1.66,x2:31.67,y2:1.66}},{tagName:"line",groupSelector:"line",attrs:{x1:5.67,y1:0,x2:29.34,y2:0}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"}}},jr={shape:"0700",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:5.33,y1:21.33,x2:0,y2:17}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(1,4,3.33,3.67)}},{tagName:"line",groupSelector:"line",attrs:{x1:2.67,y1:0,x2:2.67,y2:4}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(.67,28,3.33,3.67)}},{tagName:"line",groupSelector:"line",attrs:{x1:2.33,y1:35.67,x2:2.33,y2:31.67}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"}}},Hr={shape:"0701",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(.67,4,3.33,3.67)}},{tagName:"line",groupSelector:"line",attrs:{x1:2.33,y1:0,x2:2.33,y2:4}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(.67,28,3.33,3.67)}},{tagName:"line",groupSelector:"line",attrs:{x1:2.33,y1:35.67,x2:2.33,y2:31.67}},{tagName:"line",groupSelector:"line",attrs:{x1:0,y1:15.67,x2:5,y2:21.67}},{tagName:"line",groupSelector:"line",attrs:{x1:4.83,y1:16.5,x2:.5,y2:21.83}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"}}},$r={shape:"0800",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:14.33,y1:5.33,x2:18.67,y2:0}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(28,1,3.67,3.33)}},{tagName:"line",groupSelector:"line",attrs:{x1:35.67,y1:2.67,x2:31.67,y2:2.67}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(4,.67,3.67,3.33)}},{tagName:"line",groupSelector:"line",attrs:{x1:0,y1:2.33,x2:4,y2:2.33}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"}}},Yr={shape:"0801",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(28,.67,3.67,3.33)}},{tagName:"line",groupSelector:"line",attrs:{x1:35.67,y1:2.33,x2:31.67,y2:2.33}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(4,.67,3.67,3.33)}},{tagName:"line",groupSelector:"line",attrs:{x1:0,y1:2.33,x2:4,y2:2.33}},{tagName:"line",groupSelector:"line",attrs:{x1:19,y1:0,x2:14,y2:5}},{tagName:"line",groupSelector:"line",attrs:{x1:19.17,y1:4.83,x2:13.83,y2:.5}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"}}},Kr={shape:"0900",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:22,x2:5,y2:33}},{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:0,x2:5,y2:11.33}},{tagName:"rect",groupSelector:"rect",attrs:{...v(0,5,10,23.5)}},{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:22.67,x2:8,y2:15.67}}],attrs:{line:{stroke:"#000"},rect:{fill:"transparent",stroke:"#000"}}},Jr={shape:"0901",markup:[{tagName:"rect",groupSelector:"rect",attrs:{...v(0,5,10,23.5)}},{tagName:"line",groupSelector:"line",attrs:{x1:5,y1:0,x2:5,y2:32.67}}],attrs:{line:{stroke:"#000"},rect:{fill:"transparent",stroke:"#000"}}},Xr={shape:"1000",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(15.67,9.67,22.99,22.47)}},{tagName:"polygon",groupSelector:"triangle",attrs:{points:S(25,16.33,9.33,7.33)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,0,22.99,22.47)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,15.86,22.99,22.47)}},{tagName:"line",groupSelector:"line",attrs:{x1:6.33,y1:4.63,x2:12,y2:9.33}},{tagName:"line",groupSelector:"line",attrs:{x1:16.33,y1:4.33,x2:11,y2:9.33}},{tagName:"line",groupSelector:"line",attrs:{x1:11.33,y1:8.75,x2:11.33,y2:14.33}},{tagName:"polygon",groupSelector:"triangle",attrs:{points:S(6.67,26.33,9.33,7.33)}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"},triangle:{fill:"transparent",stroke:"#000"}}},Qr={shape:"1100",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,0,23,22.47)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,15.86,23,22.47)}},{tagName:"line",groupSelector:"line",attrs:{x1:6.33,y1:4.63,x2:12.57,y2:9.33}},{tagName:"line",groupSelector:"line",attrs:{x1:17.33,y1:4.33,x2:11.47,y2:9.33}},{tagName:"line",groupSelector:"line",attrs:{x1:11.83,y1:8.75,x2:11.83,y2:14.33}},{tagName:"line",groupSelector:"line",attrs:{x1:6.33,y1:25.3,x2:12.38,y2:30.17}},{tagName:"line",groupSelector:"line",attrs:{x1:17,y1:25,x2:11.31,y2:30.17}},{tagName:"line",groupSelector:"line",attrs:{x1:11.67,y1:29.56,x2:11.67,y2:35.33}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(15.67,7.53,23,22.47)}},{tagName:"line",groupSelector:"line",attrs:{x1:25.67,y1:16,x2:25,y2:21.33}},{tagName:"line",groupSelector:"line",attrs:{x1:25,y1:20.67,x2:33,y2:20.67}},{tagName:"line",groupSelector:"line",attrs:{x1:33.33,y1:21,x2:32.67,y2:16.33}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"},triangle:{fill:"transparent",stroke:"#000"}}},Zr={shape:"1200",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(11,10.67,23,22.47)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(11,26.53,23,22.47)}},{tagName:"line",groupSelector:"line",attrs:{x1:17.33,y1:15.29,x2:23,y2:20}},{tagName:"line",groupSelector:"line",attrs:{x1:27.33,y1:15,x2:22,y2:20}},{tagName:"line",groupSelector:"line",attrs:{x1:22.33,y1:19.41,x2:22.33,y2:25}},{tagName:"polygon",groupSelector:"triangle",attrs:{points:S(17.67,37,9.33,7.33)}},{tagName:"line",groupSelector:"line",attrs:{x1:22.33,y1:6,x2:22.33,y2:0}},{tagName:"path",groupSelector:"arc",attrs:{d:b(0,4.67,34.33,34.33,.46,71.9,0)}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"},triangle:{fill:"transparent",stroke:"#000"},arc:{fill:"transparent",stroke:"#000"}}},eo={shape:"1300",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:8,y1:40,x2:8,y2:28.83}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,0,16.67,17)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,12,16.67,17)}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"}}},to={shape:"1400",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(12,0,16.67,17)}},{tagName:"line",groupSelector:"line",attrs:{x1:14.33,y1:34.33,x2:14.33,y2:27.16}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,0,16.67,17)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(6.33,10.33,16.67,17)}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"}}},ao={shape:"1500",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(21.33,6,16.67,17)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,6,16.67,17)}},{tagName:"line",groupSelector:"line",attrs:{x1:18.67,y1:36,x2:18.67,y2:28.83}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(10.67,0,16.67,17)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(10.67,12,16.67,17)}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"}}},ro={shape:"1600",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:4.83,y1:28.33,x2:4.83,y2:35.33}},{tagName:"line",groupSelector:"line",attrs:{x1:4.83,y1:0,x2:4.83,y2:10}},{tagName:"rect",groupSelector:"rect",attrs:{...v(0,7.67,9.33,21.17)}},{tagName:"polygon",groupSelector:"triangle",attrs:{points:S(2.33,10,4.83,6.67,"south")}}],attrs:{line:{stroke:"#000"},rect:{fill:"transparent",stroke:"#000"},triangle:{fill:"#000",stroke:"#000"}}},oo={shape:"1700",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:19.67,y1:0,x2:19.67,y2:16}},{tagName:"line",groupSelector:"line",attrs:{x1:9.67,y1:0,x2:9.67,y2:16}},{tagName:"line",groupSelector:"line",attrs:{x1:29,y1:8,x2:20,y2:8}},{tagName:"line",groupSelector:"line",attrs:{x1:9,y1:8.33,x2:0,y2:8.33}}],attrs:{line:{stroke:"#000"}}},io={shape:"1800",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:16,y1:19.67,x2:0,y2:19.67}},{tagName:"line",groupSelector:"line",attrs:{x1:16,y1:9.67,x2:0,y2:9.67}},{tagName:"line",groupSelector:"line",attrs:{x1:8,y1:29,x2:8,y2:20}},{tagName:"line",groupSelector:"line",attrs:{x1:8,y1:9,x2:8,y2:0}}],attrs:{line:{stroke:"#000"}}},no={shape:"1900",markup:[{tagName:"path",groupSelector:"arc",attrs:{d:b(0,5.17,16,16,270,180)}},{tagName:"path",groupSelector:"arc",attrs:{d:b(0,5.17,16,16,170,180)}},{tagName:"line",groupSelector:"line",attrs:{x1:8,y1:12.84,x2:0,y2:12.84}},{tagName:"line",groupSelector:"line",attrs:{x1:8,y1:12.84,x2:8,y2:0}},{tagName:"line",groupSelector:"line",attrs:{x1:8,y1:27.5,x2:8,y2:21}}],attrs:{line:{stroke:"#000"},arc:{fill:"transparent",stroke:"#000"}}},so={shape:"3000",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(15.09,7.87,23,22.47)}},{tagName:"line",groupSelector:"line",attrs:{x1:31,y1:21.74,x2:26.52,y2:17.5}},{tagName:"line",groupSelector:"line",attrs:{x1:23.09,y1:22,x2:27.31,y2:17.5}},{tagName:"line",groupSelector:"line",attrs:{x1:27.05,y1:18.03,x2:27.05,y2:13}},{tagName:"line",groupSelector:"line",attrs:{x1:13.67,y1:12.41,x2:8.81,y2:8.33}},{tagName:"line",groupSelector:"line",attrs:{x1:5.09,y1:12.67,x2:9.66,y2:8.33}},{tagName:"line",groupSelector:"line",attrs:{x1:9.38,y1:8.83,x2:9.38,y2:4}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(0,0,23,22.47)}},{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(.33,17.19,23,22.47)}},{tagName:"line",groupSelector:"line",attrs:{x1:8.25,y1:26.1,x2:8,y2:35}},{tagName:"line",groupSelector:"line",attrs:{x1:7.92,y1:34.86,x2:14.29,y2:31.05}},{tagName:"line",groupSelector:"line",attrs:{x1:8.08,y1:26.23,x2:14,y2:31}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"}}},lo={shape:"3200",markup:[{tagName:"ellipse",groupSelector:"ellipse",attrs:{...d(12,0,16,16)}},{tagName:"line",groupSelector:"line",attrs:{x1:12,y1:8.33,x2:0,y2:8.33}}],attrs:{line:{stroke:"#000"},ellipse:{fill:"transparent",stroke:"#000"}}},co={shape:"3300",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:7,y1:4.83,x2:0,y2:4.83}},{tagName:"line",groupSelector:"line",attrs:{x1:36,y1:4.5,x2:13,y2:4.67}},{tagName:"rect",groupSelector:"rect",attrs:{...v(6.49,0,21.17,9.33)}},{tagName:"line",groupSelector:"line",attrs:{x1:12.33,y1:4.67,x2:18.33,y2:1.67}},{tagName:"line",groupSelector:"line",attrs:{x1:12.33,y1:4.67,x2:18.33,y2:7.33}}],attrs:{line:{stroke:"#000"},rect:{fill:"transparent",stroke:"#000"}}},po={shape:"3400",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:2.05,y1:4.87,x2:9,y2:20}},{tagName:"line",groupSelector:"line",attrs:{x1:0,y1:21.66,x2:4.33,y2:21.66}},{tagName:"line",groupSelector:"line",attrs:{x1:1.67,y1:0,x2:2,y2:5.67}},{tagName:"line",groupSelector:"line",attrs:{x1:1.67,y1:21,x2:1.67,y2:27}}],attrs:{line:{stroke:"#000",strokeWidth:1}}},uo={shape:"3401",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:2.38,y1:6.54,x2:4.67,y2:23}},{tagName:"line",groupSelector:"line",attrs:{x1:2,y1:28.67,x2:2.33,y2:22.67}},{tagName:"line",groupSelector:"line",attrs:{x1:0,y1:22.33,x2:4.33,y2:22.33}},{tagName:"line",groupSelector:"line",attrs:{x1:2,y1:0,x2:2.33,y2:6.67}}],attrs:{line:{stroke:"#000",strokeWidth:1}}},mo={shape:"3500",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:4.87,y1:6.95,x2:20,y2:0}},{tagName:"line",groupSelector:"line",attrs:{x1:21.66,y1:9,x2:21.66,y2:4.67}},{tagName:"line",groupSelector:"line",attrs:{x1:0,y1:7.33,x2:5.67,y2:7}},{tagName:"line",groupSelector:"line",attrs:{x1:21,y1:7.33,x2:27,y2:7.33}}],attrs:{line:{stroke:"#000"}}},go={shape:"3501",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:6.54,y1:2.29,x2:23,y2:0}},{tagName:"line",groupSelector:"line",attrs:{x1:28.67,y1:2.67,x2:22.67,y2:2.34}},{tagName:"line",groupSelector:"line",attrs:{x1:22.33,y1:4.67,x2:22.33,y2:.34}},{tagName:"line",groupSelector:"line",attrs:{x1:0,y1:2.67,x2:6.67,y2:2.34}}],attrs:{line:{stroke:"#000"}}},fo={shape:"3600",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:4.83,y1:29,x2:4.83,y2:36}},{tagName:"line",groupSelector:"line",attrs:{x1:4.5,y1:0,x2:4.67,y2:23}},{tagName:"rect",groupSelector:"rect",attrs:{...v(0,8.34,9.33,21.17)}},{tagName:"line",groupSelector:"line",attrs:{x1:4.67,y1:23.67,x2:1.67,y2:17.67}},{tagName:"line",groupSelector:"line",attrs:{x1:4.67,y1:23.67,x2:7.33,y2:17.67}}],attrs:{line:{stroke:"#000"},rect:{fill:"transparent",stroke:"#000"}}},ho={shape:"3700",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:6.95,y1:22.13,x2:0,y2:7}},{tagName:"line",groupSelector:"line",attrs:{x1:9,y1:5.34,x2:4.67,y2:5.34}},{tagName:"line",groupSelector:"line",attrs:{x1:7.33,y1:27,x2:7,y2:21.33}},{tagName:"line",groupSelector:"line",attrs:{x1:7.33,y1:6,x2:7.33,y2:0}}],attrs:{line:{stroke:"#000"}}},vo={shape:"3701",markup:[{tagName:"line",groupSelector:"line",attrs:{x1:2.29,y1:22.13,x2:0,y2:5.67}},{tagName:"line",groupSelector:"line",attrs:{x1:2.67,y1:0,x2:2.34,y2:6}},{tagName:"line",groupSelector:"line",attrs:{x1:4.67,y1:6.34,x2:.34,y2:6.34}},{tagName:"line",groupSelector:"line",attrs:{x1:2.67,y1:28.67,x2:2.34,y2:22}}],attrs:{line:{stroke:"#000"}}},u=t=>{const e=[];for(const a of t){const r=a.equipmentStatus?a.equipmentStatus:p.NONE,o=a.data;e.push({data:{value:o,descendantCells:[]},img:a.image,equipmentStatus:r})}return e},Co=new Map([["0A00",u([{data:yr,image:"create0A00",equipmentStatus:p.NONE}])],["0B00",u([{data:br,image:"create0B00",equipmentStatus:p.NONE}])],["0C00",u([{data:Dr,image:"create0C00",equipmentStatus:p.NONE}])],["0D00",u([{data:Pr,image:"create0D00",equipmentStatus:p.NONE}])],["0E00",u([{data:Fr,image:"create0E00",equipmentStatus:p.NONE}])],["0F00",u([{data:Tr,image:"create0F00",equipmentStatus:p.NONE}])],["1A00",u([{data:Er,image:"create1A00",equipmentStatus:p.NONE}])],["1B00",u([{data:Ar,image:"create1B00",equipmentStatus:p.NONE}])],["1C00",u([{data:Nr,image:"create1C00",equipmentStatus:p.NONE}])],["1D00",u([{data:Rr,image:"create1D00",equipmentStatus:p.NONE}])],["1E00",u([{data:wr,image:"create0E00",equipmentStatus:p.NONE}])],["1F00",u([{data:Ir,image:"create1F00",equipmentStatus:p.NONE}])],["0100",u([{data:kr,image:"create0100",equipmentStatus:p.OPEN},{data:Lr,image:"create0101",equipmentStatus:p.CLOSE}])],["0200",u([{data:zr,image:"create0200",equipmentStatus:p.OPEN},{data:Mr,image:"create0201",equipmentStatus:p.CLOSE}])],["0300",u([{data:Vr,image:"create0300",equipmentStatus:p.OPEN},{data:qr,image:"create0301",equipmentStatus:p.CLOSE}])],["0400",u([{data:Br,image:"create0400",equipmentStatus:p.OPEN},{data:Or,image:"create0401",equipmentStatus:p.CLOSE}])],["0500",u([{data:_r,image:"create0500",equipmentStatus:p.OPEN},{data:Gr,image:"create0501",equipmentStatus:p.CLOSE}])],["0600",u([{data:Ur,image:"create0600",equipmentStatus:p.OPEN},{data:Wr,image:"create0601",equipmentStatus:p.CLOSE}])],["0700",u([{data:jr,image:"create0700",equipmentStatus:p.OPEN},{data:Hr,image:"create0701",equipmentStatus:p.CLOSE}])],["0800",u([{data:$r,image:"create0800",equipmentStatus:p.OPEN},{data:Yr,image:"create0801",equipmentStatus:p.CLOSE}])],["0900",u([{data:Kr,image:"create0900",equipmentStatus:p.OPEN},{data:Jr,image:"create0901",equipmentStatus:p.CLOSE}])],["1000",u([{data:Xr,image:"create1000"}])],["1100",u([{data:Qr,image:"create1100"}])],["1200",u([{data:Zr,image:"create1200"}])],["1300",u([{data:eo,image:"create1300"}])],["1400",u([{data:to,image:"create1400"}])],["1500",u([{data:ao,image:"create1500"}])],["1600",u([{data:ro,image:"create1600"}])],["1700",u([{data:oo,image:"create1700"}])],["1800",u([{data:io,image:"create1800"}])],["1900",u([{data:no,image:"create1900"}])],["3000",u([{data:so,image:"create3000"}])],["3200",u([{data:lo,image:"create3200"}])],["3300",u([{data:co,image:"create3300"}])],["3400",u([{data:po,image:"create3400",equipmentStatus:p.OPEN},{data:uo,image:"create3401",equipmentStatus:p.CLOSE}])],["3500",u([{data:mo,image:"create3500",equipmentStatus:p.OPEN},{data:go,image:"create3501",equipmentStatus:p.CLOSE}])],["3600",u([{data:fo,image:"create3600"}])],["3700",u([{data:ho,image:"create3700",equipmentStatus:p.OPEN},{data:vo,image:"create3701",equipmentStatus:p.CLOSE}])]]);class Go{constructor(){m(this,"registered");m(this,"equipmentList");this.equipmentList=[],this.registered=!1}async loadEquipment(){if(this.registered)return;const e=G.global.t;console.log("loadEquipment");const a=Sr.data;for(const r of a){if(r.id.endsWith("01"))continue;const o={...r,components:[]};o.name=e(r.name),await this.setCfgData(o),this.equipmentList.push(o)}}async setCfgData(e){const a=Co.get(e.id);a&&(e.components=a)}registerNode(){if(!this.registered){for(const e of this.equipmentList)for(const a of e.components)K.unregisterNode(a.data.value.shape),K.registerNode(a.data.value.shape,{...a.data.value,width:30,height:30});this.registered=!0}}}const V=G.global.t;let Uo=class{createContextMenu(e){const a=W(e);if(!a||a.length==0)return[];const r=[];let o=!1;a.length>1&&(o=!0);let i=!1;if(a.length==1){for(const n of a)if(x(n.value)){i=!0;break}}return r.push({title:V("hmi.graph.contextMenu.group"),type:C.GROUP,enable:o}),r.push({title:V("hmi.graph.contextMenu.ungroup"),type:C.UNGROUP,enable:i}),r.push({title:V("hmi.graph.contextMenu.linkData"),type:C.EQUIPMENT_SADDR,enable:!0}),r}trigger(e,a,r){switch(e.type){case C.GROUP:r.group();break;case C.UNGROUP:r.ungroup();break}}};j(Re);class jo{createContextMenu(e){return e?[]:[]}trigger(e,a,r){const{t:o}=ae.useI18n();console.log(o("graph.messages.equipmentLoaded"),e,a,r)}}class Ho{setSaddrValue(e,a,r){let o;for(const i of e){if(o=a.yxMap.get(i.saddr),!o){console.log("短地址未找到配置项",i);continue}this.setRenderItemValue(i,o,r)}}setRenderItemValue(e,a,r){for(const o of a)o.show&&o.show.length>0?this.setCellRender(o,e,r):this.setCellText(o,e)}getShowValue(e,a){let r=a.value,o;try{o=Number(r)}catch{return r}if(e.equipmentConfig.factor&&(o=o*e.equipmentConfig.factor),e.equipmentConfig.format)try{return Ee.sprintf(e.equipmentConfig.format,o)}catch{}return o+""}setCellText(e,a){e.cell.attr("label/text",this.getShowValue(e,a))}setCbrDisRender(e,a,r){const o=this.getShowValue(e,a),i=e.show;let n,s;i[0].value==p.OPEN?(n=i[0].showValue,s=e.cell):(n=e.cell,s=i[0].showValue),o==r.open?(y(n,!0),y(s,!1)):(y(n,!1),y(s,!0))}setCellRender(e,a,r){const o=e.show;if(o.length==1&&o[0].isCbrDis){this.setCbrDisRender(e,a,r);return}const i=this.getShowValue(e,a),n=new Map;for(const l of o)if(n.set(l.value,l.showValue),typeof l.showValue=="object"){const c=l.showValue;c.isVisible()&&y(c,!1)}const s=n.get(i);if(!s){this.setCellText(e,a),e.cell.isVisible()||y(e.cell,!0);return}typeof s=="string"?(e.cell.attr("label/text",s),e.cell.isVisible()||y(e.cell,!0)):(e.cell.isVisible()&&y(e.cell,!1),y(s,!0))}}const $o=j(we);function Yo(t){const e={body:{fill:"#fff",strokeWidth:1},label:{fill:"#000"}},a=[{name:"node-editor",args:{attrs:{backgroundColor:"transparent"},getText:"label/text",setText:"label/text"}}];return{title:t("hmi.graph.basic.title"),components:[{title:t("hmi.graph.basic.components.line"),url:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAKNJREFUWEfN10sOgCAMBNBycvXmSiKJQYG2zBTZdNlHP4smWfzS4vzyO8B+V6REeoHqCuTEm4gcV+YQxFcLQhGtGQhD9IYwBDHaAjpiBMhbQEVoAFSEFkBDWAAUhBUAR3gAUIQXAEPMACCIWcA0AgGYQqAAbgQS4EKgAWYEA2BCsABqBBOgQrABQ0QEoIuIAjQRkYCCeMb/nWb0U6xOEN2C1wdPom0yIdc/clQAAAAASUVORK5CYII=",import.meta.url).href,data:{shape:"path",width:100,height:10,tooltip:t("hmi.graph.basic.components.line"),data:{isLine:!0},attrs:{...e,body:{fill:"#f00",strokeWidth:2,ref:"bg",refY:"50%",refDKeepOffset:"M 0,0 H 100"},label:{fill:"#000"}}}},{title:t("hmi.graph.basic.components.text"),url:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAxdJREFUWEftllmoTlEUx3/XkBCKMss8v5gyJREPHiRFkuQakgcl8xAPMg+ZUlKGTIUSQsSDqUSmkojMc0hKhBL2/7bO17mn/Z19fH11PdxVX52+vfbe/73Wf631L6GCraSC76cSgC8CD4EOwDZguqWoC/AD+AZ0Bi4C/YGXUBbF5sBV9/8g4D7wIZHa3cAk4DHQPr7mA/AMaAW8B+663yhgNXATeA7MAUYCB4EDQFVgNFAKHAfWuP/bAT2Aue4RH4E6dukbA5vD4AOwwyEdD8wA9F0Mm+YOWQqccVGbHIpAI+Cye2HHDDcPAP4AVzL47gWWAU9CALTeAPiU4dBb5tMzg6/SohT+CgFoaDmfEji0D3DNfPR9PeCvCMxOPszHgdrAEEeiExkOnGA++4yEaVtE3LPA91AEahiA0ymnKUVitHxlPx1hmwXSNgy4lAVAU0uByiqfzTdCRQDktwBYl7Jnp+1R78hZIa1Ye14AFwCBHWqnvbL+8TsDIVMBhEg4HDgJ9DUAR2MXjrA1H4btFoG3oQiESKhmIpAqvSrGhcZ2qEimXPtMwNXCv4YA1LKw+qqghYVf3WyPHbQYWGHfakrq9eWaja1lBlDfsXqRGzzzPM9YD8x0c0GHifkyVcSRmO9GmxfJ7auATTYbCiKhGK8pVzdAsi+WoghgqruvCppYGU5M7FTYd7lm0i/WASMXjeb4PJjqpp7KLm4abJoFqpbUCFQDenkuuQdUN63ge9VToLUtSBN0TTj1Bm5nmQUKsV6wwQ6o6UTGGCOdRvTWPDEVb5TnyBSxQ7HOJ+5oHnwORUCkWhgj4WDgvG1a6V6wJA+ALaYh4ssqSZWmLDMJFeaWJp8CfPun5TaA0lTOfCRsa6+UhiumPXKRHAi8C6XgsDmq5lXTxTBpw+XAMWBcCIBUi1IgUy2r+202mfbahKYG0Clgf0yUSpieA9ZapXS3hibxKiUtqaezo0opu8CXAsksKVpdOsuAqKSkbiUmOgE3nALulpDldwCVmkpQKrge8ACQbpS8H2uqSeopZ4WM42KkpBLA/xOBv1PjliEeaF86AAAAAElFTkSuQmCC",import.meta.url).href,data:{shape:"text-block",width:100,height:30,tooltip:t("hmi.graph.basic.components.text"),attrs:{body:{fill:"none",stroke:"none"},label:{class:"graph-text-block"}},text:t("hmi.graph.basic.components.text"),tools:a}},{title:t("hmi.graph.basic.components.rectangle"),url:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAHtJREFUWEftljsSwCAIRPFoOVlysxwtGZ18SNRxLIDm0VHIrg+KTRJcKVhfMKAJrNc6elQOkQ+xfz+7zS0/0GJ5oGcV7ZaBxdjFrkm3DFgf5k26SwADEIAABCAAAQhAIJxAeCQzjoTP+CqSlZjsWFUsd9R+pawPbvgpDJxkgC4h9DMqowAAAABJRU5ErkJggg==",import.meta.url).href,data:{shape:"rect",width:100,height:50,tooltip:t("hmi.graph.basic.components.rectangle"),tools:a,attrs:{...e,label:{fill:"#000"}}}},{title:t("hmi.graph.basic.components.circle"),url:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAa1JREFUWEft1j9IVlEYx/GPi7PgpA5uuYWI0BY22KJg1ODklNDS4KCTgkU02dAiOOjk1NAfEYNqMFoiEB2kRWwIApsKGx3Ce+BcuF7et3sv+XIWLxxeeO9znt/3Pn/Oc7okfroS67sCaBqBHoxhuE3qPiOsP3VTWxdgGjMYR3eF8zN8wCZeVIFUAdzGU4xGR78ykG84xhG+4i+uxTUUf3uj/R4W8b4dyL8A5rESN37H87iqPiq8n4trMBov4Fmrje0AHmEZIZy58Ekd5YJNXwEkpO1x9i74vfC0AsjFf2IC+w2Fy+Yj2EZ/K4gywA18ioU2iZ3/FM+3h1p6FyN6E1/yF2WAN5jCQ6xeknju5gHWsIU7rQBCf+/iFe5dsnju7nUUv5WdJx/Dn8UIrOM+lmLrdYLhSfS/gdkywEE84ULhve2EOu7iZRbhwyzS18sAv7OWC0dtqNamLVeXdyD78h84jVoXUpAcIHkKkhdh8jYMhZT0IAoAyY/iAJF0GOX93IlxHHyGkVw5jnODpBeS4hhNdiUrhirZpbScsmTX8rrDprFd1bW8scOmG64AzgEFDXIhxQQs9wAAAABJRU5ErkJggg==",import.meta.url).href,data:{shape:"circle",width:50,height:50,tooltip:t("hmi.graph.basic.components.circle"),tools:a,attrs:{...e,label:{fill:"#000"}}}},{title:t("hmi.graph.basic.components.ellipse"),url:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAoNJREFUWEftlkuojVEUx393aK4MEMVAXqUMCAOUoZJHEUUKKaGoKwNMEFcemXiEUp55jWR0b8jERLeL5EZCFIM7uMbs/2nt26Z9vr33d746E6tOp+98e6/1P2ut/3+tHrpsPV2Oz38ATWVgCbAcWAFMBiYBX4DPwE3gMTAcK3cnAGYC64A1wKyMXroIbP/3XF0Ae4DTgTP929vAA+CDC/TN3nmQh+y5D9gfgqgD4AKwzZz0W2AFH6nIgoC8tvcbrCytx1IATwHVW7YbOJeRen9kM3AVeB74KALwAxgPfAI2uaZ7VhDcH1WpJlrPvCnJwEdgKvAQWFUjsL9yGFA/rAdu5QK44+q31iG/FNS+LgYP4ABwPAeAb7hXwLy6UYN7x4BeYJcr5/kUgJBqM1za3jUA4LoTpI2mHfeqAIS02QpcaSC4XLwE5uc0oa+VeL6soeDqI/WT9GC299lOB4YM5Q5AfdCEPQJWAmeAvVUANFCe2DCZk1C4XGCLTIB+AQtcD+gPtiyWgZPAPuCUfecGqTp3w7h/FDgYHowBeOEysNDkUrLZqSnlkm2xaDHwMwVAM1zzXJ+vHUYX3/28iLIploHfFeUpwTMdeG8X7gOrY5djAHwGSidl6H8C8N1+eOvoJ12JWlUPaMtpTaxC8yyqavQxlzEAvm5H3CkJUomJPWKRbABYmrocAxDWLjcL4rn2Pe0Jsru2L6bit11IwrVri9sFrrXxJHnVcJHCyUS1EyWzo6rRvCDJseh42YKMc6vVFGCaDRb9LIU7a4vqXzxPpSDV6dpcdpqAxHxp19fOr5V7TF5TQcP3KQD+rGg019ayUVu7xfHBkmCxs7kAOo3T9n7XAfwBk0FwIYMlweYAAAAASUVORK5CYII=",import.meta.url).href,data:{shape:"ellipse",width:100,height:50,tooltip:t("hmi.graph.basic.components.ellipse"),tools:a,attrs:{...e,label:{fill:"#000"}}}},{title:t("hmi.graph.basic.components.triangle"),url:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAzRJREFUWEftlm2IFVUcxp/nrK1fIrE+BAYi2Mu9M1s4M2A756IR6EZiGVZkUBEIEYqBiR8MEUJIiYoUQyIogtTe3/2iQhs5Z1jYORfZvTMtaIqIFEiCHyTIe/81K8K6e+/uvbO6+8XzbTj///P85pnzMsQsD86yP24BTCsBx698CTRuT228quinLAxQDsKnKPwuNxZidZaYw0UgCgM4vpaxhqk1hbQKNTmB3gvBJoF8S6g6IM8KuTNLoh2dptAxQGnp0rvUlTkXcqMG5SF1RTXQJcP5c9e/6s6hoeMXO4HoGMDxtQXggdydJtG23MzxwjdB7qDgaK1q+m4agOvpZ4T4CsAfl+/ods/09/+Tm7mu2y1z550AUAK5LE2i4+1CdJSA4+u/AcyHyPq0Gn881qTsV54j5HMAl1Jr5t1wANfXHwiwQYAjmTWPNTMo++E3BNcS3FSz0b52INpKoKfn4bsb3V1/XhXk8tRGvzUFCHp9ikryubrwnpFqdH4qiLYAXE8fEWIlBftrVbNhMlE30DtFsB3AgdSaF6YNUPb0OhKHAJxRt9V7hwcG/ppMdHSb1ucYCO4Xxb5sMDo6Wf2UCTiBHsnFKNhSq5r3pnqjfL7shS+R/BTAydSa+woDOF7lLVC2ATyW2mhlO+bXahxPfw9ijUA2ZzZ+v1VvywQe8CoLuiinAXQryBPDNv65E4CSFz6iyP68RxH3DifmVLP+lgBlTx8k8TzAD1MbvdqJ+bVa1w/fFnCrAB9l1rzSNoDr6aeF+BrA2bpi38hgNFIEoLSkd5FS6hiAxRSuqVWjH8frNE2g7OuIgAZla5rE7xQxH5PCRgH3CWAya5bld9hYvQkArqdfF+JdAL+k1qwY31AAho4fHgb4OChvpEm8qyWAE1QWQiQGsEAgazMbj/7xTHf0+OHqBvgTgXpDYUk2aEav73xcl4AT6D0QvDbZoikK4wThfgjzxfxZas2LEwDKnl5O4lcQ5xqQVb8n8VBRs2Z9pSB8UAnzrbxQwHWZjb64LgHH1z8AePJGmrbW4kBqo97xAJ/8f96/PBMAItKfVeNHJ6yBmTBv6xyYSZApb8ObDTPrAP8BcYoaMPu/g44AAAAASUVORK5CYII=",import.meta.url).href,data:{shape:"polygon",width:50,height:50,tooltip:t("hmi.graph.basic.components.triangle"),tools:a,points:"0.5,0.05 1,0.95 0,0.95",attrs:{...e,label:{fill:"#000"}}}},{title:t("hmi.graph.basic.components.arc"),url:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAjFJREFUWEftVb+LE0EUfm+2CYeS8mrDzkyCIEIKLa5QvEYLFcHCn5WCzenZ2Cr+A55aCVb+BAuRK7zGw+YKRQISkGTehGB7ZRSv23kyx65sfmyyCYEg7HQ7773v+9733rAICz64YH4oBBQO/H8O1Ov1pV6vtyKEWGbmZf+KEHHXObdbLpd3Go3G3jQvK7cDSqkbAHAaAC5MIHgPAFtE9DyPkIkCtNZnmfk2AJzKA5jK2UbEJ8aYzXF1YwUopR4AwP0xABTHVFYOIj4zxtzKjGcFpJRrvoOB+CcA+OCc+xEEQcMY89vHtdYHoyiqCyEOA8B5AFhN1zHzprX23CiukQ5orS8y87t0ASLeNcZs5BmD1nqdmR8N5N4ctRdDAqSUxxDxS7o4CALVarVsHvIkp1arySiKkhHtXzPzcWvt177GBkGVUm8A4FLqfpWItqchT3KVUn5x/diS85aILmcKUEr5Z/YxlfCSiK7PQp4S8QIArqUwzhDRVvLdNwKllF+6tTjYRMSVZNFmFeEXlJl3AOBIjPGUiPyz3j+DAvycw3hej62167MSp+uklBuIeCe+6xCRHBIQK/2VBJxzVzudzut5CAjD8IoQ4lWCVSqVDjSbzT99DlSrVeWcM3H3e1EUHe12u1NtfpbYSqUigyD4johLPkcIcajdbv/sExCG4QkhxENmjnzAWntyHt0nGFLKz/9sR7xHRN+GdmCehHmxJv6M8gLNmlcIKBwoHCgcWLgDfwEX7bsh4h7rJAAAAABJRU5ErkJggg==",import.meta.url).href,data:{shape:"path",width:50,height:25,tooltip:t("hmi.graph.basic.components.arc"),tools:a,attrs:{body:{refDResetOffset:"M 0,25 A 50 25 0 0 1 50 25"},label:{fill:"#000"}}}}]}}function Ko({value:t,fromBase:e,toBase:a}){const r="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ+/".split(""),o=r.slice(0,e),i=r.slice(0,a);let n=t.split("").reverse().reduce((l,c,g)=>{if(!o.includes(c))throw new Error(`Invalid digit "${c}" for base ${e}.`);return l+=BigInt(o.indexOf(c))*BigInt(e)**BigInt(g)},0n),s="";for(;n>0;)s=i[Number(n%BigInt(a))]+s,n=(n-n%BigInt(a))/BigInt(a);return s||"0"}function _(t){return typeof t!="object"||t===null?t:Array.isArray(t)?t.map(_):Object.keys(t).sort((e,a)=>e.localeCompare(a)).reduce((e,a)=>(e[a]=_(t[a]),e),{})}function Jo({rawJson:t,sortKeys:e=!0,indentSize:a=3}){const r=Ae.parse(M(t));return JSON.stringify(M(e)?_(r):r,null,M(a))}const Xo=t=>t+273.15,Qo=t=>t-273.15,Zo=t=>(t+459.67)*(5/9),ei=t=>t*(9/5)-459.67,ti=t=>t*(5/9),ai=t=>t*(9/5),ri=t=>373.15-2/3*t,oi=t=>3/2*(373.15-t),ii=t=>t*(100/33)+273.15,ni=t=>(t-273.15)*(33/100),si=t=>t*(5/4)+273.15,li=t=>(t-273.15)*(4/5),ci=t=>(t-7.5)*(40/21)+273.15,di=t=>(t-273.15)*(21/40)+7.5;function le(t){return t.trim()}function pi(t,e){return Ie(()=>te(le(t),e)??"","")}function ui(t){const e=le(t);if(e==="")return!0;try{return te(e),!0}catch{return!1}}export{Xo as $,or as A,jo as B,C,Za as D,q as E,Vo as F,zo as G,Ho as H,mr as I,$o as J,_o as K,Ko as L,Jo as M,di as N,ci as O,li as P,si as Q,ni as R,ir as S,ii as T,oi as U,ri as V,ai as W,ti as X,ei as Y,Zo as Z,Qo as _,nr as a,ui as a0,pi as a1,G as a2,Fo as b,B as c,x as d,se as e,N as f,Ao as g,Yo as h,Eo as i,p as j,Mo as k,sr as l,Lo as m,Bo as n,W as o,X as p,No as q,Z as r,ko as s,qo as t,wo as u,Uo as v,Go as w,Ro as x,Io as y,To as z};
