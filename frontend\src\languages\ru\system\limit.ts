export default {
  limit: {
    module: {
      title: "Название модуля",
      icon: "Иконка",
      status: "Статус",
      sort: "Сортировка",
      description: "Описание",
      createTime: "Время создания",
      operation: "Операция",
      add: "Добавить модуль",
      edit: "Редактировать модуль",
      delete: "Удалить модуль",
      deleteConfirm: "Удалить выбранный модуль",
      deleteConfirmWithName: "Удалить модуль 【{name}】",
      form: {
        title: "Пожалуйста, введите название модуля",
        status: "Пожалуйста, выберите статус",
        sort: "Пожалуйста, введите сортировку",
        icon: "Пожалуйста, выберите иконку"
      }
    },
    menu: {
      title: "Название меню",
      icon: "Иконка меню",
      type: "Тип меню",
      component: "Название компонента",
      path: "Адрес маршрута",
      componentPath: "Путь компонента",
      sort: "Сортировка",
      status: "Статус",
      description: "Описание",
      operation: "Операция",
      add: "Добавить меню",
      edit: "Редактировать меню",
      delete: "Удалить меню",
      deleteConfirm: "Удалить выбранное меню",
      deleteConfirmWithName: "Удалить меню 【{name}】",
      form: {
        title: "Пожалуйста, введите название меню",
        parent: "Пожалуйста, выберите родительское меню",
        type: "Пожалуйста, выберите тип меню",
        path: "Пожалуйста, введите адрес маршрута",
        component: "Пожалуйста, введите адрес компонента",
        sort: "Пожалуйста, введите сортировку",
        icon: "Пожалуйста, выберите иконку",
        status: "Пожалуйста, выберите статус",
        link: "Пожалуйста, введите адрес ссылки"
      }
    },
    button: {
      title: "Название кнопки",
      code: "Код кнопки",
      sort: "Сортировка",
      description: "Описание",
      operation: "Операция",
      add: "Добавить кнопку",
      edit: "Редактировать кнопку",
      delete: "Удалить кнопку",
      deleteConfirm: "Удалить выбранную кнопку",
      deleteConfirmWithName: "Удалить кнопку 【{name}】",
      batch: {
        title: "Пакетное добавление кнопок",
        shortName: "Краткое название разрешения",
        codePrefix: "Префикс кода",
        form: {
          shortName: "Пожалуйста, введите краткое название разрешения",
          codePrefix: "Пожалуйста, введите префикс кода"
        }
      },
      form: {
        title: "Пожалуйста, введите название кнопки",
        code: "Пожалуйста, введите код кнопки",
        sort: "Пожалуйста, введите сортировку"
      }
    },
    role: {
      title: "Название роли",
      org: "Принадлежащая организация",
      category: "Тип роли",
      status: "Статус",
      sort: "Сортировка",
      description: "Описание",
      createTime: "Время создания",
      operation: "Операция",
      add: "Добавить роль",
      edit: "Редактировать роль",
      delete: "Удалить роль",
      deleteConfirm: "Удалить выбранную роль",
      deleteConfirmWithName: "Удалить роль 【{name}】",
      grant: {
        resource: "Авторизованные ресурсы",
        permission: "Авторизованные разрешения",
        dataScope: "Область данных"
      },
      form: {
        title: "Пожалуйста, введите название роли",
        org: "Пожалуйста, выберите принадлежащую организацию",
        category: "Пожалуйста, выберите тип роли",
        status: "Пожалуйста, выберите статус"
      }
    },
    spa: {
      title: "Название SPA",
      icon: "Иконка",
      type: "Тип SPA",
      path: "Адрес маршрута",
      component: "Путь компонента",
      sort: "Сортировка",
      description: "Описание",
      createTime: "Время создания",
      operation: "Операция",
      add: "Добавить SPA",
      edit: "Редактировать SPA",
      delete: "Удалить SPA",
      deleteConfirm: "Удалить выбранное SPA",
      deleteConfirmWithName: "Удалить SPA 【{name}】",
      form: {
        title: "Введите название SPA",
        type: "Выберите тип SPA",
        path: "Введите адрес маршрута",
        component: "Введите адрес компонента",
        sort: "Введите сортировку",
        icon: "Выберите иконку",
        link: "Заполните адрес ссылки, например: http://www.baidu.com"
      }
    }
  }
};
