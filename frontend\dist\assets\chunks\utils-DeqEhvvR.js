const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["../vendor/vue-vendor-EiOvILk3.js","../vendor/utils-vendor-C_ezCHls.js","../vendor/highlight-vendor-DzhJ4giz.js","../vendor/vendor-P-ltm-Yc.js","../styles/vendor-CaccPJo9.css","../styles/highlight-vendor-ZgkIHsf0.css","./element-core-DyPKvxS2.js","./element-icons-DbNYNmpr.js","../styles/element-core-BlK98vAp.css","../vendor/i18n-vendor-BPpKJ4WV.js","./element-components-CkwEs5_B.js","./components-BkHQV7Qm.js","../vendor/charts-vendor-DS7xuoj-.js","./views-biz-DnwQrgNu.js","../vendor/icons-vendor-DBJjj0eo.js","../styles/vue-vendor-DNbd9zSU.css"])))=>i.map(i=>d[i]);
var it=Object.defineProperty;var st=(e,t,r)=>t in e?it(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var v=(e,t,r)=>st(e,typeof t!="symbol"?t+"":t,r);import{d as w,s as nt,c as at,a as ct,b as ut,S as lt}from"../vendor/vue-vendor-EiOvILk3.js";import{a as h,b as U,c as dt}from"./element-components-CkwEs5_B.js";import{W as pe,X as mt,Y as pt,_ as ee}from"../vendor/utils-vendor-C_ezCHls.js";import{cL as ht,cM as X,bT as gt,bh as Ge,c7 as vt,bl as Ee,cp as V,cN as _t,cO as ft,cP as Et}from"../vendor/vendor-P-ltm-Yc.js";import{s as St,t as Se}from"./components-BkHQV7Qm.js";import{Message as bt}from"./element-icons-DbNYNmpr.js";import{a as x,b as Tt,e as Dt,c as Lt,d as It,f as wt,l as At}from"../vendor/icons-vendor-DBJjj0eo.js";const Rt="modulepreload",yt=function(e,t){return new URL(e,t).href},be={},s=function(t,r,o){let i=Promise.resolve();if(r&&r.length>0){const c=document.getElementsByTagName("link"),a=document.querySelector("meta[property=csp-nonce]"),u=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));i=Promise.allSettled(r.map(l=>{if(l=yt(l,o),l in be)return;be[l]=!0;const S=l.endsWith(".css"),k=S?'[rel="stylesheet"]':"";if(!!o)for(let g=c.length-1;g>=0;g--){const b=c[g];if(b.href===l&&(!S||b.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${l}"]${k}`))return;const m=document.createElement("link");if(m.rel=S?"stylesheet":Rt,S||(m.as="script"),m.crossOrigin="",m.href=l,u&&m.setAttribute("nonce",u),document.head.appendChild(m),S)return new Promise((g,b)=>{m.addEventListener("load",g),m.addEventListener("error",()=>b(new Error(`Unable to preload CSS for ${l}`)))})}))}function n(c){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=c,window.dispatchEvent(a),!a.defaultPrevented)throw c}return i.then(c=>{for(const a of c||[])a.status==="rejected"&&n(a.reason);return t().catch(n)})},se="/biz/debug/device/index",q="/activate",He="/login",he="#124198",Pt=["/500"],Ct=e=>{const{key:t,paths:r,storage:o=localStorage,beforeRestore:i,afterRestore:n,serializer:c,debug:a=!1}=e;return{key:t,storage:o,paths:r,serializer:c||{serialize:u=>{try{const l=JSON.stringify(u,null,a?2:0);return a&&console.log(`[EnhancedPersist] 序列化 ${t}:`,u),l}catch(l){return console.error(`[EnhancedPersist] 序列化失败 ${t}:`,l),"{}"}},deserialize:u=>{try{const l=JSON.parse(u);return a&&console.log(`[EnhancedPersist] 反序列化 ${t}:`,l),l}catch(l){return console.error(`[EnhancedPersist] 反序列化失败 ${t}:`,l),{}}}},beforeRestore:u=>{a&&console.log(`[EnhancedPersist] 开始恢复 ${t}`),i==null||i(u)},afterRestore:u=>{a&&console.log(`[EnhancedPersist] 恢复完成 ${t}`),n==null||n(u),Ot(u.store,t,a)}}},Ot=(e,t,r)=>{try{t==="simple-global"?Mt(e,r):t==="simple-auth"&&kt(e,r)}catch(o){console.error(`[EnhancedPersist] 状态验证失败 ${t}:`,o)}},Mt=(e,t)=>{const r={layout:"columns",language:"zh",assemblySize:"default",maximize:!1,isDark:!1,isGrey:!1,isConsole:!0,consoleHeight:154,isDeviceList:!0,isFunctionList:!0,isWeak:!1,breadcrumb:!1,breadcrumbIcon:!1,tabs:!1,tabsIcon:!1,footer:!0,drawerForm:!1,watermark:!0};let o=!1;for(const[i,n]of Object.entries(r))(e[i]===void 0||e[i]===null)&&(e[i]=n,o=!0,t&&console.log(`[EnhancedPersist] 修复全局状态字段 ${i}:`,n));o&&t&&console.log("[EnhancedPersist] 全局状态已修复")},kt=(e,t)=>{e.authMenuList||(e.authMenuList=[]),e.authButtonList||(e.authButtonList=[]),t&&console.log("[EnhancedPersist] 认证状态已验证")};class Vt{constructor(t=localStorage,r=sessionStorage){v(this,"primaryStorage");v(this,"backupStorage");this.primaryStorage=t,this.backupStorage=r}getItem(t){try{let r=this.primaryStorage.getItem(t);return r?(this.backupStorage.setItem(t,r),r):(r=this.backupStorage.getItem(t),r?(this.primaryStorage.setItem(t,r),console.log(`[MultiBackupStorage] 从备份存储恢复数据: ${t}`),r):null)}catch(r){return console.error(`[MultiBackupStorage] 获取数据失败: ${t}`,r),null}}setItem(t,r){try{this.primaryStorage.setItem(t,r),this.backupStorage.setItem(t,r)}catch(o){console.error(`[MultiBackupStorage] 保存数据失败: ${t}`,o);try{this.backupStorage.setItem(t,r)}catch(i){console.error(`[MultiBackupStorage] 备份存储也失败: ${t}`,i)}}}removeItem(t){try{this.primaryStorage.removeItem(t),this.backupStorage.removeItem(t)}catch(r){console.error(`[MultiBackupStorage] 删除数据失败: ${t}`,r)}}clear(){try{this.primaryStorage.clear(),this.backupStorage.clear()}catch(t){console.error("[MultiBackupStorage] 清空存储失败",t)}}get length(){try{return this.primaryStorage.length}catch(t){return console.error("[MultiBackupStorage] 获取存储长度失败",t),0}}key(t){try{return this.primaryStorage.key(t)}catch(r){return console.error("[MultiBackupStorage] 获取存储键失败",r),null}}}const xt=(e,t)=>{const r=new Vt;return Ct({key:e,paths:t,storage:r,debug:!1,beforeRestore:()=>{console.log(`[MultiBackupPersist] 开始恢复多重备份数据: ${e}`)},afterRestore:()=>{console.log(`[MultiBackupPersist] 多重备份数据恢复完成: ${e}`)}})},Te="simple-global",$t=()=>localStorage.getItem("language")||"zh",ve=w(Te,{state:()=>({layout:"columns",assemblySize:"default",language:$t(),maximize:!1,primary:he,isDark:!1,isGrey:!1,isConsole:!0,consoleHeight:154,isDeviceList:!0,isFunctionList:!0,isWeak:!1,breadcrumb:!1,breadcrumbIcon:!1,tabs:!1,tabsIcon:!1,footer:!0,drawerForm:!1,watermark:!0}),getters:{},actions:{setGlobalState(...e){this.$patch({[e[0]]:e[1]}),e[0]==="language"&&localStorage.setItem("language",e[1])},checkColumnLayout(){return this.layout==="columns"}},persist:xt(Te,["layout","language","assemblySize","maximize","isDark","isGrey","isConsole","consoleHeight","isDeviceList","isFunctionList","isWeak","breadcrumb","breadcrumbIcon","tabs","tabsIcon","footer","drawerForm","watermark","primary"])});/**
 * @description  颜色相关工具函数
 * @license Apache License Version 2.0
 */function Ue(e){let t="";if(!/^\#?[0-9A-Fa-f]{6}$/.test(e))return h.warning("输入错误的hex");e=e.replace("#",""),t=e.match(/../g);for(let o=0;o<3;o++)t[o]=parseInt(t[o],16);return t}function We(e,t,r){let o=/^\d{1,3}$/;if(!o.test(e)||!o.test(t)||!o.test(r))return h.warning("输入错误的rgb颜色值");let i=[e.toString(16),t.toString(16),r.toString(16)];for(let n=0;n<3;n++)i[n].length==1&&(i[n]=`0${i[n]}`);return`#${i.join("")}`}function De(e,t){if(!/^\#?[0-9A-Fa-f]{6}$/.test(e))return h.warning("输入错误的hex颜色值");let o=Ue(e);for(let i=0;i<3;i++)o[i]=Math.round(20.5*t+o[i]*(1-t));return We(o[0],o[1],o[2])}function Le(e,t){if(!/^\#?[0-9A-Fa-f]{6}$/.test(e))return h.warning("输入错误的hex颜色值");let o=Ue(e);for(let i=0;i<3;i++)o[i]=Math.round(255*t+o[i]*(1-t));return We(o[0],o[1],o[2])}const zt={light:{"--el-menu-bg-color":"#ffffff","--el-menu-hover-bg-color":"var(--bl-hover-bg-color)","--el-menu-active-bg-color":"var(--el-color-primary-light-9)","--el-menu-text-color":"#333333","--el-menu-active-color":"var(--el-color-primary)","--el-menu-hover-text-color":"#333333","--el-menu-horizontal-sub-item-height":"50px"},inverted:{"--el-menu-bg-color":"#191a20","--el-menu-hover-bg-color":"var(--bl-hover-bg-color)","--el-menu-active-bg-color":"#000000","--el-menu-text-color":"#bdbdc0","--el-menu-active-color":"#ffffff","--el-menu-hover-text-color":"#ffffff","--el-menu-horizontal-sub-item-height":"50px"},dark:{"--el-menu-bg-color":"#141414","--el-menu-hover-bg-color":"var(--bl-hover-bg-color)","--el-menu-active-bg-color":"#000000","--el-menu-text-color":"#bdbdc0","--el-menu-active-color":"#ffffff","--el-menu-hover-text-color":"#ffffff","--el-menu-horizontal-sub-item-height":"50px"}},Bt={light:{"--el-aside-logo-text-color":"#303133","--el-aside-border-color":"#e4e7ed"},inverted:{"--el-aside-logo-text-color":"#dadada","--el-aside-border-color":"#414243"},dark:{"--el-aside-logo-text-color":"#dadada","--el-aside-border-color":"#414243"}},Nt={light:{"--el-header-logo-text-color":"#303133","--el-header-bg-color":"#ffffff","--el-header-text-color":"#303133","--el-header-text-color-regular":"#606266","--el-header-border-color":"#e4e7ed"},inverted:{"--el-header-logo-text-color":"#dadada","--el-header-bg-color":"#191a20","--el-header-text-color":"#e5eaf3","--el-header-text-color-regular":"#cfd3dc","--el-header-border-color":"#414243"},dark:{"--el-header-logo-text-color":"#dadada","--el-header-bg-color":"#141414","--el-header-text-color":"#e5eaf3","--el-header-text-color-regular":"#cfd3dc","--el-header-border-color":"#414243"}};/**
 * @description  全局主题 hooks
 * @license Apache License Version 2.0
 */const Io=()=>{const e=ve(),{primary:t,isDark:r,isGrey:o,isWeak:i}=nt(e),n=()=>{const d=document.documentElement;r.value?d.setAttribute("class","dark"):d.setAttribute("class",""),c(t.value),l(),S()},c=d=>{d||(d=he,h({type:"success",message:`主题颜色已重置为 ${he}`})),document.documentElement.style.setProperty("--el-color-primary",d),document.documentElement.style.setProperty("--el-color-primary-dark-2",r.value?`${Le(d,.2)}`:`${De(d,.3)}`);for(let m=1;m<=9;m++){const g=r.value?`${De(d,m/10)}`:`${Le(d,m/10)}`;document.documentElement.style.setProperty(`--el-color-primary-light-${m}`,g)}e.setGlobalState("primary",d)},a=(d,m)=>{const g=document.body;if(!m)return g.removeAttribute("style");const b={grey:"filter: grayscale(1)",weak:"filter: invert(80%)"};g.setAttribute("style",b[d]);const p=d==="grey"?"isWeak":"isGrey";e.setGlobalState(p,!1)},u=()=>{let d="light";r.value&&(d="dark");const m=zt[d];for(const[g,b]of Object.entries(m))document.documentElement.style.setProperty(g,b)},l=()=>{let d="light";r.value&&(d="dark");const m=Bt[d];for(const[g,b]of Object.entries(m))document.documentElement.style.setProperty(g,b);u()},S=()=>{let d="light";r.value&&(d="dark");const m=Nt[d];for(const[g,b]of Object.entries(m))document.documentElement.style.setProperty(g,b);u()};return{initTheme:()=>{n(),o.value&&a("grey",!0),i.value&&a("weak",!0)},switchDark:n,changePrimary:c,changeGreyOrWeak:a,setAsideTheme:l,setHeaderTheme:S}};/**
 * @description http请求枚举
 * @license Apache License Version 2.0
 */var ne=(e=>(e[e.SUCCESS=200]="SUCCESS",e[e.ERROR=500]="ERROR",e[e.OVERDUE=401]="OVERDUE",e[e.TIMEOUT=3e4]="TIMEOUT",e.TYPE="success",e))(ne||{}),C=(e=>(e.ACCESS_TOKEN_KEY="access-token",e.REFRESH_TOKEN_KEY="x-access-token",e.TOKEN_NAME="Authorization",e.TOKEN_PREFIX="Bearer ",e))(C||{});/**
 * @description 系统配置枚举
 * @license Apache License Version 2.0
 */var Ft=(e=>(e.LOGIN_CAPTCHA_OPEN="LOGIN_CAPTCHA_OPEN",e.SYS_DEFAULT_WORKBENCH_DATA="SYS_DEFAULT_WORKBENCH_DATA",e.SYS_FOOTER_LINKS="SYS_FOOTER_LINKS",e))(Ft||{});/**
 * @description 字典枚举
 * @license Apache License Version 2.0
 */var Gt=(e=>(e.MENU_TYPE="MENU_TYPE",e.GENDER="GENDER",e.COMMON_STATUS="COMMON_STATUS",e.ROLE_CATEGORY="ROLE_CATEGORY",e.ORG_CATEGORY="ORG_CATEGORY",e.POSITION_CATEGORY="POSITION_CATEGORY",e.NATION="NATION",e.AUTH_DEVICE_TYPE="AUTH_DEVICE_TYPE",e.DICT_CATEGORY="DICT_CATEGORY",e.FILE_ENGINE="FILE_ENGINE",e.COMMON_SWITCH="COMMON_SWITCH",e.IDCARD_TYPE="IDCARD_TYPE",e.CULTURE_LEVEL="CULTURE_LEVEL",e.MESSAGE_CATEGORY="MESSAGE_CATEGORY",e.RECEIVER_TYPE="RECEIVER_TYPE",e.MESSAGE_WAY="MESSAGE_WAY",e.MESSAGE_STATUS="MESSAGE_STATUS",e.ONLINE_STATUS="ONLINE_STATUS",e.YES_NO="YES_NO",e.TENANT_OPTIONS="TENANT_OPTIONS",e.CAPTCHA_TYPE="CAPTCHA_TYPE",e))(Gt||{}),Ht=(e=>(e.CATALOG="CATALOG",e.MENU="MENU",e.BUTTON="BUTTON",e.SUBSET="SUBSET",e.LINK="LINK",e))(Ht||{}),_e=(e=>(e.ENABLE="ENABLE",e.DISABLE="DISABLED",e))(_e||{}),Ut=(e=>(e.GLOBAL="GLOBAL",e.ORG="ORG",e))(Ut||{}),Wt=(e=>(e.CHOSE="CHOSE",e.CLOSE="CLOSE",e.DOMAIN="DOMAIN",e))(Wt||{});/**
 * @description 表单枚举
 * @license Apache License Version 2.0
 */var Yt=(e=>(e.ADD="新增",e.EDIT="编辑",e.VIEW="查看",e.DELETE="删除",e))(Yt||{});/**
 * @description 窗口枚举
 * @license Apache License Version 2.0
 */var jt=(e=>(e.QUIT="1",e.HIDE="2",e))(jt||{});function Ye(e,t){return Object.prototype.toString.call(e)===`[object ${t}]`}function Kt(e){return Ye(e,"Number")}function Jt(e){return Ye(e,"String")}function qt(e){return e&&Array.isArray(e)}/**
 * @description utils
 * @license Apache License Version 2.0
 */const Xt="hash";function Qt(e){const t=window.localStorage.getItem(e);try{return JSON.parse(window.localStorage.getItem(e))}catch{return t}}function Zt(e,t){window.localStorage.setItem(e,JSON.stringify(t))}function wo(){let e="";for(let t=0;t<32;t++){let r=Math.random()*16|0;(t===8||t===12||t===16||t===20)&&(e+="-"),e+=(t===12?4:t===16?r&3|8:r).toString(16)}return e}const Ao=()=>ht();function er(){let t=new Date().getHours();if(t>=6&&t<=10)return"早上好 ⛅";if(t>=10&&t<=14)return"中午好 🌞";if(t>=14&&t<=18)return"下午好 🌞";if(t>=18&&t<=24)return"晚上好 🌛";if(t>=0&&t<=6)return"凌晨好 🌛"}const tr=()=>{const e=new Date,t=e.getFullYear(),r=O(e.getMonth()+1),o=O(e.getDate()),i=O(e.getHours()),n=O(e.getMinutes()),c=O(e.getSeconds());return""+t+"-"+r+"-"+o+" "+i+":"+n+":"+c},Ro=e=>{const t=new Date(e),r=t.getFullYear(),o=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0"),n=String(t.getHours()).padStart(2,"0"),c=String(t.getMinutes()).padStart(2,"0"),a=String(t.getSeconds()).padStart(2,"0"),u=String(t.getMilliseconds()).padStart(3,"0");return`${r}-${o}-${i} ${n}:${c}:${a}.${u}`},yo=()=>{const e=new Date,t=e.getFullYear(),r=O(e.getMonth()+1),o=O(e.getDate());return`${t}年${r}月${o}日`},O=e=>e<10?"0"+e:e;function Po(){let e=navigator.language?navigator.language:navigator.browserLanguage,t="";return["cn","zh","zh-cn"].includes(e.toLowerCase())?t="zh":t="en",t}function rr(){return{hash:location.hash.substring(1),history:location.pathname+location.search}[Xt]}function je(e){return JSON.parse(JSON.stringify(e)).flatMap(r=>[r,...r.children?je(r.children):[]])}function Ke(e){return JSON.parse(JSON.stringify(e)).filter(r=>{var o,i;return(o=r.children)!=null&&o.length&&(r.children=Ke(r.children)),!((i=r.meta)!=null&&i.isHide)})}const Je=(e,t=[],r={})=>{for(const o of e)r[o.path]=[...t,o],o.children&&Je(o.children,r[o.path],r);return r};function Co(e){return qt(e)?e.length?e.join(" / "):"--":e??"--"}function Oo(e,t){return t.includes(".")?(t.split(".").forEach(r=>e=e[r]??"--"),e):e[t]??"--"}function Mo(e){const t=e.split(".");return t.length==1?e:t[t.length-1]}function ko(e,t,r,o){const i=(r==null?void 0:r.value)??"value",n=(r==null?void 0:r.label)??"label",c=(r==null?void 0:r.children)??"children";let a={};return Array.isArray(t)&&(a=qe(t,e,i,c)),o=="tag"?a!=null&&a.tagType?a.tagType:"":a?a[n]:"--"}function qe(e,t,r,o){return e.reduce((i,n)=>{if(i)return i;if(n[r]===t)return n;if(n[o])return qe(n[o],t,r,o)},null)}function Vo(e){const t=ve();let r=e;return t.tabs&&(r+=40),t.footer&&(r+=30),t.isConsole&&(r+=t.consoleHeight+1),`calc(100vh  - ${r}px)`}function xo(e,t,r=!0){const o=ve();let i=t;return r&&r===!0?(o.tabs&&(i+=40),o.footer&&(i+=30),{[e]:`calc(100vh  - ${i}px)`}):(o.tabs&&(i-=40),o.footer&&(i-=30),{[e]:`${i}px`})}function $o(e="",t=""){return e.localeCompare(t,void 0,{numeric:!0,sensitivity:"base"})}const Q=(e,t)=>({key:e,storage:localStorage,paths:t}),Ie="simple-user",M=w({id:Ie,state:()=>({accessToken:"",refreshToken:"",userInfo:null,defaultModule:null,chooseModule:null,moduleList:[]}),getters:{userInfoGet:e=>e.userInfo,chooseModuleGet:e=>e.chooseModule},actions:{setToken(e,t){this.accessToken=e,this.refreshToken=t},async getUserInfo(){const{data:e}=await loginApi.getLoginUser();return e?this.setUserInfo(e):U({title:"系统错误",message:"获取个人信息失败，请联系系统管理员！",type:"warning",duration:3e3}),this.userInfo},setUserInfo(e){this.userInfo=e,this.defaultModule=e.defaultModule,this.moduleList=e.moduleList},setSignature(e){this.userInfo.signature=e},setUserInfoItem(e,t){this.userInfo[e]=t},clearToken(){this.accessToken="",this.refreshToken=""},clearUserStore(){this.clearToken(),this.userInfo=null,this.defaultModule=null,this.moduleList=[]},setModule(e){this.chooseModule=e}},persist:Q(Ie)}),we="simple-activate",or=w({id:we,state:()=>({isActivated:!1}),getters:{getActivated(){return this.isActivated}},actions:{setActivated(e){this.isActivated=e}},persist:Q(we,["isActivated"])}),ir=Object.assign({"/src/views/activate/index.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.i),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/components/Console.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.h),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/components/Device.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.t),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/components/DeviceInnerTabs.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.q),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/components/DeviceList.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.v),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/components/DeviceListCollapse.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.w),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/components/DeviceSearch.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.x),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/components/Devices.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.y),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/components/MessageMonitor.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.M),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/components/SummaryPie.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.z),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/dialog/AddCustomPointGroupDialog.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.r),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/dialog/AllParamCompareDialog.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.B),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/dialog/DeviceFormDialog.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.u),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/dialog/FilePackageDialog.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.m),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/dialog/ParamCompareDialog.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.E),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/dialog/ProgressDialog.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.P),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/index.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.H),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/AllEditParamSetting.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.I),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/AllParamSetting.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.J),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/CustomGroup.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.K),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/DebugVariable.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.D),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/DeviceBackUp.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.p),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/DeviceInfo.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.L),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/DeviceOperate.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.l),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/DeviceSummary.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.k),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/DeviceTime.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.j),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/DictCfg.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.o),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/FileDownload.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.n),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/FileUpload.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.F),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/GroupInfo.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.N),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/ParamSetting.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.O),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/RemoteControl.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.R),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/RemoteDrive.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.Q),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/RemoteSignal.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.T),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/RemoteTelemetry.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.U),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/RemoteYm.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.V),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/RemoteYt.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.W),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/ReportAuditLog.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.X),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/ReportCommon.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.Y),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/ReportGroup.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.Z),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/ReportOperate.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e._),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/debug/device/views/ReportTrip.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.$),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/device/components/Configure.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.ap),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/device/components/ConfigureList.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aq),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/device/components/Configures.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.at),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/device/components/CustomConfigure.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.ar),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/device/components/DeviceList.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.au),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/device/components/DeviceListCollapse.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.av),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/device/components/DeviceSearch.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aw),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/device/components/EditConfigure.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.as),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/device/components/RemoteSet.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.ao),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/device/index.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.ax),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-define/src/EquipmentList.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.ag),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-define/src/GraphComponent.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.af),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-define/src/GraphDefine.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.ai),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-define/src/GraphTools.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.ae),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-define/src/SetStatus.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.ah),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-editor/src/GraphComponent.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.al),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-editor/src/GraphEditor.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.am),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-icons/back.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.ac),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-icons/delete.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.ak),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-icons/figure-list.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.ad),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-icons/front.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.ab),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-icons/ratio.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.ay),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-icons/redo.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aa),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-icons/save.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aj),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-icons/undo.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.a9),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/components/graph-view/src/GraphView.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.an),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/graph-properties/src/Blank.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.a2),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/graph-properties/src/ColorPicker.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.a3),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/graph-properties/src/Graph.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.a4),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/graph-properties/src/GraphProperties.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.a8),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/graph-properties/src/Group.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.a6),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/graph-properties/src/Node.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.a5),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/graph-properties/src/PathLine.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.a7),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/graph/SelectEquipment.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.a0),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/hmi/packages/graph/saddr/src/SetSAddr.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.a1),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/matrix/components/FuctionSearch.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.az),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/matrix/components/FunctionList.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aA),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/matrix/components/FunctionListCollapse.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aB),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/matrix/components/MatrixContent.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aC),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/matrix/index.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aD),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/matrix/views/DeviceList.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aE),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/matrix/views/DownList.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aF),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/matrix/views/PackageProgram.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aG),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/matrix/views/ParamList.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aH),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/tools/custom/encryption/Encryption.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aI),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/tools/custom/index.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aJ),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/tools/custom/integer-base-converter/Integer-base-converter.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aK),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/tools/custom/json-viewer/Json-viewer.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aL),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/tools/custom/temperature-converter/Temperature-converter.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aM),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/biz/tools/custom/xml-formatter/Xml-formatter.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aN),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/home/<USER>":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aO),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/login/components/phone-login/index.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aP),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/login/components/pwd-login/index.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aQ),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/login/index.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aR),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/config/components/otherConfig.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aS),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/config/components/paramConfig.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aT),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/config/components/sysBaseConfig.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aU),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/config/index.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aV),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/button/components/batch.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aW),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/button/components/form.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aX),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/button/index.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aY),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/menu/components/changeModule.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aZ),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/menu/components/form.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.a_),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/menu/index.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.a$),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/module/components/form.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.b0),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/module/index.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.b1),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/role/components/dataScopeSelector.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.b2),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/role/components/form.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.b3),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/role/components/grantPermission.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.b4),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/role/components/grantResource.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.b5),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/role/index.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.b6),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/spa/components/form.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.b7),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),"/src/views/sys/limit/spa/index.vue":()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.b8),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)}),Xe=async()=>{const e=M(),t=tt();try{let r=se;await t.getAuthMenuList(),await t.getAuthButtonList();const o=t.authMenuListGet.filter(i=>i.isHome===!0);if(o.length>0)r=o[0].path;else{let i=t.authMenuListGet[0].children;i&&i.length>0&&(r=i[0].path)}return t.flatMenuListGet.forEach(i=>{i.children&&delete i.children,i.component&&typeof i.component=="string"&&(i.path===se?i.component=()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(n=>n.H),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url):i.component=ir["/src/views/"+i.component+".vue"]),i.meta.isFull?y.addRoute(i):y.addRoute("layout",i)}),Promise.resolve(r)}catch(r){return e.clearToken(),Promise.reject(r)}},sr=[{path:"/",redirect:q},{path:q,name:"activate",component:()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.i),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),meta:{title:"授权激活"}},{path:He,name:"login",component:()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.aR),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),meta:{title:"登录"}},{path:"/layout",name:"layout",component:()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.b9),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),redirect:se,children:[]}],nr=[{path:"/403",name:"403",component:()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.ba),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),meta:{title:"403页面"}},{path:"/404",name:"404",component:()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.bb),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),meta:{title:"404页面"}},{path:"/500",name:"500",component:()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.bc),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url),meta:{title:"500页面"}},{path:"/:pathMatch(.*)*",component:()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.bb),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)}];X.configure({easing:"ease",speed:500,showSpinner:!1,trickleSpeed:200,minimum:.3});const ar="hash",cr={hash:()=>ut(),history:()=>ct()},y=at({history:cr[ar](),routes:[...sr,...nr],strict:!1,scrollBehavior:()=>({left:0,top:0})});y.beforeEach(async(e,t,r)=>{const o=tt(),i=or();if(X.start(),setTimeout(()=>{const n="VisualDebug";document.title=e.meta.title?`${e.meta.title} - ${n}`:n},0),e.path.toLowerCase()===q)return i.getActivated?r(se):r();if(!i.getActivated)return r({path:q,replace:!0});if(Pt.includes(e.path))return r();if(!o.authMenuListGet.length)try{return await Xe(),r({...e,replace:!0})}catch(n){return console.warn("动态路由加载失败:",n),X.done(),r({path:q,replace:!0})}r()});y.onError(e=>{X.done(),console.warn("路由错误",e.message)});y.afterEach(()=>{X.done()});const ur="simple-keepAlive",J=w({id:ur,state:()=>({keepAliveName:[]}),actions:{async addKeepAliveName(e){!this.keepAliveName.includes(e)&&this.keepAliveName.push(e)},async removeKeepAliveName(e){this.keepAliveName=this.keepAliveName.filter(t=>t!==e)},async setKeepAliveName(e=[]){this.keepAliveName=e}}}),Ae="simple-tabs",lr=w({id:Ae,state:()=>({tabsMenuList:[]}),actions:{async addTabs(e){const t=J();this.tabsMenuList.every(r=>r.path!==e.path)&&this.tabsMenuList.push(e),!t.keepAliveName.includes(e.name)&&e.isKeepAlive&&t.addKeepAliveName(e.path)},async removeTabs(e,t=!0){const r=J();t&&this.tabsMenuList.forEach((i,n)=>{if(i.path!==e)return;const c=this.tabsMenuList[n+1]||this.tabsMenuList[n-1];c&&y.push(c.path)}),this.tabsMenuList=this.tabsMenuList.filter(i=>i.path!==e);const o=this.tabsMenuList.find(i=>i.path===e);o!=null&&o.isKeepAlive&&r.removeKeepAliveName(o.path),this.tabsMenuList=this.tabsMenuList.filter(i=>i.path!==e)},async closeTabsOnSide(e,t){const r=J(),o=this.tabsMenuList.findIndex(n=>n.path===e);if(o!==-1){const n=t==="left"?[0,o]:[o+1,this.tabsMenuList.length];this.tabsMenuList=this.tabsMenuList.filter((c,a)=>a<n[0]||a>=n[1]||!c.close)}const i=this.tabsMenuList.filter(n=>n.isKeepAlive);r.setKeepAliveName(i.map(n=>n.path))},async closeMultipleTab(e){const t=J();this.tabsMenuList=this.tabsMenuList.filter(o=>o.path===e||!o.close);const r=this.tabsMenuList.filter(o=>o.isKeepAlive);t.setKeepAliveName(r.map(o=>o.path))},async setTabs(e){this.tabsMenuList=e},async setTabsTitle(e){this.tabsMenuList.forEach(t=>{t.path==rr()&&(t.title=e)})}},persist:Q(Ae)}),dr="simple-message",mr=w({id:dr,state:()=>({unReadInfo:{},unReadCount:0,newUnRead:[]}),getters:{unReadCountGet:e=>e.unReadCount,unReadInfoGet:e=>e.unReadInfo},actions:{setShowMore(e){this.showMore=e},unReadCountAdd(e){this.unReadCount+=e},unReadCountSubtract(e){this.unReadCount-=e},unReadCountSet(e){this.unReadCount=e},async getUnReadInfo(e=!1){await ge.unReadCount().then(t=>{if(t.data.length>0){this.unReadInfo=t.data.reduce((o,i)=>(o[i.category]=i.unReadCount,o),{});let r=0;t.data.map(o=>{r+=o.unReadCount}),r>this.unReadCount&&this.getNewMessage(e),this.unReadCountSet(r)}else this.unReadCountSet(0)})},async getNewUnRead(){await ge.newUnRead().then(e=>{e.data.length>0&&(this.newUnRead=e.data)})},getUnReadCount(e){return this.unReadInfo[e]||0},getNewMessage(e=!1,t="您有一条新消息,请注意查收!"){this.getNewUnRead(),e&&U({title:"收到一条新消息",message:t,icon:bt,offset:40})},async getNewMessageInterval(){setInterval(()=>{this.getUnReadInfo(!0)},1e4)},reSet(){this.unReadCount=0,this.unReadInfo={},this.newUnRead=[]}}}),pr={mainMenu:[{parentId:0,title:"menu.debug.title",name:"debug",description:"menu.debug.description",code:"system",category:"SPA",module:null,menuType:"MENU",path:"/biz/debug/device/index",component:"biz/debug/device/index",icon:"ant-design:bug-filled",activeMenu:null,isHide:!1,isFull:!1,isAffix:!1,isKeepAlive:!0,isHome:!0,sortCode:1,meta:{icon:"ant-design:bug-filled",title:"menu.debug.title",activeMenu:null,isHide:!1,isLink:"",isFull:!1,isAffix:!1,isKeepAlive:!0},children:[],status:"ENABLE"},{parentId:0,title:"menu.configure.title",name:"configure",description:"menu.configure.description",code:"system",category:"SPA",module:null,menuType:"MENU",path:"/biz/hmi/device/index",component:"biz/hmi/device/index",icon:"eva:pie-chart-fill",activeMenu:null,isHide:!1,isFull:!1,isAffix:!1,isKeepAlive:!0,isHome:!1,sortCode:1,meta:{icon:"eva:pie-chart-fill",title:"menu.configure.title",activeMenu:null,isHide:!1,isLink:"",isFull:!1,isAffix:!1,isKeepAlive:!0},children:[],status:"ENABLE"},{parentId:0,title:"menu.tool.title",name:"tool",description:"menu.tool.description",code:"system",category:"SPA",module:null,menuType:"MENU",path:"/biz/matrix/index",component:"biz/matrix/index",icon:"ant-design:profilexxx-outlined",activeMenu:null,isHide:!1,isFull:!1,isAffix:!1,isKeepAlive:!0,isHome:!1,sortCode:1,meta:{icon:"ant-design:tool-filled",title:"menu.tool.title",activeMenu:null,isHide:!1,isLink:"",isFull:!1,isAffix:!1,isKeepAlive:!0},children:[],status:"ENABLE"},{parentId:0,title:"menu.sysConfig.title",name:"sysConfig",description:"menu.sysConfig.description",code:"system",category:"MENU",module:null,menuType:"MENU",path:"/sys/config/index",component:"sys/config/index",icon:"ant-design:setting-filled",activeMenu:null,isHide:!0,isFull:!1,isAffix:!1,isKeepAlive:!0,isHome:!1,sortCode:12,meta:{icon:"ant-design:setting-filled",title:"menu.sysConfig.title",activeMenu:null,isHide:!0,isLink:"",isFull:!1,isAffix:!1,isKeepAlive:!0},children:[],status:"ENABLE"}]},hr={data:pr};class gr{constructor(){v(this,"metrics",{});v(this,"startTime");this.startTime=performance.now(),console.log("🚀 [AuthPerformance] 授权性能监控开始")}markCheckAuthStart(){this.metrics.checkAuthStart=performance.now(),this.logMetric("授权检查开始",this.metrics.checkAuthStart)}markCheckAuthEnd(){if(this.metrics.checkAuthEnd=performance.now(),this.logMetric("授权检查结束",this.metrics.checkAuthEnd),this.metrics.checkAuthStart){const t=this.metrics.checkAuthEnd-this.metrics.checkAuthStart;console.log(`🔐 [AuthPerformance] 授权检查耗时: ${t.toFixed(2)}ms`)}}markLanguageSyncStart(){this.metrics.languageSyncStart=performance.now(),this.logMetric("语言同步开始",this.metrics.languageSyncStart)}markLanguageSyncEnd(){if(this.metrics.languageSyncEnd=performance.now(),this.logMetric("语言同步结束",this.metrics.languageSyncEnd),this.metrics.languageSyncStart){const t=this.metrics.languageSyncEnd-this.metrics.languageSyncStart;console.log(`🌐 [AuthPerformance] 语言同步耗时: ${t.toFixed(2)}ms`)}}markRouteLoadStart(){this.metrics.routeLoadStart=performance.now(),this.logMetric("路由加载开始",this.metrics.routeLoadStart)}markRouteLoadEnd(){if(this.metrics.routeLoadEnd=performance.now(),this.logMetric("路由加载结束",this.metrics.routeLoadEnd),this.metrics.routeLoadStart){const t=this.metrics.routeLoadEnd-this.metrics.routeLoadStart;console.log(`🛣️ [AuthPerformance] 路由加载耗时: ${t.toFixed(2)}ms`)}}markDeviceControllerInitStart(){this.metrics.deviceControllerInitStart=performance.now(),this.logMetric("设备控制器初始化开始",this.metrics.deviceControllerInitStart)}markDeviceControllerInitEnd(){if(this.metrics.deviceControllerInitEnd=performance.now(),this.logMetric("设备控制器初始化结束",this.metrics.deviceControllerInitEnd),this.metrics.deviceControllerInitStart){const t=this.metrics.deviceControllerInitEnd-this.metrics.deviceControllerInitStart;console.log(`🔧 [AuthPerformance] 设备控制器初始化耗时: ${t.toFixed(2)}ms`)}}logMetric(t,r){const o=r-this.startTime;console.log(`📊 [AuthPerformance] ${t}: ${o.toFixed(2)}ms`)}generateReport(){console.group("📊 授权启动性能报告");const t={授权检查耗时:this.getTimeDiff("checkAuthStart","checkAuthEnd"),语言同步耗时:this.getTimeDiff("languageSyncStart","languageSyncEnd"),路由加载耗时:this.getTimeDiff("routeLoadStart","routeLoadEnd"),设备控制器初始化耗时:this.getTimeDiff("deviceControllerInitStart","deviceControllerInitEnd"),总启动时间:this.getTotalTime()};Object.entries(t).forEach(([r,o])=>{o!==null&&console.log(`${r}: ${o}ms`)}),this.generateSuggestions(),console.groupEnd()}getTimeDiff(t,r){const o=this.metrics[t],i=this.metrics[r];return o&&i?Number((i-o).toFixed(2)):null}getTotalTime(){const t=this.metrics.deviceControllerInitEnd||this.metrics.routeLoadEnd||this.metrics.checkAuthEnd;return t?Number((t-this.startTime).toFixed(2)):null}generateSuggestions(){console.group("💡 性能优化建议");const t=this.getTimeDiff("checkAuthStart","checkAuthEnd");t&&t>5e3&&console.warn("⚠️ 授权检查耗时过长，建议添加缓存机制");const r=this.getTimeDiff("languageSyncStart","languageSyncEnd");r&&r>1e3&&console.warn("⚠️ 语言同步耗时过长，建议异步处理");const o=this.getTimeDiff("routeLoadStart","routeLoadEnd");o&&o>2e3&&console.warn("⚠️ 路由加载耗时过长，建议优化动态路由加载");const i=this.getTotalTime();i&&i>1e4&&console.warn("⚠️ 总启动时间过长，建议进行全面优化"),console.groupEnd()}}const Z=new gr,zo=()=>Z.markCheckAuthStart(),Bo=()=>Z.markCheckAuthEnd(),No=()=>Z.markLanguageSyncStart(),Fo=()=>Z.markLanguageSyncEnd(),Go=()=>Z.generateReport();class vr{constructor(){v(this,"metrics",{});v(this,"startTime");this.startTime=performance.now(),this.metrics.startTime=this.startTime,this.initializeListeners()}initializeListeners(){document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{this.metrics.domContentLoaded=performance.now(),this.logMetric("DOM Content Loaded",this.metrics.domContentLoaded)}):this.metrics.domContentLoaded=performance.now(),document.readyState!=="complete"?window.addEventListener("load",()=>{this.metrics.windowLoaded=performance.now(),this.logMetric("Window Loaded",this.metrics.windowLoaded)}):this.metrics.windowLoaded=performance.now()}markAppMounted(){this.metrics.appMounted=performance.now(),this.logMetric("App Mounted",this.metrics.appMounted)}markRouteReady(){this.metrics.routeReady=performance.now(),this.logMetric("Route Ready",this.metrics.routeReady)}markResourcesLoaded(){this.metrics.resourcesLoaded=performance.now(),this.logMetric("Resources Loaded",this.metrics.resourcesLoaded),this.generateReport()}logMetric(t,r){const o=r-this.startTime;console.log(`🚀 [Performance] ${t}: ${o.toFixed(2)}ms`)}generateReport(){console.group("📊 启动性能报告");const t={DOM内容加载:this.getTimeDiff("domContentLoaded"),窗口加载完成:this.getTimeDiff("windowLoaded"),应用挂载完成:this.getTimeDiff("appMounted"),路由准备完成:this.getTimeDiff("routeReady"),资源加载完成:this.getTimeDiff("resourcesLoaded"),总启动时间:this.getTimeDiff("resourcesLoaded")};Object.entries(t).forEach(([r,o])=>{if(o!==null){const i=this.getPerformanceStatus(r,o);console.log(`${i} ${r}: ${o}ms`)}}),this.generateSuggestions(),console.groupEnd()}getPerformanceStatus(t,r){const i={DOM内容加载:100,窗口加载完成:200,应用挂载完成:500,路由准备完成:300,资源加载完成:1e3,总启动时间:2e3}[t]||1e3;return r<=i*.5?"🟢":r<=i?"🟡":"🔴"}getTimeDiff(t){const r=this.metrics[t];return r?Number((r-this.startTime).toFixed(2)):null}generateSuggestions(){const t=this.getTimeDiff("appMounted"),r=this.getTimeDiff("routeReady"),o=this.getTimeDiff("resourcesLoaded");console.group("💡 性能优化建议"),t&&t>1e3&&console.warn("应用挂载时间较长，建议优化main.ts中的同步导入"),r&&r>1500&&console.warn("路由初始化时间较长，建议优化动态路由加载"),o&&o>3e3&&console.warn("资源加载时间较长，建议实现更多的懒加载策略"),t&&t<500&&console.log("✅ 应用挂载速度良好"),console.groupEnd()}getWebVitals(){if("web-vital"in window)return;const t=performance.getEntriesByType("navigation")[0];t&&(console.group("🌐 Web性能指标"),console.log(`DNS查询: ${(t.domainLookupEnd-t.domainLookupStart).toFixed(2)}ms`),console.log(`TCP连接: ${(t.connectEnd-t.connectStart).toFixed(2)}ms`),console.log(`请求响应: ${(t.responseEnd-t.requestStart).toFixed(2)}ms`),console.log(`DOM解析: ${(t.domContentLoadedEventEnd-t.domContentLoadedEventStart).toFixed(2)}ms`),console.groupEnd())}}const Qe=new vr,Ho=()=>Qe.markAppMounted(),Uo=()=>Qe.markResourcesLoaded();class _r{constructor(){v(this,"currentSwitch",null);v(this,"switchHistory",[]);v(this,"maxHistorySize",50)}startSwitch(t,r=!1){const o=performance.now();this.currentSwitch&&!this.currentSwitch.endTime&&this.endSwitch(!0),this.currentSwitch={componentKey:t,startTime:o,fromCache:r,interrupted:!1},console.log(`🔄 [ComponentSwitch] 开始切换到组件: ${t}${r?" (从缓存)":""}`)}endSwitch(t=!1){if(!this.currentSwitch){console.warn("⚠️ [ComponentSwitch] 没有正在进行的组件切换");return}const r=performance.now(),o=r-this.currentSwitch.startTime;this.currentSwitch.endTime=r,this.currentSwitch.interrupted=t,this.addToHistory({...this.currentSwitch});const i=t?"中断":"完成",n=this.currentSwitch.fromCache?" (缓存)":"";console.log(`✅ [ComponentSwitch] 组件切换${i}: ${this.currentSwitch.componentKey}${n}, 耗时: ${o.toFixed(2)}ms`),!t&&o>1e3&&console.warn(`⚠️ [ComponentSwitch] 组件切换耗时过长: ${o.toFixed(2)}ms`),this.currentSwitch=null}recordError(t){this.currentSwitch&&(this.currentSwitch.error=t,console.error(`❌ [ComponentSwitch] 组件切换错误: ${t}`),this.endSwitch(!0))}addToHistory(t){this.switchHistory.unshift(t),this.switchHistory.length>this.maxHistorySize&&(this.switchHistory=this.switchHistory.slice(0,this.maxHistorySize))}getStatistics(){const t=this.switchHistory.filter(l=>!l.interrupted&&!l.error),r=this.switchHistory.filter(l=>l.interrupted),o=this.switchHistory.filter(l=>l.error),i=this.switchHistory.filter(l=>l.fromCache),n=t.filter(l=>l.endTime).map(l=>l.endTime-l.startTime),c=n.length>0?n.reduce((l,S)=>l+S,0)/n.length:0,a=n.length>0?Math.max(...n):0,u=n.length>0?Math.min(...n):0;return{total:this.switchHistory.length,completed:t.length,interrupted:r.length,errors:o.length,cached:i.length,avgDuration:Number(c.toFixed(2)),maxDuration:Number(a.toFixed(2)),minDuration:Number(u.toFixed(2))}}printReport(){const t=this.getStatistics();console.group("📊 [ComponentSwitch] 组件切换性能报告"),console.log(`总切换次数: ${t.total}`),console.log(`成功完成: ${t.completed}`),console.log(`被中断: ${t.interrupted}`),console.log(`发生错误: ${t.errors}`),console.log(`缓存命中: ${t.cached}`),t.completed>0&&(console.log(`平均耗时: ${t.avgDuration}ms`),console.log(`最长耗时: ${t.maxDuration}ms`),console.log(`最短耗时: ${t.minDuration}ms`)),console.groupEnd()}clearHistory(){this.switchHistory=[],console.log("🗑️ [ComponentSwitch] 历史记录已清空")}getCurrentSwitch(){return this.currentSwitch}getHistory(){return[...this.switchHistory]}}const Wo=new _r;class fr{constructor(){v(this,"CACHE_PREFIX","visualdebug_startup_");v(this,"DEFAULT_EXPIRY",24*60*60*1e3);v(this,"CACHE_VERSION","1.0.0")}setCache(t,r,o){try{const i={data:r,timestamp:Date.now(),version:this.CACHE_VERSION,expiry:o||this.DEFAULT_EXPIRY};localStorage.setItem(this.CACHE_PREFIX+t,JSON.stringify(i)),console.log(`✅ 缓存已保存: ${t}`)}catch(i){console.warn(`❌ 缓存保存失败: ${t}`,i)}}getCache(t){try{const r=localStorage.getItem(this.CACHE_PREFIX+t);if(!r)return null;const o=JSON.parse(r);return o.version!==this.CACHE_VERSION?(console.log(`🔄 缓存版本不匹配，清除: ${t}`),this.removeCache(t),null):o.expiry&&Date.now()-o.timestamp>o.expiry?(console.log(`⏰ 缓存已过期，清除: ${t}`),this.removeCache(t),null):(console.log(`✅ 缓存命中: ${t}`),o.data)}catch(r){return console.warn(`❌ 缓存读取失败: ${t}`,r),this.removeCache(t),null}}removeCache(t){try{localStorage.removeItem(this.CACHE_PREFIX+t)}catch(r){console.warn(`❌ 缓存删除失败: ${t}`,r)}}clearAllCache(){try{Object.keys(localStorage).forEach(r=>{r.startsWith(this.CACHE_PREFIX)&&localStorage.removeItem(r)}),console.log("🧹 所有启动缓存已清除")}catch(t){console.warn("❌ 清除缓存失败",t)}}getCacheStats(){const t={};try{Object.keys(localStorage).forEach(o=>{if(o.startsWith(this.CACHE_PREFIX)){const i=o.replace(this.CACHE_PREFIX,""),n=localStorage.getItem(o);if(n)try{const c=JSON.parse(n),a=Date.now()-c.timestamp,u=c.expiry?a>c.expiry:!1;t[i]={size:new Blob([n]).size,age:Math.round(a/1e3),expired:u,version:c.version}}catch{t[i]={error:"Invalid cache data"}}}})}catch(r){console.warn("❌ 获取缓存统计失败",r)}return t}async warmupCache(){console.log("🔥 开始预热启动缓存...");const t=performance.now(),o=["menuList","userInfo","systemConfig"].map(async c=>this.getCache(c)?(console.log(`📦 预热缓存: ${c}`),{key:c,cached:!0}):(console.log(`🔍 缓存未命中: ${c}`),{key:c,cached:!1})),i=await Promise.all(o),n=performance.now()-t;console.log(`🔥 缓存预热完成，耗时: ${n.toFixed(2)}ms`),console.log("📊 缓存状态:",i)}setBatchCache(t){Object.entries(t).forEach(([r,o])=>{o!==void 0&&this.setCache(r,o)})}getBatchCache(t){const r={};return t.forEach(o=>{const i=this.getCache(o);i!==null&&(r[o]=i)}),r}checkCacheHealth(){const t=[];try{const r=this.CACHE_PREFIX+"test";localStorage.setItem(r,"test"),localStorage.removeItem(r)}catch{t.push("localStorage不可用")}try{this.getCacheUsage()>5*1024*1024&&t.push("缓存占用过大")}catch{t.push("无法检查缓存大小")}return{healthy:t.length===0,issues:t}}getCacheUsage(){let t=0;try{Object.keys(localStorage).forEach(o=>{if(o.startsWith(this.CACHE_PREFIX)){const i=localStorage.getItem(o);i&&(t+=new Blob([i]).size)}})}catch(r){console.warn("❌ 计算缓存大小失败",r)}return t}}const ae=new fr,Ze=(e,t,r)=>ae.setCache(e,t,r),et=e=>ae.getCache(e),Er=()=>ae.warmupCache(),Re=Object.freeze(Object.defineProperty({__proto__:null,getStartupCache:et,setStartupCache:Ze,startupCache:ae,warmupStartupCache:Er},Symbol.toStringTag,{value:"Module"}));class Sr{constructor(){v(this,"testResults",[]);v(this,"currentSuite",null)}startTestSuite(t){this.currentSuite={name:t,tests:[],totalDuration:0,successCount:0,failureCount:0},console.group(`🧪 开始测试套件: ${t}`)}endTestSuite(){if(!this.currentSuite){console.warn("❌ 没有活动的测试套件");return}this.testResults.push(this.currentSuite),console.log(`✅ 测试套件完成: ${this.currentSuite.name}`),console.log(`📊 总耗时: ${this.currentSuite.totalDuration.toFixed(2)}ms`),console.log(`✅ 成功: ${this.currentSuite.successCount}`),console.log(`❌ 失败: ${this.currentSuite.failureCount}`),console.groupEnd(),this.currentSuite=null}async runTest(t,r){if(!this.currentSuite){console.warn("❌ 请先开始测试套件");return}const o=performance.now();let i;try{const n=await r(),a=performance.now()-o;i={name:t,duration:n.duration||a,success:n.success,data:n.data},i.success?(console.log(`✅ ${t}: ${i.duration.toFixed(2)}ms`),this.currentSuite.successCount++):(console.warn(`⚠️ ${t}: ${i.duration.toFixed(2)}ms (性能不达标)`),this.currentSuite.failureCount++)}catch(n){const a=performance.now()-o;i={name:t,duration:a,success:!1,error:n instanceof Error?n.message:String(n)},console.error(`❌ ${t}: ${a.toFixed(2)}ms - ${i.error}`),this.currentSuite.failureCount++}this.currentSuite.tests.push(i),this.currentSuite.totalDuration+=i.duration}async testDOMPerformance(){await this.runTest("DOM内容加载",async()=>{const t=performance.getEntriesByType("navigation")[0],r=t.domContentLoadedEventEnd-t.domContentLoadedEventStart;return{duration:r,success:r<100}}),await this.runTest("DOM完全加载",async()=>{const t=performance.getEntriesByType("navigation")[0],r=t.loadEventEnd-t.loadEventStart;return{duration:r,success:r<200}})}async testResourcePerformance(){await this.runTest("CSS资源加载",async()=>{const r=performance.getEntriesByType("resource").filter(n=>n.name.includes(".css")),o=r.reduce((n,c)=>n+c.duration,0),i=r.length>0?o/r.length:0;return{duration:i,success:i<50,data:{count:r.length,total:o}}}),await this.runTest("JS资源加载",async()=>{const r=performance.getEntriesByType("resource").filter(n=>n.name.includes(".js")),o=r.reduce((n,c)=>n+c.duration,0),i=r.length>0?o/r.length:0;return{duration:i,success:i<100,data:{count:r.length,total:o}}})}async testCachePerformance(){await this.runTest("缓存写入性能",async()=>{const{setStartupCache:t}=await s(async()=>{const{setStartupCache:n}=await Promise.resolve().then(()=>Re);return{setStartupCache:n}},void 0,import.meta.url),r={test:"data",timestamp:Date.now()},o=performance.now();t("menuList",r);const i=performance.now()-o;return{duration:i,success:i<10}}),await this.runTest("缓存读取性能",async()=>{const{getStartupCache:t}=await s(async()=>{const{getStartupCache:n}=await Promise.resolve().then(()=>Re);return{getStartupCache:n}},void 0,import.meta.url),r=performance.now(),o=t("menuList"),i=performance.now()-r;return{duration:i,data:o,success:i<5}})}async testComponentPerformance(){await this.runTest("Vue组件创建",async()=>{const t=performance.now(),{createApp:r}=await s(async()=>{const{createApp:n}=await import("../vendor/vendor-P-ltm-Yc.js").then(c=>c.dn);return{createApp:n}},__vite__mapDeps([3,2,5,1,4]),import.meta.url),o=r({template:"<div>Test Component</div>"}),i=performance.now()-t;return o.unmount(),{duration:i,success:i<20}})}async testNetworkPerformance(){await this.runTest("DNS查询时间",async()=>{const t=performance.getEntriesByType("navigation")[0],r=t.domainLookupEnd-t.domainLookupStart;return{duration:r,success:r<50}}),await this.runTest("TCP连接时间",async()=>{const t=performance.getEntriesByType("navigation")[0],r=t.connectEnd-t.connectStart;return{duration:r,success:r<100}}),await this.runTest("首字节时间(TTFB)",async()=>{const t=performance.getEntriesByType("navigation")[0],r=t.responseStart-t.requestStart;return{duration:r,success:r<200}})}async runFullTestSuite(){console.log("🚀 开始完整的启动性能测试"),this.startTestSuite("DOM性能测试"),await this.testDOMPerformance(),this.endTestSuite(),this.startTestSuite("资源加载性能测试"),await this.testResourcePerformance(),this.endTestSuite(),this.startTestSuite("缓存性能测试"),await this.testCachePerformance(),this.endTestSuite(),this.startTestSuite("组件性能测试"),await this.testComponentPerformance(),this.endTestSuite(),this.startTestSuite("网络性能测试"),await this.testNetworkPerformance(),this.endTestSuite(),this.generateSummaryReport()}generateSummaryReport(){console.group("📊 启动性能测试总结报告");let t=0,r=0,o=0;this.testResults.forEach(i=>{t+=i.tests.length,r+=i.successCount,o+=i.totalDuration,console.log(`📋 ${i.name}:`),console.log(`  ✅ 成功: ${i.successCount}`),console.log(`  ❌ 失败: ${i.failureCount}`),console.log(`  ⏱️ 耗时: ${i.totalDuration.toFixed(2)}ms`)}),console.log(`
🎯 总体统计:`),console.log(`总测试数: ${t}`),console.log(`成功率: ${(r/t*100).toFixed(1)}%`),console.log(`总耗时: ${o.toFixed(2)}ms`),this.generatePerformanceRecommendations(),console.groupEnd()}generatePerformanceRecommendations(){console.group("💡 性能优化建议");const t=[];this.testResults.forEach(r=>{r.tests.forEach(o=>{if(!o.success)switch(o.name){case"DOM内容加载":o.duration>100&&t.push("DOM加载较慢，建议减少同步脚本和优化HTML结构");break;case"缓存写入性能":o.duration>10&&t.push("缓存写入较慢，建议检查localStorage性能或减少缓存数据量");break;case"CSS资源加载":o.duration>50&&t.push("CSS加载较慢，建议启用压缩和CDN加速");break;case"JS资源加载":o.duration>100&&t.push("JS加载较慢，建议代码分割和懒加载");break}})}),t.length===0?console.log("🎉 所有性能指标都达标！"):t.forEach(r=>console.log(`• ${r}`)),console.groupEnd()}getTestResults(){return[...this.testResults]}clearTestResults(){this.testResults=[],this.currentSuite=null}}const fe=new Sr,br=()=>fe.runFullTestSuite(),Tr=()=>fe.getTestResults(),Yo=Object.freeze(Object.defineProperty({__proto__:null,getStartupTestResults:Tr,runStartupTests:br,startupTester:fe},Symbol.toStringTag,{value:"Module"})),Dr="simple-auth",tt=w({id:Dr,state:()=>({loginLoading:!1,showChooseModule:!1,moduleList:[],authButtonList:[],authMenuList:[]}),getters:{authButtonListGet:e=>e.authButtonList,authMenuListGet:e=>e.authMenuList,showMenuListGet:e=>Ke(e.authMenuList),flatMenuListGet:e=>je(e.authMenuList),breadcrumbListGet:e=>Je(e.authMenuList)},actions:{SetModuleList(e){this.moduleList=e},async chooseModule(e){M().setModule(e.id),await ge.setDefaultModule(e),this.showChooseModule=!1},async getAuthButtonList(){const e=M(),{userInfo:t}=e;this.authButtonList=(t==null?void 0:t.buttonCodeList)||[]},async getAuthMenuList(e){console.log(e);const t=et("menuList");if(t&&t.length>0){console.log("📦 使用缓存的菜单数据"),this.authMenuList=t;return}this.authMenuList=hr.data.mainMenu,Ze("menuList",this.authMenuList,30*60*1e3),console.log("💾 菜单数据已缓存")},async loginPwd(e){this.loginLoading=!0,await loginApi.login(e).then(t=>{t.data&&this.loginSuccess(t.data)}).catch(t=>Promise.reject(t)).finally(()=>{this.loginLoading=!1})},async loginPhone(e){this.loginLoading=!0,this.setTenantId(e),await loginApi.loginByPhone(e).then(t=>{t.data&&this.loginSuccess(t.data)}).catch(t=>Promise.reject(t)).finally(()=>{this.loginLoading=!1})},async handleActionAfterLogin(){await Xe().then(e=>{const t=lr(),r=J(),o=mr();t.setTabs([]),r.setKeepAliveName([]),o.reSet(),y.push(e),U({title:er(),message:"欢迎回来 SimpleAdmin",type:"success",duration:3e3})}).catch(e=>{console.log("[ err ] >",e),U({title:"系统错误",message:"系统错误,请联系系统管理员！",type:"warning",duration:3e3})})},loginSuccess(e){const{defaultModule:t,moduleList:r}=e,o=M();if(this.SetModuleList(r),r.length===1){o.setModule(r[0].id),this.handleActionAfterLogin();return}else t&&r.find(i=>i.id===t)?(o.setModule(t),this.handleActionAfterLogin()):this.showChooseModule=!0}}}),ye="simple-dict",jo=w({id:ye,state:()=>({dictInfo:[]}),getters:{dictInfoGet:e=>e.dictInfo},actions:{async setDictTree(){const{data:e}=await Yr.tree();e?this.dictInfo=e:U({title:"系统错误",message:"获取系统字典信息失败，请联系系统管理员！",type:"warning",duration:3e3})},dictTranslation(e,t){const r=this.dictInfo.find(i=>i.dictValue===e);if(!r)return"无此字典";const o=r.children.find(i=>i.dictValue===t);return(o==null?void 0:o.dictLabel)||"无此字典"},getDictList(e,t){const r=this.dictInfo.find(o=>o.dictValue===e);return r?(r.children=r.children.filter(o=>o.status===_e.ENABLE),r.children.map(o=>t?{value:o.dictValue==="true",label:o.dictLabel}:{value:o.dictValue,label:o.dictLabel})):[]}},persist:Q(ye)}),Lr={SYS_NAME:"",SYS_LOGO:"./logo.png",SYS_VERSION:"",WAVE_TOOL_PATH:"",SYS_COPYRIGHT:"2025 © {} By Sieyuan Electric Co., Ltd.",SYS_COPYRIGHT_URL:"2025 © {} By Sieyuan Electric Co., Ltd."},Ir={PARAM_REFRESH_TIME:5e3,VARI_REFRESH_TIME:5e3,STATE_REFRESH_TIME:2e3,REPORT_REFRESH_TIME:2e3},Pe={data:Lr,paramInfo:Ir},wr="VisualDebug",Ar="1.00.001",Rr="1、包含可视化工具连接、装置信息查看、设定量、模拟量、状态量、遥信、遥测、遥控、报告、装置对时、定值导入导出、变量调试功能；2、包含组态工具预览、新增、编辑、自定义图符、关联装置信息功能；3、包含主题定制、it小工具、装置配置导入导出功能",yr="可视化平台工程调试工具",Pr="./public/electron/main.js",Cr={dev:"chcp 65001 && ee-bin dev",build:"npm run build-frontend && npm run build-electron && ee-bin encrypt",start:"chcp 65001 && ee-bin start","dev-frontend":"ee-bin dev --serve=frontend","dev-electron":"ee-bin dev --serve=electron","dev-go":"ee-bin exec --cmds=go","dev-go-w":"ee-bin exec --cmds=go_w","dev-python":"ee-bin exec --cmds=python","build-frontend":"ee-bin build --cmds=frontend && ee-bin move --flag=frontend_dist","build-electron":"ee-bin build --cmds=electron","build-go-w":"ee-bin move --flag=go_static,go_config,go_package && ee-bin build --cmds=go_w","build-go-m":"ee-bin move --flag=go_static,go_config,go_package,go_images && ee-bin build --cmds=go_m","build-go-l":"ee-bin move --flag=go_static,go_config,go_package,go_images && ee-bin build --cmds=go_l","build-python":"ee-bin build --cmds=python && ee-bin move --flag=python_dist",encrypt:"ee-bin encrypt",icon:"ee-bin icon","re-sqlite":"electron-rebuild -f -w better-sqlite3","build-w":"ee-bin build --cmds=win64","build-we":"ee-bin build --cmds=win_e","build-win32":"ee-bin build --cmds=win32","build-7z":"ee-bin build --cmds=win_7z","build-7z-auto":"node ./build/script/set-builder-version.js && npm run build-7z && node ./build/script/set-builder-version.js --restore","build-m":"ee-bin build --cmds=mac","build-m-arm64":"ee-bin build --cmds=mac_arm64","build-l":"ee-bin build --cmds=linux","debug-dev":"cross-env DEBUG=ee-* ee-bin dev","debug-encrypt":"ee-bin encrypt","debug-electron":"cross-env DEBUG=ee-* ee-bin dev --serve=electron","debug-move":"ee-bin move --flag=frontend_dist"},Or="https://www.sieyuan.com",Mr={name:"Sieyuan",email:"<EMAIL>"},kr="Apache",Vr={"@electron/rebuild":"^3.6.0","@types/node":"^20.16.0","@types/uuid":"^10.0.0","@types/xml2js":"^0.4.14","cross-env":"^7.0.3",debug:"^4.4.0","ee-bin":"^4.1.10",electron:"31.0.1","electron-builder":"^23.6.0",eslint:"^5.13.0","eslint-plugin-prettier":"^3.0.1","icon-gen":"^5.0.0",typescript:"^5.4.2"},xr={axios:"^1.7.9",dayjs:"^1.11.13","decimal.js":"^10.5.0","ee-core":"^4.1.5","electron-updater":"^6.3.8",entities:"^1.1.2",entries:"^1.0.1",esbuild:"^0.21.5",exceljs:"^4.4.0","fast-csv":"^4.3.6","iec-common":"file:./libs/iec-common-1.0.13.tgz","iec-net":"file:./libs/iec-net-1.0.21.tgz","iec-upadrpc":"file:./libs/iec-upadrpc-1.0.51.tgz",license:"file:./libs/license-1.0.2.tgz",rollup:"^4.37.0",uuid:"^8.3.2","xml-formatter":"^3.6.3",xml2js:"^0.6.2","node-gyp":"^9.4.1","xml2js-xpath":"^0.13.0"},$r={registry:"http://***********:5000"},Ce={name:wr,version:Ar,feature:Rr,description:yr,main:Pr,scripts:Cr,homepage:Or,author:Mr,license:kr,devDependencies:Vr,dependencies:xr,publishConfig:$r},Oe="simple-config",Me={sysInfo:{SYS_NAME:"",SYS_LOGO:"",SYS_VERSION:"",WAVE_TOOL_PATH:"",SYS_COPYRIGHT:"",SYS_COPYRIGHT_URL:""},paramInfo:{PARAM_REFRESH_TIME:5e3,VARI_REFRESH_TIME:5e3,STATE_REFRESH_TIME:2e3,REPORT_REFRESH_TIME:2e3}},zr=w({id:Oe,state:()=>({sysInfo:{SYS_NAME:"",SYS_LOGO:"",SYS_VERSION:"",WAVE_TOOL_PATH:"",SYS_COPYRIGHT:"",SYS_COPYRIGHT_URL:""},paramInfo:{PARAM_REFRESH_TIME:5e3,VARI_REFRESH_TIME:5e3,STATE_REFRESH_TIME:2e3,REPORT_REFRESH_TIME:5e3}}),getters:{sysBaseInfoGet:e=>e.sysInfo,paramInfoGet:e=>e.paramInfo},actions:{async setSysBaseInfo(){return console.log(this.sysInfo),this.sysInfo=Pe.data,this.sysInfo.SYS_NAME=Ce.name,this.sysInfo.SYS_VERSION=Ce.version,this.sysInfo},async getSysBaseInfo(){return this.setSysBaseInfo()},async resetValues(e=["SYS_NAME","SYS_LOGO","SYS_VERSION","SYS_COPYRIGHT","SYS_COPYRIGHT_URL"]){const t=pe.cloneDeep(Me);e.forEach(r=>{t.sysInfo[r]=this.$state.sysInfo[r]}),t.paramInfo=this.$state.paramInfo,this.$state=t},async setParamInfo(){return this.paramInfo=Pe.paramInfo,this.sysInfo},async getParamInfo(){return this.setParamInfo()},async resetParamValues(e=[]){const t=pe.cloneDeep(Me);e.forEach(r=>{t.paramInfo[r]=this.$state.paramInfo[r]}),t.sysInfo=this.$state.sysInfo,this.$state=t},async setTenantList(){const{data:e}=await jr.tenantList();return this.tenantList=e,e}},persist:Q(Oe)}),Br="simple-hmi",Ko=w({id:Br,state:()=>({hmiInfo:{configureList:[],currConfigure:void 0}}),actions:{async setCurrentHmi(e){this.hmiInfo.currConfigure=e}}}),Nr=e=>{switch(e){case 400:h.error("请求失败！请您稍后重试");break;case 401:h.error("登录失效！请您重新登录");break;case 403:h.error("当前账号无权限访问！");break;case 404:h.error("你所访问的资源不存在！");break;case 405:h.error("请求方式错误！请您稍后重试");break;case 408:h.error("请求超时！请您稍后重试");break;case 423:const t=zr();h.error(t.sysBaseInfoGet.SYS_WEB_CLOSE_PROMPT);break;case 500:h.error("服务异常！");break;case 502:h.error("网关错误！");break;case 503:h.error("服务不可用！");break;case 504:h.error("网关超时！");break;default:h.error("请求失败！")}},ke=new pt;class Fr{constructor(t){v(this,"service");v(this,"apiNameArray",["add","edit","grant","batch","update"]);v(this,"noMessageApiNameArray",[]);this.service=mt.create(t),this.setInterceptor()}setInterceptor(){this.service.interceptors.request.use(t=>{const r=M();if(t.cancel??(t.cancel=!0),t.cancel&&ke.addPending(t),t.loading??(t.loading=!0),t.loading&&St(),t.headers&&typeof t.headers.set=="function"){const{accessToken:o,refreshToken:i}=r;if(o){t.headers.set(C.TOKEN_NAME,C.TOKEN_PREFIX+o);const n=this.decryptJWT(o),c=this.getJWTDate(n.exp);if(new Date>=c){const a=i;a&&t.headers.set("X-"+C.TOKEN_NAME,C.TOKEN_PREFIX+a)}}}return t.method==="get"&&(t.params={...t.params,_t:new Date().getTime()}),t},t=>Promise.reject(t)),this.service.interceptors.response.use(t=>{this.checkAndStoreAuthentication(t);const{data:r,config:o}=t,i=M();if(ke.removePending(o),o.loading&&Se(),r.code==ne.OVERDUE)return i.clearUserStore(),y.replace(He),h.error(r.msg),Promise.reject(r);if(r.code&&r.code!==ne.SUCCESS)return h.error(r.msg),Promise.reject(r);{const n=t.config.url||"";this.apiNameArray.forEach(c=>{let a=n.split("/"),u=a[a.length-1];!this.noMessageApiNameArray.includes(u)&&n.includes(c)&&h.success(r.msg)})}return r},async t=>{const{response:r}=t;return Se(),t.message.indexOf("timeout")!==-1&&h.error("请求超时！请您稍后重试"),t.message.indexOf("Network Error")!==-1&&h.error("网络错误！请您稍后重试"),r&&Nr(r.status),window.navigator.onLine||y.replace("/500"),Promise.reject(t)})}get(t,r,o={}){return this.service.get(t,{params:r,...o})}post(t,r,o={}){return this.service.post(t,r,o)}put(t,r,o={}){return this.service.put(t,r,o)}delete(t,r,o={}){return this.service.delete(t,{params:r,...o})}download(t,r,o={}){return this.service.post(t,r,{...o,responseType:"blob"})}decryptJWT(t){t=t.replace(/_/g,"/").replace(/-/g,"+");const r=decodeURIComponent(escape(window.atob(t.split(".")[1])));return JSON.parse(r)}getJWTDate(t){return new Date(t*1e3)}checkAndStoreAuthentication(t){let r=t.headers[C.ACCESS_TOKEN_KEY],o=t.headers[C.REFRESH_TOKEN_KEY];const i=M();r==="invalid_token"?i.clearToken():o&&r&&r!=="invalid_token"&&i.setToken(r,o)}}function Gr(e){const t=new Fr(e);async function r(a,u,l={}){return t.get(a,u,l)}async function o(a,u,l={}){return t.post(a,u,l)}async function i(a,u,l={}){return t.put(a,u,l)}async function n(a,u,l={}){return t.delete(a,u,l)}async function c(a,u,l={}){return t.download(a,u,{...l,responseType:"blob"})}return{get:r,post:o,put:i,delete:n,download:c}}const Hr=window.require&&window.require("electron")||{},ce=Hr.ipcRenderer||void 0,te={SUCCESS:0},rt="simple-debug",ot={id:rt,state:()=>({debugIndex:{asideIndex:"1",currentComponent:new Map,compName:"装置信息",compData:new Map},currDevice:{id:"",ip:"",name:"",port:"",encrypted:!1,prjType:void 0,deviceType:void 0,isConnect:!1,isActive:!1,connectTime:""},searchDevice:"",deviceList:[],report:new Map,consoleLog:[]}),getters:{debugIndexGet:e=>e.debugIndex},actions:{setDebugState(...e){this.$patch({[e[0]]:e[1]})},setSearchDevice(e){this.searchDevice=e},setCurrDevice(e){this.currDevice=e},async addDevice(e){const t=await me.addDeviceCfg(e);t.code==te.SUCCESS&&(this.deviceList.push(t.data),this.addConsole("装置"+e.name+"：添加成功"))},async updateDevice(e){const t=await me.updateDeviceCfg(e.id,e);if(t.code==te.SUCCESS){const r=this.deviceList.findIndex(o=>o.id===e.id);r>=te.SUCCESS&&(this.deviceList.splice(r,1,e),this.addConsole("装置"+e.name+"：更新完成"))}else this.addConsole(t.msg)},async removeDevice(e){const t=await me.removeDeviceCfg(e);if(t.code==te.SUCCESS){const r=this.deviceList.findIndex(o=>o.id===e);if(r>=0){const o=this.deviceList[r];this.addConsole("装置"+o.name+"：删除完成"),this.deviceList.splice(r,1)}}else this.addConsole(t.msg)},disconnectAllDevices(){this.deviceList.forEach(e=>{e.isConnect&&(e.isConnect=!1,this.addConsole(`装置${e.name}已断开连接`))})},initReportData(e){const t={currReportType:"",currReportMethod:"",currReportDesc:"",newname:"",keyword:"",isReportLoading:!1,commonReport:new Map,operateReport:new Map,groupReport:new Map,auditlogReport:new Map,queryConditions:new Map};this.report.set(e,t)},removeReportData(e){this.report.delete(e)},addConsole(e){var r;if(pe.isEmpty(e))return;const t=tr()+"："+e;this.consoleLog.length>1e4&&((r=this.consoleLog)==null||r.shift()),console.log(t),this.consoleLog.push(t)},clearConsole(){this.consoleLog.splice(0,this.consoleLog.length)}}},Ur=w(rt,ot),Jo=e=>w(e,ot);function Wr(e){const t=e;async function r(n,c={}){return ce.invoke(t+n,c)}async function o(n,c={}){const u={head:{id:Ur().currDevice.id},data:c};return ce.invoke(t+n,u)}async function i(n,c={},a){const u={head:{id:a},data:c};return ce.invoke(t+n,u)}return{invoke:r,iecInvoke:o,iecInvokeWithDevice:i}}const _=(e,t="/api")=>Gr({baseURL:t+e,timeout:ne.TIMEOUT,withCredentials:!0}),L=e=>Wr(e),$=_("/sys/auth/b/"),qo={login(e){return $.post("login",e,{loading:!1})},picCaptcha(){return $.get("getPicCaptcha",{},{loading:!1})},logout(e){return $.post("logout",e)},getLoginUser(){return $.get("getLoginUser",{},{loading:!1})},getPhoneValidCode(e){return $.get("getPhoneValidCode",e,{loading:!1})},loginByPhone(e){return $.post("loginByPhone",e,{loading:!1})}},E=_("/userCenter/"),ge={getAuthMenuList(e){return E.get("loginMenu",e,{loading:!1})},setDefaultModule(e){E.post("setDefaultModule",e,{loading:!1})},updatePassword(e){return E.post("updatePassword",e)},updateSignature(e){return E.post("updateSignature",e,{loading:!1})},updateAvatar(e){return E.post("updateAvatar",e,{loading:!1})},updateUserInfo(e){return E.post("updateUserInfo",e,{loading:!1})},updateUserWorkbench(e){return E.post("updateUserWorkbench",e,{loading:!1})},loginWorkbench(){return E.get("loginWorkbench",{},{loading:!1})},shortcutTree(){return E.get("shortcutTree",{},{loading:!1})},unReadCount(){return E.get("unReadCount",{},{loading:!1})},newUnRead(){return E.get("newUnRead",{},{loading:!1})},myMessagePage(e){return E.get("myMessagePage",e,{loading:!1})},myMessageDetail(e){return E.get("myMessageDetail",e)},setRead(e){return E.post("setRead",e)},allRead(e){return E.post("setRead",e)},allDelete(e){return E.post("setDelete",e)},setDelete(e){return E.post("setDelete",e)}},re=_("/sys/limit/spa/"),Xo={page(e){return re.get("page",e)},detail(e){return re.get("detail",e)},submitForm(e={},t=!1){return re.post(t?"edit":"add",e)},delete(e){return re.post("delete",e)}},Y=_("/sys/limit/module/"),Qo={page(e){return Y.get("page",e)},detail(e){return Y.get("detail",e)},submitForm(e={},t=!1){return Y.post(t?"edit":"add",e)},delete(e){return Y.post("delete",e)},list(){return Y.get("list")}},z=_("/sys/limit/menu/"),Zo={tree(e){return z.get("tree",e)},menuTreeSelector(e){return z.get("menuTreeSelector",e)},detail(e){return z.get("detail",e)},submitForm(e={},t=!1){return z.post(t?"edit":"add",e)},delete(e){return z.post("delete",e)},changeModule(e){return z.post("changeModule",e)}},j=_("/sys/limit/button/"),ei={page(e){return j.get("page",e)},detail(e){return j.get("detail",e)},submitForm(e={},t=!1){return j.post(t?"edit":"add",e)},delete(e){return j.post("delete",e)},batch(e){return j.post("batch",e)}},T=_("/sys/limit/role/"),ti={page(e){return T.get("page",e)},tree(){return T.get("tree",{},{loading:!1})},detail(e){return T.get("detail",e)},submitForm(e={},t=!1){return T.post(t?"edit":"add",e)},delete(e){return T.post("delete",e)},resourceTreeSelector(){return T.get("resourceTreeSelector",{},{loading:!1})},permissionTreeSelector(e){return T.get("permissionTreeSelector",e,{loading:!1})},ownResource(e){return T.get("ownResource",e,{loading:!1})},grantResource(e){return T.post("grantResource",e)},ownPermission(e){return T.get("ownPermission",e,{loading:!1})},grantPermission(e){return T.post("grantPermission",e)},ownUser(e){return T.get("ownUser",e,{loading:!1})},grantUser(e){return T.post("grantUser",e)},roleSelector(e){return T.get("roleSelector",e,{loading:!1})}},B=_("/sys/ops/config/"),ri={sysBaseList(){return B.get("sysBaseList",{},{loading:!1})},list(){return B.get("list",{},{loading:!1,cancel:!1})},configEditForm(e){return B.post("editBatch",e)},page(e){return B.get("page",e)},delete(e){return B.post("delete",e)},submitForm(e={},t=!1){return B.post(t?"edit":"add",e)}},K=_("/sys/ops/dict/"),Yr={tree(){return K.get("tree",{},{loading:!1})},page(e){return K.get("page",e)},detail(e){return K.get("detail",e)},submitForm(e={},t=!1){return K.post(t?"edit":"add",e)},delete(e){return K.post("delete",e)}};_("/sys/audit/logVisit/");_("/sys/audit/logOperate/");const N=_("/sys/organization/org/"),oi={page(e){return N.get("page",e)},tree(){return N.get("tree")},detail(e){return N.get("detail",e)},submitForm(e={},t=!1){return N.post(t?"edit":"add",e)},delete(e){return N.post("delete",e)},copy(e={}){return N.post("copy",e)}},D=_("/sys/organization/user/"),ii={page(e){return D.get("page",e)},selector(e){return D.get("selector",e)},detail(e){return D.get("detail",e)},submitForm(e={},t=!1){return D.post(t?"edit":"add",e)},delete(e){return D.post("delete",e)},updateStatus(e,t){return D.post(t===_e.DISABLE?"disableUser":"enableUser",e)},resetPassword(e){return D.post("resetPassword",e)},ownRole(e){return D.get("ownRole",e)},grantRole(e){return D.post("grantRole",e)},permissionTreeSelector(e){return D.get("permissionTreeSelector",e,{loading:!1})},ownResource(e){return D.get("ownResource",e,{loading:!1})},grantResource(e){return D.post("grantResource",e)},ownPermission(e){return D.get("ownPermission",e,{loading:!1})},grantPermission(e){return D.post("grantPermission",e)}},F=_("/sys/organization/position/"),si={page(e){return F.get("page",e)},tree(){return F.get("tree",{},{loading:!1})},detail(e){return F.get("detail",e)},submitForm(e={},t=!1){return F.post(t?"edit":"add",e)},delete(e){return F.post("delete",e)},selector(){return F.get("selector",{},{loading:!1})}};/**
 * @description 文件上传
 * @license Apache License Version 2.0
 */_("/sys/upload/");const ue=_("/sys/"),jr={sysInfo(){return ue.get("sysInfo",{},{loading:!1,cancel:!1})},loginPolicy(){return ue.get("loginPolicy",{},{loading:!1,cancel:!1})},tenantList(){return ue.get("tenantList",{},{loading:!1,cancel:!1})}};_("/mqtt/");_("/sys/dev/message/");const le=L("controller/common/language/"),ni={setLanguage(e){return le.invoke("setLanguage",e)},getCurrentLanguage(){return le.invoke("getCurrentLanguage")},syncLanguage(e){return le.invoke("syncLanguage",e)}},G=L("controller/common/window/"),ai={closeWindow(e){return G.invoke("closeWindow",e)},maximizeWindow(){return G.invoke("maximizeWindow")},minimizeWindow(){return G.invoke("minimizeWindow")},openDevTools(){return G.invoke("openDevTools")},printScreen(){return G.invoke("printScreen")},dragWindow(){return G.invoke("dragWindow")}},H=L("controller/debug/variable/"),ci={getVariableByDevice(e,t){return H.iecInvokeWithDevice("getVariable",t,e)},addVariableByDevice(e,t){return H.iecInvokeWithDevice("addVariable",t,e)},modifyVariableByDevice(e,t){return H.iecInvokeWithDevice("modifyVariable",t,e)},deleteVariableByDevice(e,t){return H.iecInvokeWithDevice("deleteVariable",t,e)},importVariableByDevice(e,t){return H.iecInvokeWithDevice("importVariable",t,e)},exportVariableByDevice(e,t){return H.iecInvokeWithDevice("exportVariable",t,e)}},I=L("controller/debug/report/"),ui={getCommonReportListByDevice(e,t){return I.iecInvokeWithDevice("getCommonReportList",t,e)},getGroupReportListByDevice(e,t){return I.iecInvokeWithDevice("getGroupReportList",t,e)},getOperateReportListByDevice(e,t){return I.iecInvokeWithDevice("getOperateReportList",t,e)},getAuditReportListByDevice(e,t){return I.iecInvokeWithDevice("getAuditLogList",t,e)},refreshReportByDevice(e,t){return I.iecInvokeWithDevice("refreshReport",t,e)},refreshGroupReportByDevice(e,t){return I.iecInvokeWithDevice("refreshGroupReport",t,e)},refreshTripReportByDevice(e,t){return I.iecInvokeWithDevice("refreshTripReport",t,e)},refreshOperateReportByDevice(e,t){return I.iecInvokeWithDevice("refreshOperateReport",t,e)},exportCommonReportByDevice(e,t){return I.iecInvokeWithDevice("exportCommonReport",t,e)},uploadWaveByDevice(e,t){return I.iecInvokeWithDevice("uploadWave",t,e)},cancelUploadByDevice(e,t){return I.iecInvokeWithDevice("cancelUpload",t,e)},openWaveFile(e){return I.invoke("openWaveFile",e)}},de=L("controller/debug/debuginfomenu/"),oe=L("controller/debug/deviceconnect/"),ie=L("controller/debug/deviceoperate/"),li={getDeviceMenuTree(e){return de.invoke("getDeviceMenuTree",e)},getDeviceMenuItemByDevice(e,t){return de.iecInvokeWithDevice("getTreeItemByName",t,e)},getGroupInfoListByDevice(e,t){return de.iecInvokeWithDevice("getGroupInfoList",t,e)}},di={deviceConnectByRpc(e,t){return oe.invoke("connectDeviceByRpc",JSON.stringify(t))},disconnectDevice(e){return oe.iecInvokeWithDevice("deviceDisconnect",e,e)},startMessageMonitor(e){return oe.iecInvokeWithDevice("startMessageMonitor",{},e)},stopMessageMonitor(e){return oe.iecInvokeWithDevice("stopMessageMonitor",{},e)}},me={getDeviceCfgList(){return ie.invoke("getDeviceCfgList")},addDeviceCfg(e){return ie.invoke("addDeviceCfg",e)},removeDeviceCfg(e){return ie.iecInvokeWithDevice("removeDeviceCfg",void 0,e)},updateDeviceCfg(e,t){return ie.iecInvokeWithDevice("updateDeviceCfg",t,e)}},R=L("controller/debug/param/"),mi={getParamByDevice(e,t){return R.iecInvokeWithDevice("getParam",t,e)},getAllParamByDevice(e,t){return R.iecInvokeWithDevice("getAllParam",t,e)},confirmParamByDevice(e,t){return R.iecInvokeWithDevice("confirmParam",t,e)},importParamByDevice(e,t){return R.iecInvokeWithDevice("importParam",t,e)},getDiffParamByDevice(e,t){return R.iecInvokeWithDevice("getDiffParam",t,e)},getAllDiffParamByDevice(e,t){return R.iecInvokeWithDevice("getAllDiffParam",t,e)},exportParamByDevice(e,t){return R.iecInvokeWithDevice("exportParam",t,e)},exportAllParamByDevice(e,t){return R.iecInvokeWithDevice("exportAllParam",t,e)},getCurrentRunAreaByDevice(e){return R.iecInvokeWithDevice("getCurrentRunArea",void 0,e)},selectRunAreaByDevice(e,t){return R.iecInvokeWithDevice("selectRunArea",t,e)}},Ve=L("controller/debug/devicetime/"),pi={getDeviceTimeByDevice(e){return Ve.iecInvokeWithDevice("getDeviceTime",void 0,e)},writeDeviceTimeByDevice(e,t){return Ve.iecInvokeWithDevice("writeDeviceTime",t,e)}},xe=L("controller/debug/deviceinfo/"),hi={getDeviceInfoByDevice(e,t){return xe.iecInvokeWithDevice("getDeviceInfo",t,e)},exportDeviceInfoByDevice(e,t){return xe.iecInvokeWithDevice("exportDeviceInfo",t,e)}},$e=L("controller/debug/realevent/"),gi={subRealEventByDevice(e,t){return $e.iecInvokeWithDevice("subRealEvent",t,e)},unSubRealEventByDevice(e,t){return $e.iecInvokeWithDevice("unSubRealEvent",t,e)}},A=L("controller/debug/custominfo/"),vi={getAllGroupsByDevice(e){return A.iecInvokeWithDevice("getAllGroups",void 0,e)},validateMenuNameByDevice(e,t){return A.iecInvokeWithDevice("validateMenuName",{name:t},e)},addMenuByDevice(e,t){return A.iecInvokeWithDevice("addMenu",{group:t},e)},editMenuByDevice(e,t,r){return A.iecInvokeWithDevice("editMenu",{uuid:t,newGroup:r},e)},deleteMenuByDevice(e,t){return A.iecInvokeWithDevice("deleteMenu",{uuid:t},e)},addReportByDevice(e,t,r){return A.iecInvokeWithDevice("addReport",{groupUuid:t,report:r},e)},editReportByDevice(e,t,r,o){return A.iecInvokeWithDevice("editReport",{groupUuid:t,reportUuid:r,newReport:o},e)},deleteReportByDevice(e,t,r){return A.iecInvokeWithDevice("deleteReport",{groupUuid:t,reportUuid:r},e)},getLGReportsByDevice(e){return A.iecInvokeWithDevice("getLGReports",void 0,e)},getFcListByDevice(e){return A.iecInvokeWithDevice("getFcList",void 0,e)},getMenusByFcByDevice(e,t){return A.iecInvokeWithDevice("getMenusByFc",{fc:t},e)}},ze=L("controller/debug/dictconfig/"),_i={getProjectDictByDevice(e,t){return ze.iecInvokeWithDevice("getProjectDict",t,e)},setProjectDictByDevice(e,t){return ze.iecInvokeWithDevice("setProjectDict",t,e)}},P=L("controller/matrix/matrix/"),fi={importDeviceList(e){return console.log("matrixApi.importDeviceList"),P.iecInvoke("importDeviceList",e)},exportDeviceList(e){return console.log("matrixApi.exportDeviceList"),P.iecInvoke("exportDeviceList",e)},importDownloadList(e){return console.log("matrixApi.importDownloadList"),P.iecInvoke("importDownloadList",e)},exportDownloadList(e){return console.log("matrixApi.exportDownloadList"),P.iecInvoke("exportDownloadList",e)},importParamList(e){return console.log("matrixApi.importParamList"),P.iecInvoke("importParamList",e)},exportParanList(e){return console.log("matrixApi.exportParanList"),P.iecInvoke("exportParanList",e)},runTask(e){return console.log("matrixApi.runTask"),P.iecInvoke("runTask",e)},handlePackage(e){return P.iecInvoke("handlePackage",e)}};var Be;(e=>{(t=>{t.SCOPE_ALL="SCOPE_ALL",t.SCOPE_ORG_CHILD="SCOPE_ORG_CHILD",t.SCOPE_ORG="SCOPE_ORG",t.SCOPE_SELF="SCOPE_SELF",t.SCOPE_ORG_DEFINE="SCOPE_ORG_DEFINE"})(e.DataScopeEnum||(e.DataScopeEnum={})),e.dataScopeOptions=[{level:5,title:"全部",scopeCategory:"SCOPE_ALL",scopeDefineOrgIdList:[]},{level:4,title:"所属组织及以下",scopeCategory:"SCOPE_ORG_CHILD",scopeDefineOrgIdList:[]},{level:2,title:"所属组织",scopeCategory:"SCOPE_ORG",scopeDefineOrgIdList:[]},{level:1,title:"仅自己",scopeCategory:"SCOPE_SELF",scopeDefineOrgIdList:[]},{level:3,title:"自定义",scopeCategory:"SCOPE_ORG_DEFINE",scopeDefineOrgIdList:[]}]})(Be||(Be={}));var Ne;(e=>{(t=>{t.TYPE_ALL="all",t.TYPE_DEVICE="device",t.TYPE_CONFIGURE="configure"})(e.ConfigTypeEnum||(e.ConfigTypeEnum={}))})(Ne||(Ne={}));/**
 * @description 全局代码错误捕捉
 * @license Apache License Version 2.0
 */const Ei=e=>{if(e.status||e.status==0)return!1;let r={InternalError:"Javascript引擎内部错误",ReferenceError:"未找到对象",TypeError:"使用了错误的类型或对象",RangeError:"使用内置对象时，参数超范围",SyntaxError:"语法错误",EvalError:"错误的使用了Eval",URIError:"URI错误"}[e.name]||"未知错误";U({title:r,message:e,type:"error",duration:3e3}),console.log("[ error ] >",e)},Si=(e,t={},r=!0,o,i,n="default")=>{const c=()=>{const p=Qt(`proTable_pageSize_${location.pathname}_${n}`);return p&&typeof p=="number"?p:15},a=p=>{Zt(`proTable_pageSize_${location.pathname}_${n}`,p)},u=gt({tableData:[],pageable:{pageNum:1,pageSize:c(),total:0},searchParam:{},searchInitParam:{},totalParam:{}}),l=Ge({get:()=>({pageNum:u.pageable.pageNum,pageSize:u.pageable.pageSize}),set:p=>{console.log("我是分页更新之后的值",p)}}),S=async()=>{if(e)try{Object.assign(u.totalParam,t,r?l.value:{});let{data:p}=await e({...u.searchInitParam,...u.totalParam});o&&o(p),u.tableData=r?p.list:p,r&&(u.pageable.total=p.total)}catch(p){i&&i(p)}},k=()=>{u.totalParam={};let p={};for(let W in u.searchParam)(u.searchParam[W]||u.searchParam[W]===!1||u.searchParam[W]===0)&&(p[W]=u.searchParam[W]);Object.assign(u.totalParam,p)},d=()=>{u.pageable.pageNum=1,k(),S()},m=()=>{u.pageable.pageNum=1,u.searchParam={...u.searchInitParam},k(),S()},g=p=>{u.pageable.pageNum=1,u.pageable.pageSize=p,a(p),S()},b=p=>{u.pageable.pageNum=p,S()};return{...vt(u),getTableList:S,search:d,reset:m,handleSizeChange:g,handleCurrentChange:b,updatedTotalParam:k}},bi=(e="id")=>{const t=Ee(!1),r=Ee([]),o=Ge(()=>{let n=[];return r.value.forEach(c=>n.push(c[e])),n});return{isSelected:t,selectedList:r,selectedListIds:o,selectionChange:n=>{n.length?t.value=!0:t.value=!1,r.value=n}}},Ti=(e,t,r,o)=>{if(console.log("getComponentByMenuName:",e,t,r),e=="Root")return V(so);if(r==="customGroup")return V(uo);if(t)return V(ao);if(r=="LG"&&o){const a={QueryHisEvtByTime:Jr,QueryHisFaultByTime:qr,QueryOpReportByTime:Xr,QueryAuditLogByTime:Qr}[o];return a?V(a):(console.warn("getComponentByMenuName: 未找到对应的LG报告组件",o),null)}const n={ALL_SP:no,ALL_SG:co,DC:Kr,CO:Zr,ST:eo,MX:to,AO:ro,CI:oo,BO:io,SP:Fe,SG:Fe}[r];return n?V(n):(console.warn("getComponentByMenuName: 未找到对应的功能组件",r),V(lo))},Di=e=>{const t=e.getFullYear(),r=String(e.getMonth()+1).padStart(2,"0"),o=String(e.getDate()).padStart(2,"0"),i=String(e.getHours()).padStart(2,"0"),n=String(e.getMinutes()).padStart(2,"0"),c=String(e.getSeconds()).padStart(2,"0"),a=String(e.getMilliseconds()).padStart(3,"0");return`${t}-${r}-${o} ${i}:${n}:${c}.${a}`},Li=e=>(e.setHours(e.getHours()+8),e.toISOString().replace(/[-:.TZ]/g,"").replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})(\d{3})/,"$1-$2-$3T$4:$5:$6.$7000+0800"));function f(e){return _t({loader:e,loadingComponent:lt,delay:200})}const Kr=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.L),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),Jr=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.Y),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),qr=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.Z),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),Xr=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e._),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),Qr=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.X),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),Fe=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.O),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),Zr=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.R),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),eo=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.T),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),to=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.U),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),ro=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.W),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),oo=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.V),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),io=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.Q),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),so=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.k),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),no=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.J),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),ao=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.N),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),co=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.I),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),uo=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.K),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)),lo=f(()=>s(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.bb),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url));class Ii{static sum(t,r){return t+r}static formatBytes(t,r=2){if(t===0)return"0 Bytes";const o=1024,i=["Bytes","KB","MB","GB","TB"],n=Math.floor(Math.log(t)/Math.log(o));return`${parseFloat((t/Math.pow(o,n)).toFixed(r))} ${i[n]}`}}const wi=ft();class Ai{static ipToNumber(t){if(!this.validateIp(t))throw new Error(t+": Invalid IPv4 format");const r=t.split(".").map(Number);return(parseInt(r[0],10)<<24|parseInt(r[1],10)<<16|parseInt(r[2],10)<<8|parseInt(r[3],10))>>>0}static numberToIp(t){if(t<0||t>4294967295)throw new RangeError("Number out of IPv4 range: "+t);return[t>>>24&255,t>>>16&255,t>>>8&255,t&255].join(".")}static validateIp(t){return/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(t)}}const mo=()=>Math.random(),Ri=()=>`id-${mo().toString(36).substring(2,12)}`;function yi(e){try{e();return}catch(t){return ee.isString(t)?t:ee.isError(t)||ee.isObject(t)&&ee.has(t,"message")?t.message:"An error as occurred."}}function Pi(e,t){try{return e()}catch{return t}}/**
 * @description  表单验证规则
 * @license Apache License Version 2.0
 */const Ci=(e,t=["blur","change"])=>({required:!0,message:e,trigger:t});/**
 * @description smCrypto 加密解密工具
 * @license Apache License Version 2.0
 */const{sm2:po}=Et,ho=0,go="04BD62406DF6789B1FBE8C457AECAE6D7C806CDB39316F190519905C24DF395E8952C47798D76ADECF8CA28C935702AFCDD9B17DE77121FA6448F0EDEFBD8365D6",Oi={doSm2Encrypt(e){return po.doEncrypt(e,go,ho)}};/**
 * @description 计算
 * @license Apache License Version 2.0
 */function Mi(e,t="px"){return Kt(e)||Jt(e)&&e.indexOf(t)===-1?`${e}${t}`:e}/**
 * @description 操作单条数据信息
 * @license Apache License Version 2.0
 */const ki=(e,t={},r,o="warning")=>new Promise((i,n)=>{dt.confirm(`是否${r}?`,"温馨提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:o,draggable:!0}).then(async()=>{const c=await e(t);if(!c)return n(!1);h({type:"success",message:`${r}成功!`}),i(c)}).catch(()=>{})});async function vo(){x(Tt),x(Dt),x(Lt),x(It),x(wt),x(At)}const Vi=Object.freeze(Object.defineProperty({__proto__:null,downloadAndInstall:vo},Symbol.toStringTag,{value:"Module"}));export{Ko as $,q as A,ci as B,jt as C,Di as D,pi as E,ce as F,fi as G,se as H,Ro as I,_i as J,Ao as K,vi as L,Ii as M,li as N,Ti as O,wi as P,di as Q,me as R,mi as S,Ai as T,hi as U,Vo as V,Li as W,ui as X,yo as Y,gi as Z,s as _,ve as a,Wo as a0,Q as a1,Ri as a2,xo as a3,yi as a4,tt as a5,Ci as a6,qo as a7,Wt as a8,jr as a9,Ei as aA,y as aB,Ho as aC,Er as aD,Uo as aE,Yo as aF,Vi as aG,Ft as aa,Oi as ab,Mi as ac,Ce as ad,Yt as ae,ki as af,ri as ag,ei as ah,Zo as ai,jo as aj,Gt as ak,Ht as al,M as am,_e as an,Qo as ao,Be as ap,oi as aq,ti as ar,Ut as as,ii as at,si as au,Xo as av,he as aw,lr as ax,J as ay,Ne as az,No as b,Fo as c,Go as d,Io as e,ai as f,Po as g,zo as h,Bo as i,Ur as j,Jo as k,ni as l,L as m,Mo as n,Zt as o,ko as p,Co as q,Oo as r,wo as s,bi as t,or as u,Si as v,Pi as w,Qt as x,$o as y,zr as z};
