export default {
  // 通用操作
  common: {
    add: "Ajouter",
    index: "Index",
    delete: "Supprimer",
    clear: "Effacer",
    import: "Importer",
    export: "Exporter",
    execute: "Exécuter",
    moveUp: "Monter",
    moveDown: "Descendre",
    loading: "Chargement...",
    success: "Succès",
    failed: "Échec",
    confirm: "Confirmer",
    cancel: "Annuler",
    yes: "Oui",
    no: "Non",
    operation: "Opération",
    tips: "Astuce",
    title: "Conseil"
  },

  // 搜索相关
  search: {
    placeholder: "Rechercher une fonction"
  },

  // 功能列表相关
  functionList: {
    unnamedDevice: "Appareil sans nom",
    batchDownload: {
      name: "Téléchargement en lot",
      desc: "Téléchargement de fichiers en lot multi-appareils et import de valeurs fixes en lot"
    },
    xmlFormatter: {
      name: "Formateur XML",
      desc: "Organisation hiérarchique rapide des données XML"
    },
    jsonFormatter: {
      name: "Formateur JSON",
      desc: "Formatage intelligent des données JSON avec validation syntaxique"
    },
    radixConverter: {
      name: "Convertisseur de base",
      desc: "Support de conversion entre binaire, décimal, hexadécimal et autres bases"
    },
    temperatureConverter: {
      name: "Convertisseur de température",
      desc: "Conversion intelligente entre Celsius, Fahrenheit, Kelvin et autres unités de température"
    },
    encryption: {
      name: "Chiffrement/déchiffrement de texte",
      desc: "Chiffrement et déchiffrement rapide de texte basé sur les algorithmes AES, RSA, Base64, etc."
    },
    packageProgram: {
      name: "Emballage du programme",
      desc: "Emballer les fichiers d'exécution de l'appareil pour l'exportation, supporte le répertoire de sauvegarde personnalisé et la localisation du dossier en un clic"
    }
  },

  // 矩阵内容相关
  matrixContent: {
    loading: "Chargement...",
    tabs: {
      deviceList: "Appareils",
      downloadConfig: "Fichiers",
      paramConfig: "Paramètres"
    }
  },

  // Étapes de la tâche
  taskSteps: {
    connect: "Connecter",
    download: "Télécharger",
    import: "Importer",
    disconnect: "Déconnecter",
    complete: "Terminer"
  },

  // Messages de tâches
  messages: {
    connectDevice: "Connecter l'Appareil",
    executeFileDownload: "Exécuter le Téléchargement de Fichier",
    downloadingFile: "Téléchargement de Fichier",
    downloadFileFailed: "Échec du Téléchargement de Fichier",
    fileDownloadCompleted: "Téléchargement de Fichier Terminé",
    executeParamImport: "Exécuter l'Importation de Paramètres",
    paramValidationFailed: "Validation des Paramètres Échouée",
    paramImportFailed: "Importation des Paramètres Échouée",
    paramImportCompleted: "Importation des Paramètres Terminée",
    taskCompleted: "Tâche Terminée",
    deviceConnectionFailed: "Connexion de l'Appareil Échouée",
    deviceRebootSuccess: "Redémarrage de l'Appareil Réussi"
  },

  // 装置列表
  deviceList: {
    title: "Appareils",
    deviceListExcel: "Liste des appareils.xlsx",
    exportDeviceList: "Exporter la liste des appareils",
    importDeviceList: "Importer la liste des appareils",
    exportSuccess: "Export de la liste des appareils réussi",
    exportFail: "Échec de l'export de la liste des appareils",
    importSuccess: "Import de la liste des appareils réussi",
    importFail: "Échec de l'import de la liste des appareils",
    exportSuccessMsg: "Export de la liste des appareils réussi",
    exportFailMsg: "Échec de l'export de la liste des appareils",
    importSuccessMsg: "Import de la liste des appareils réussi",
    importFailMsg: "Échec de l'import de la liste des appareils : {msg}",
    deviceName: "Nom de l'appareil",
    deviceAddress: "Adresse de l'appareil",
    devicePort: "Port de l'appareil",
    isEncrypted: "Chiffré",
    encrypted: "Chiffré",
    notEncrypted: "Non chiffré",
    status: "Statut",
    operation: "Opération",
    reboot: "Redémarrer",
    noReboot: "Ne pas redémarrer",
    addDevice: "Ajouter un appareil",
    deleteDevice: "Supprimer l'appareil",
    clearDevices: "Effacer les appareils",
    deviceExists: "L'appareil existe déjà",
    deviceDeleted: "Appareil {ip} supprimé",
    downloadFile: "Télécharger le fichier",
    importParam: "Importer les valeurs fixes",
    connectTimeout: "Délai d'expiration de connexion",
    paramTimeout: "Délai d'expiration de modification des valeurs fixes",
    readTimeout: "Délai d'expiration global des requêtes",
    progress: "Progression"
  },

  // 下载文件配置
  downList: {
    title: "Téléchargement",
    deviceDirectory: "Répertoire de l'appareil",
    fileName: "Nom du fichier",
    fileSize: "Taille du fichier",
    filePath: "Chemin du fichier",
    lastModified: "Dernière modification",
    addFile: "Ajouter un fichier à télécharger",
    addFolder: "Ajouter un dossier à télécharger",
    fileExists: "Le fichier {path} existe déjà, ajout échoué !",
    fileDeleted: "Fichier {path} supprimé",
    filesDeleted: "Fichiers supprimés",
    defaultExportFileName: "Liste de fichiers à télécharger.xlsx",
    exportTitle: "Exporter la liste des fichiers à télécharger",
    importTitle: "Importer la liste des fichiers à télécharger",
    exportSuccess: "Liste de fichiers exportée avec succès",
    exportFailed: "Échec de l'export de la liste de fichiers",
    importSuccess: "Liste de fichiers importée avec succès",
    importFailed: "Échec de l'import de la liste de fichiers",
    fileExistsMsg: "Le fichier {path} existe déjà, ajout échoué !"
  },

  // 参数定值配置
  paramList: {
    title: "Paramètres",
    paramGroup: "Groupe de valeurs fixes",
    groupName: "Nom du groupe",
    paramName: "Nom du paramètre",
    paramDesc: "Description du paramètre",
    paramValue: "Valeur du paramètre",
    minValue: "Valeur minimale",
    maxValue: "Valeur maximale",
    step: "Pas",
    unit: "Unité",
    searchParamName: "Nom du paramètre",
    searchParamDesc: "Description du paramètre",
    importSuccess: "Import des valeurs fixes de paramètres d'appareil réussi",
    importFailed: "Échec de l'import des valeurs fixes de paramètres d'appareil",
    exportSuccess: "Export de la liste des valeurs fixes d'appareil réussi",
    exportFailed: "Échec de l'export de la liste des valeurs fixes d'appareil",
    clearSuccess: "Liste des valeurs fixes effacée"
  },

  // 进度对话框
  progressDialog: {
    title: "En cours de traitement",
    pleaseWait: "Veuillez patienter..."
  },

  packageProgram: {
    saveDir: "Répertoire de sauvegarde",
    selectSaveDir: "Sélectionner le répertoire de sauvegarde",
    packageBtn: "Emballer",
    locateDir: "Localiser le dossier",
    delete: "Supprimer",
    sequence: "N°",
    fileName: "Nom du fichier",
    fileSize: "Taille du fichier",
    filePath: "Chemin du fichier",
    lastModified: "Dernière modification",
    operation: "Opération",
    saveDirEmpty: "Veuillez d'abord sélectionner le répertoire de sauvegarde !",
    packageSuccess: "Emballage du programme terminé !",
    tip: "Astuce",
    confirmButton: "OK",
    // Ajout import/export
    defaultExportFileName: "Liste des fichiers du package programme.xlsx",
    exportTitle: "Exporter la liste des fichiers du package programme",
    importTitle: "Importer la liste des fichiers du package programme",
    exportSuccess: "Liste de fichiers exportée avec succès",
    exportFailed: "Échec de l'export de la liste de fichiers",
    importSuccess: "Liste de fichiers importée avec succès",
    importFailed: "Échec de l'import de la liste de fichiers",
    fileExists: "Le fichier {path} existe déjà, ajout échoué !",
    selectDirSuccess: "Répertoire sélectionné : {dir}",
    locateDirSuccess: "Répertoire localisé : {dir}",
    addFileStart: "Ouverture du sélecteur de fichiers...",
    addFileSuccess: "{count} fichiers/dossiers ajoutés avec succès",
    addFileNone: "Aucun nouveau fichier/dossier ajouté",
    deleteSuccess: "{count} fichiers/dossiers supprimés avec succès",
    clearSuccess: "Tous les fichiers/dossiers ont été effacés",
    moveUpSuccess: "Déplacé vers le haut : {name}",
    moveDownSuccess: "Déplacé vers le bas : {name}",
    noFileSelected: "Veuillez d'abord sélectionner les fichiers à emballer !",
    noDeviceSelected: "Veuillez d'abord sélectionner les appareils à emballer !",
    packageFailed: "Échec de l'emballage : {msg}",
    openFileButton: "Ouvrir le fichier",
    zipPath: "Chemin du zip : {zipPath}"
  }
};
