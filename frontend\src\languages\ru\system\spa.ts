export default {
  title: "Управление одностраничными приложениями",
  list: {
    title: "Список одностраничных приложений",
    add: "Добавить одностраничное приложение",
    deleteSelected: "Удалить выбранные",
    deleteConfirm: "Вы уверены, что хотите удалить одностраничное приложение {title}?"
  },
  form: {
    title: "{opt} одностраничное приложение",
    basicSettings: "Основные настройки",
    functionSettings: "Функциональные настройки",
    name: "Название одностраничного приложения",
    type: "Тип одностраничного приложения",
    icon: "Иконка",
    path: "Адрес маршрута",
    pathPlaceholder: "Пожалуйста, заполните адрес маршрута, например: /home/<USER>",
    componentName: "Название компонента",
    componentPath: "Адрес компонента",
    linkPath: "Адрес ссылки",
    linkPathPlaceholder: "Пожалуйста, заполните адрес ссылки, например: http://www.baidu.com",
    sort: "Сортировка",
    description: "Описание",
    isHome: "Установить как главную страницу",
    isHide: "Скрыть страницу",
    isFull: "Полноэкранная страница",
    isAffix: "Закрепить вкладку",
    isKeepAlive: "Кэш маршрута",
    cancel: "Отмена",
    confirm: "Подтвердить",
    nameRequired: "Пожалуйста, введите название одностраничного приложения",
    typeRequired: "Пожалуйста, выберите тип одностраничного приложения",
    pathRequired: "Пожалуйста, введите адрес маршрута",
    componentNameRequired: "Пожалуйста, введите название компонента",
    componentPathRequired: "Пожалуйста, введите адрес компонента",
    sortRequired: "Пожалуйста, введите сортировку",
    iconRequired: "Пожалуйста, выберите иконку"
  }
};
