export default {
  common: {
    // Общие кнопки операций
    buttons: {
      add: "Добавить",
      edit: "Редактировать",
      delete: "Удалить",
      save: "Сохранить",
      cancel: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      confirm: "Подтвердить",
      search: "Поиск",
      reset: "Сбро<PERSON>",
      import: "Импорт",
      export: "Экспорт",
      upload: "Загрузить",
      download: "Скачать",
      preview: "Предварительный просмотр",
      refresh: "Обновить",
      clear: "Очистить",
      moveUp: "Переместить вверх",
      moveDown: "Переместить вниз"
    },
    // Общие статусы
    status: {
      success: "Успешно",
      failed: "Не удалось",
      loading: "Загрузка",
      processing: "Обработка",
      completed: "Завершено",
      error: "Ош<PERSON>б<PERSON>а",
      warning: "Предупреждение",
      info: "Информация"
    },
    // Общие сообщения
    messages: {
      confirmDelete: "Вы уверены, что хотите удалить?",
      confirmClear: "Вы уверены, что хотите очистить?",
      confirmImport: "Вы уверены, что хотите импортировать?",
      confirmExport: "Вы уверены, что хотите экспортировать?",
      operationSuccess: "Операция успешна",
      operationFailed: "Операция не удалась",
      saveSuccess: "Сохранение успешно",
      saveFailed: "Сохранение не удалось",
      deleteSuccess: "Удаление успешно",
      deleteFailed: "Удаление не удалось",
      importSuccess: "Импорт успешен",
      importFailed: "Импорт не удался",
      exportSuccess: "Экспорт успешен",
      exportFailed: "Экспорт не удался",
      uploadSuccess: "Загрузка успешна",
      uploadFailed: "Загрузка не удалась",
      downloadSuccess: "Скачивание успешно",
      downloadFailed: "Скачивание не удалось",
      noData: "Нет данных",
      loading: "Загрузка...",
      processing: "Обработка...",
      pleaseWait: "Пожалуйста, подождите...",
      pleaseSelect: "Пожалуйста, выберите",
      pleaseInput: "Пожалуйста, введите",
      invalidInput: "Недопустимый ввод",
      networkError: "Ошибка сети",
      serverError: "Ошибка сервера",
      unknownError: "Неизвестная ошибка"
    },
    // Общая валидация форм
    validation: {
      required: "Это поле обязательно для заполнения",
      invalidFormat: "Неправильный формат",
      invalidLength: "Неправильная длина",
      invalidRange: "Неправильный диапазон",
      invalidValue: "Неправильное значение",
      invalidEmail: "Неправильный формат электронной почты",
      invalidPhone: "Неправильный формат номера телефона",
      invalidUrl: "Неправильный формат URL",
      invalidNumber: "Пожалуйста, введите число",
      invalidInteger: "Пожалуйста, введите целое число",
      invalidDecimal: "Пожалуйста, введите десятичное число",
      invalidDate: "Неправильный формат даты",
      invalidTime: "Неправильный формат времени",
      invalidDateTime: "Неправильный формат даты и времени",
      invalidIp: "Неправильный формат IP-адреса",
      invalidPort: "Неправильный формат номера порта",
      invalidPath: "Неправильный формат пути",
      invalidFileName: "Неправильный формат имени файла",
      invalidFileSize: "Размер файла превышает лимит",
      invalidFileType: "Тип файла не поддерживается"
    },
    // Общие столбцы таблицы
    columns: {
      index: "Номер",
      name: "Название",
      type: "Тип",
      status: "Статус",
      createTime: "Время создания",
      updateTime: "Время обновления",
      description: "Описание",
      operation: "Операция"
    },
    // Общие диалоги
    dialog: {
      title: "Подсказка",
      confirm: "Подтвердить",
      cancel: "Отмена",
      close: "Закрыть",
      maximize: "Развернуть",
      minimize: "Свернуть",
      restore: "Восстановить"
    },
    // Общие файловые операции
    file: {
      upload: "Загрузить файл",
      download: "Скачать файл",
      delete: "Удалить файл",
      preview: "Предварительный просмотр файла",
      rename: "Переименовать",
      move: "Переместить",
      copy: "Копировать",
      paste: "Вставить",
      cut: "Вырезать",
      select: "Выбрать файл",
      drag: "Перетащите файл сюда",
      drop: "Отпустите файл",
      size: "Размер файла",
      type: "Тип файла",
      name: "Имя файла",
      path: "Путь к файлу",
      lastModified: "Время последнего изменения",
      createTime: "Время создания"
    }
  }
};
