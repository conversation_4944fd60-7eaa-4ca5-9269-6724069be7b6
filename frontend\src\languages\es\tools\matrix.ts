export default {
  // 通用操作
  common: {
    add: "Agregar",
    index: "Índice",
    delete: "Eliminar",
    clear: "Limpiar",
    import: "Importar",
    export: "Exportar",
    execute: "Ejecutar",
    moveUp: "Mover arriba",
    moveDown: "Mover abajo",
    loading: "Cargando...",
    success: "Éxito",
    failed: "Fallido",
    confirm: "Confirmar",
    cancel: "Cancel<PERSON>",
    yes: "Sí",
    no: "No",
    operation: "Operación",
    tips: "Consejo",
    title: "Aviso"
  },

  // 搜索相关
  search: {
    placeholder: "Buscar función"
  },

  // 功能列表相关
  functionList: {
    unnamedDevice: "Dispositivo sin nombre",
    batchDownload: {
      name: "Descarga por lotes",
      desc: "Descarga de archivos de múltiples dispositivos e importación de valores por lotes"
    },
    xmlFormatter: {
      name: "Formato XML",
      desc: "Organización jerárquica rápida de datos XML"
    },
    jsonFormatter: {
      name: "Formato JSON",
      desc: "Formato inteligente de datos JSON, con validación de sintaxis"
    },
    radixConverter: {
      name: "Conversión de Base",
      desc: "Conversión entre binario, decimal, hexadecimal y otras bases"
    },
    temperatureConverter: {
      name: "Conversión de Temperatura",
      desc: "Conversión inteligente entre Celsius, Fahrenheit, Kelvin y otras unidades de temperatura"
    },
    encryption: {
      name: "Encriptación de Texto",
      desc: "Encriptación y desencriptación rápida de texto basada en algoritmos como AES, RSA, Base64, etc."
    },
    packageProgram: {
      name: "Empaquetado de programa",
      desc: "Empaqueta los archivos de ejecución del dispositivo para exportar, soporta directorio de guardado personalizado y ubicación de carpeta con un clic"
    }
  },

  // 矩阵内容相关
  matrixContent: {
    loading: "Cargando...",
    tabs: {
      deviceList: "Dispositivos",
      downloadConfig: "Archivos",
      paramConfig: "Parámetros"
    }
  },

  // Pasos de la tarea
  taskSteps: {
    connect: "Conectar",
    download: "Descargar",
    import: "Importar",
    disconnect: "Desconectar",
    complete: "Completar"
  },

  // Mensajes de tareas
  messages: {
    connectDevice: "Conectar Dispositivo",
    executeFileDownload: "Ejecutar Descarga de Archivo",
    downloadingFile: "Descargando Archivo",
    downloadFileFailed: "Descarga de Archivo Falló",
    fileDownloadCompleted: "Descarga de Archivo Completada",
    executeParamImport: "Ejecutar Importación de Parámetros",
    paramValidationFailed: "Validación de Parámetros Falló",
    paramImportFailed: "Importación de Parámetros Falló",
    paramImportCompleted: "Importación de Parámetros Completada",
    taskCompleted: "Tarea Completada",
    deviceConnectionFailed: "Conexión de Dispositivo Falló",
    deviceRebootSuccess: "Reinicio de Dispositivo Exitoso"
  },

  // 装置列表
  deviceList: {
    title: "Dispositivos",
    deviceListExcel: "Lista de Dispositivos.xlsx",
    exportDeviceList: "Exportar Lista de Dispositivos",
    importDeviceList: "Importar Lista de Dispositivos",
    exportSuccess: "Lista de dispositivos exportada correctamente",
    exportFail: "Error al exportar la lista de dispositivos",
    importSuccess: "Lista de dispositivos importada correctamente",
    importFail: "Error al importar la lista de dispositivos",
    exportSuccessMsg: "Lista de dispositivos exportada correctamente",
    exportFailMsg: "Error al exportar la lista de dispositivos",
    importSuccessMsg: "Lista de dispositivos importada correctamente",
    importFailMsg: "Error al importar la lista de dispositivos: {msg}",
    deviceName: "Nombre del Dispositivo",
    deviceAddress: "Dirección del Dispositivo",
    devicePort: "Puerto del Dispositivo",
    isEncrypted: "¿Encriptado?",
    encrypted: "Encriptado",
    notEncrypted: "No encriptado",
    status: "Estado",
    operation: "Operación",
    reboot: "Reiniciar",
    noReboot: "No Reiniciar",
    addDevice: "Agregar Dispositivo",
    deleteDevice: "Eliminar Dispositivo",
    clearDevices: "Limpiar Dispositivos",
    deviceExists: "El dispositivo ya existe",
    deviceDeleted: "Dispositivo {ip} eliminado",
    downloadFile: "¿Descargar archivo?",
    importParam: "¿Importar parámetro?",
    connectTimeout: "Tiempo de Espera de Conexión",
    paramTimeout: "Tiempo de Espera de Modificación de Parámetro",
    readTimeout: "Tiempo de Espera de Solicitud Global",
    progress: "Progreso"
  },

  // 下载文件配置
  downList: {
    title: "Descarga",
    deviceDirectory: "Directorio del Dispositivo",
    fileName: "Nombre del Archivo",
    fileSize: "Tamaño del Archivo",
    filePath: "Ruta del Archivo",
    lastModified: "Última Modificación",
    addFile: "Agregar archivo a descargar",
    addFolder: "Agregar carpeta a descargar",
    fileExists: "¡El archivo {path} ya existe, no se pudo agregar!",
    fileDeleted: "Archivo {path} eliminado",
    filesDeleted: "Archivos eliminados",
    defaultExportFileName: "Lista de Archivos Descargados.xlsx",
    exportTitle: "Exportar Lista de Archivos Descargados",
    importTitle: "Importar Lista de Archivos Descargados",
    exportSuccess: "Lista de archivos exportada correctamente",
    exportFailed: "Error al exportar la lista de archivos",
    importSuccess: "Lista de archivos importada correctamente",
    importFailed: "Error al importar la lista de archivos",
    fileExistsMsg: "El archivo {path} ya existe, no se pudo agregar!"
  },

  // 参数定值配置
  paramList: {
    title: "Parámetros",
    paramGroup: "Grupo de Valores",
    groupName: "Nombre del Grupo",
    paramName: "Nombre del Parámetro",
    paramDesc: "Descripción del Parámetro",
    paramValue: "Valor del Parámetro",
    minValue: "Valor Mínimo",
    maxValue: "Valor Máximo",
    step: "Paso",
    unit: "Unidad",
    searchParamName: "Nombre del parámetro",
    searchParamDesc: "Descripción del parámetro",
    importSuccess: "Importación de valores de parámetros exitosa",
    importFailed: "Error al importar valores de parámetros",
    exportSuccess: "Exportación de la lista de valores exitosa",
    exportFailed: "Error al exportar la lista de valores",
    clearSuccess: "Lista de valores limpiada exitosamente"
  },

  // 进度对话框
  progressDialog: {
    title: "Procesando...",
    pleaseWait: "Por favor, espere..."
  },

  // 打包程序
  packageProgram: {
    saveDir: "Directorio de guardado",
    selectSaveDir: "Seleccionar directorio de guardado",
    packageBtn: "Empaquetar",
    locateDir: "Ubicar carpeta",
    delete: "Eliminar",
    sequence: "N.º",
    fileName: "Nombre de archivo",
    fileSize: "Tamaño de archivo",
    filePath: "Ruta de archivo",
    lastModified: "Última modificación",
    operation: "Operación",
    saveDirEmpty: "¡Por favor seleccione primero el directorio de guardado!",
    packageSuccess: "¡Empaquetado del programa completado!",
    tip: "Aviso",
    confirmButton: "Aceptar",
    // Nuevo import/export
    defaultExportFileName: "Lista de archivos del paquete de programa.xlsx",
    exportTitle: "Exportar lista de archivos del paquete de programa",
    importTitle: "Importar lista de archivos del paquete de programa",
    exportSuccess: "Lista de archivos exportada correctamente",
    exportFailed: "Error al exportar la lista de archivos",
    importSuccess: "Lista de archivos importada correctamente",
    importFailed: "Error al importar la lista de archivos",
    fileExists: "¡El archivo {path} ya existe, no se pudo agregar!",
    selectDirSuccess: "Directorio seleccionado: {dir}",
    locateDirSuccess: "Directorio localizado: {dir}",
    addFileStart: "Abriendo selector de archivos...",
    addFileSuccess: "{count} archivos/carpetas añadidos correctamente",
    addFileNone: "No se añadieron archivos/carpetas nuevos",
    deleteSuccess: "{count} archivos/carpetas eliminados correctamente",
    clearSuccess: "Todos los archivos/carpetas han sido eliminados",
    moveUpSuccess: "Movido arriba: {name}",
    moveDownSuccess: "Movido abajo: {name}",
    noFileSelected: "¡Por favor seleccione primero los archivos a empaquetar!",
    noDeviceSelected: "¡Por favor seleccione primero los dispositivos a empaquetar!",
    packageFailed: "Error de empaquetado: {msg}",
    zipPath: "Ruta de empaquetado: {zipPath}",
    openFileButton: "Abrir archivo"
  }
};
