const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["../chunks/utils-DeqEhvvR.js","../vendor/vue-vendor-EiOvILk3.js","../vendor/utils-vendor-C_ezCHls.js","../vendor/highlight-vendor-DzhJ4giz.js","../vendor/vendor-P-ltm-Yc.js","../styles/vendor-CaccPJo9.css","../styles/highlight-vendor-ZgkIHsf0.css","../chunks/element-core-DyPKvxS2.js","../chunks/element-icons-DbNYNmpr.js","../styles/element-core-BlK98vAp.css","../vendor/i18n-vendor-BPpKJ4WV.js","../chunks/element-components-CkwEs5_B.js","../chunks/components-BkHQV7Qm.js","../vendor/charts-vendor-DS7xuoj-.js","../chunks/views-biz-DnwQrgNu.js","../vendor/icons-vendor-DBJjj0eo.js","../styles/vue-vendor-DNbd9zSU.css","../styles/iconfont-kWcO7Ai6.css","../styles/iconfont-DRZ30ItQ.css","../styles/font-D0q-O3lU.css","../styles/element-dark-DWIGLUio.css","../styles/element-85kFTNYz.css","../chunks/index-BsuDHVIl.js"])))=>i.map(i=>d[i]);
import{_ as o,aA as I,aB as S,aC as V,aD as M,aE as g}from"../chunks/utils-DeqEhvvR.js";import{cy as B,dm as z}from"../vendor/vendor-P-ltm-Yc.js";import{f as F,g as N,A as $}from"../vendor/vue-vendor-EiOvILk3.js";import"../chunks/element-core-DyPKvxS2.js";import{a2 as j}from"../chunks/views-biz-DnwQrgNu.js";import"../chunks/element-components-CkwEs5_B.js";import"../vendor/utils-vendor-C_ezCHls.js";import"../vendor/highlight-vendor-DzhJ4giz.js";import"../chunks/element-icons-DbNYNmpr.js";import"../chunks/components-BkHQV7Qm.js";import"../vendor/charts-vendor-DS7xuoj-.js";import"../vendor/icons-vendor-DBJjj0eo.js";import"../vendor/i18n-vendor-BPpKJ4WV.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&s(l)}).observe(document,{childList:!0,subtree:!0});function a(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function s(r){if(r.ep)return;r.ep=!0;const i=a(r);fetch(r.href,i)}})();const H={enablePerformanceMonitor:!0,enableStartupCache:!0,cacheExpiry:60*60*1e3,delayedResourcesTimeout:20,iconPreloadDelay:2e3,enableLazyComponents:!0,componentLoadBatchSize:8,enableRouteCache:!0,routePreloadDelay:50,enableAsyncServiceStart:!0,serviceStartTimeout:3e3,isDevelopment:!0,enableDebugLogs:!1};function E(){const t=x();return{...H,...t}}function x(){try{const t=localStorage.getItem("visualdebug_startup_config");if(t)return JSON.parse(t)}catch(t){console.warn("读取用户启动配置失败:",t)}return{}}function G(){const t=[],n=E();return n.enableStartupCache||t.push({type:"warning",title:"启动缓存已禁用",description:"启用启动缓存可以显著提高应用启动速度",action:"启用启动缓存"}),!n.enablePerformanceMonitor&&n.isDevelopment&&t.push({type:"info",title:"性能监控已禁用",description:"在开发模式下启用性能监控有助于识别性能瓶颈",action:"启用性能监控"}),n.delayedResourcesTimeout>200&&t.push({type:"warning",title:"资源延迟加载时间过长",description:"延迟时间过长可能影响用户体验",action:"减少延迟时间"}),n.serviceStartTimeout<3e3&&t.push({type:"warning",title:"服务启动超时时间过短",description:"超时时间过短可能导致服务启动失败",action:"增加超时时间"}),t}function q(t){console.group("🚀 应用启动优化配置"),console.log("性能监控:",t.enablePerformanceMonitor?"✅ 启用":"❌ 禁用"),console.log("启动缓存:",t.enableStartupCache?"✅ 启用":"❌ 禁用"),console.log("懒加载组件:",t.enableLazyComponents?"✅ 启用":"❌ 禁用"),console.log("异步服务启动:",t.enableAsyncServiceStart?"✅ 启用":"❌ 禁用"),console.log("延迟资源加载时间:",`${t.delayedResourcesTimeout}ms`),console.log("缓存过期时间:",`${t.cacheExpiry/1e3}秒`);const n=G();n.length>0&&(console.group("💡 优化建议"),n.forEach(a=>{const s=a.type==="error"?"❌":a.type==="warning"?"⚠️":"ℹ️";console.log(`${s} ${a.title}: ${a.description}`)}),console.groupEnd()),console.groupEnd()}const p=E(),C=F();C.use(N);p.enablePerformanceMonitor&&setTimeout(()=>{o(()=>import("../chunks/utils-DeqEhvvR.js").then(t=>t.aF),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]),import.meta.url).catch(console.warn)},1e3);const c=B($);q(p);c.config.errorHandler=I;c.use(S).use(C).use(j).use(z);c.mount("#app");V();p.enableStartupCache&&M().catch(console.warn);console.log("🔧 开发模式：状态调试器已启用"),console.log("💡 使用 stateDebugger.help() 查看可用命令");setTimeout(async()=>{try{await Promise.all([o(()=>Promise.resolve({}),__vite__mapDeps([17]),import.meta.url).catch(console.warn),o(()=>Promise.resolve({}),__vite__mapDeps([18]),import.meta.url).catch(console.warn),o(()=>Promise.resolve({}),__vite__mapDeps([19]),import.meta.url).catch(console.warn),o(()=>import("../chunks/element-core-DyPKvxS2.js").then(e=>e.b4),__vite__mapDeps([7,4,3,6,2,5,8,9]),import.meta.url).catch(console.warn),o(()=>Promise.resolve({}),__vite__mapDeps([20]),import.meta.url).catch(console.warn),o(()=>Promise.resolve({}),__vite__mapDeps([21]),import.meta.url).catch(console.warn),o(()=>import("../vendor/highlight-vendor-DzhJ4giz.js").then(e=>e.b),__vite__mapDeps([3,4,2,5,6]),import.meta.url).catch(console.warn)]);const[t,n,a,s,r,i,l,L]=await Promise.all([o(()=>import("../chunks/index-BsuDHVIl.js"),__vite__mapDeps([22,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]),import.meta.url).catch(()=>({default:null})),o(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.bd),__vite__mapDeps([1,0,11,4,3,6,2,5,7,8,9,12,13,15,10,14,16]),import.meta.url).catch(()=>({default:null})),o(()=>import("../vendor/vue-vendor-EiOvILk3.js").then(e=>e.be),__vite__mapDeps([1,0,11,4,3,6,2,5,7,8,9,12,13,15,10,14,16]),import.meta.url).catch(()=>({default:null})),o(()=>import("../chunks/utils-DeqEhvvR.js").then(e=>e.aG),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]),import.meta.url).catch(()=>({downloadAndInstall:()=>console.warn("图标加载失败")})),o(()=>import("../chunks/element-icons-DbNYNmpr.js"),__vite__mapDeps([8,4,3,6,2,5]),import.meta.url).catch(()=>({})),o(()=>import("../vendor/highlight-vendor-DzhJ4giz.js").then(e=>e.h),__vite__mapDeps([3,4,2,5,6]),import.meta.url).catch(()=>({default:null})),o(()=>import("../vendor/highlight-vendor-DzhJ4giz.js").then(e=>e.d),__vite__mapDeps([3,4,2,5,6]),import.meta.url).catch(()=>({default:null})),o(()=>import("../vendor/charts-vendor-DS7xuoj-.js").then(e=>e.e),__vite__mapDeps([13,4,3,6,2,5]),import.meta.url).catch(()=>({use:()=>console.warn("ECharts加载失败")}))]),_=t.default,h=n.default,f=a.default,{downloadAndInstall:P}=s;_&&c.use(_),i.default&&c.use(i.default),h&&f&&c.component("BlRow",h).component("BLCol",f),["Edit","Delete","Search","Refresh","Plus","Minus","ColdDrink","Setting","Notification","CircleCheckFilled","ChromeFilled","CircleClose","FolderDelete","Remove","DArrowLeft","DArrowRight","More"].forEach(e=>{r[e]&&c.component(e,r[e])}),P();const{LineChart:y,BarChart:v,PieChart:w}=await o(async()=>{const{LineChart:e,BarChart:u,PieChart:m}=await import("../vendor/charts-vendor-DS7xuoj-.js").then(d=>d.P);return{LineChart:e,BarChart:u,PieChart:m}},__vite__mapDeps([13,4,3,6,2,5]),import.meta.url),{TitleComponent:A,TooltipComponent:R,LegendComponent:D,GridComponent:T}=await o(async()=>{const{TitleComponent:e,TooltipComponent:u,LegendComponent:m,GridComponent:d}=await import("../vendor/charts-vendor-DS7xuoj-.js").then(b=>b.Q);return{TitleComponent:e,TooltipComponent:u,LegendComponent:m,GridComponent:d}},__vite__mapDeps([13,4,3,6,2,5]),import.meta.url),{CanvasRenderer:O}=await o(async()=>{const{CanvasRenderer:e}=await import("../vendor/charts-vendor-DS7xuoj-.js").then(u=>u.R);return{CanvasRenderer:e}},__vite__mapDeps([13,4,3,6,2,5]),import.meta.url);L.use([y,v,w,A,R,D,T,O]);try{l.default&&l.default.highlightAuto&&l.default.highlightAuto("<h1>Highlight.js has been registered successfully!</h1>").value}catch(e){console.warn("Highlight.js初始化失败:",e)}p.enablePerformanceMonitor&&g()}catch(t){console.warn("延迟资源加载失败:",t),p.enablePerformanceMonitor&&g()}},50);
