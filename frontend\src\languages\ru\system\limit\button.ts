export default {
  limit: {
    button: {
      title: "Управление кнопкам<PERSON>",
      buttonList: "Спис<PERSON><PERSON> кнопок",
      addButton: "Добавить кнопку",
      batchAdd: "Пакетное добавление",
      deleteSelected: "Удалить выбранные",
      confirm: "Подтвердить",
      name: "Название кнопки",
      code: "Код кнопки",
      sort: "Сортировка",
      description: "Описание",
      operation: "Операция",
      form: {
        add: "Добавить кнопку",
        edit: "Редактировать кнопку",
        view: "Просмотр кнопки",
        title: "Название кнопки",
        code: "Код кнопки",
        sort: "Сортировка",
        description: "Описание",
        cancel: "Отм<PERSON><PERSON>",
        confirm: "Подтвердить",
        validation: {
          title: "Пожалуйста, введите название кнопки",
          code: "Пожалуйста, введите код кнопки",
          sort: "Пожалуйста, введите сортировку"
        }
      },
      batch: {
        title: "Пакетное добавление кнопок",
        module: "Принадлежащий модуль",
        buttons: "Список кнопок",
        add: "Добавить",
        delete: "Удалить",
        cancel: "Отмена",
        confirm: "Подтвердить",
        validation: {
          module: "Пожалуйста, выберите принадлежащий модуль",
          buttons: "Пожалуйста, добавьте кнопки"
        }
      }
    }
  }
};
