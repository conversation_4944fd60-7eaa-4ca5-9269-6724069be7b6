import { addCollection } from "@iconify/vue";
import antJson from "@iconify/json/json/ant-design.json";
import epJson from "@iconify/json/json/ep.json";
import etJson from "@iconify/json/json/et.json";
import evaJson from "@iconify/json/json/eva.json";
import flatJson from "@iconify/json/json/flat-color-icons.json";
import lineMdJson from "@iconify/json/json/line-md.json";

export async function downloadAndInstall() {
  addCollection(antJson);
  addCollection(epJson);
  addCollection(etJson);
  addCollection(evaJson);
  addCollection(flatJson);
  addCollection(lineMdJson);
}
