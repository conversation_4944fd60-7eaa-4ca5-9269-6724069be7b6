export default {
  common: {
    date: "Дата",
    search: "Поиск",
    save: "Сохранить",
    clear: "Очистить",
    loading: "Загрузка...",
    reportNo: "Номер отчета",
    time: "Время",
    description: "Описание",
    progress: "Прогресс",
    selectDateRange: "Пожалуйста, выберите диапазон дат",
    noData: "Нет данных",
    saveSuccess: "Сохранение успешно",
    saveFailed: "Сохранение не удалось"
  },
  date: "Дата",
  search: "Поиск",
  filter: "Фильтр",
  save: "Сохранить",
  clearList: "Очистить список",
  loading: "Загрузка...",
  reportNumber: "Номер отчета",
  time: "Время",
  description: "Описание",
  progress: "Прогресс",
  loadingText: "Загрузка...",
  querying: "Выполняется запрос",
  selectCompleteTimeRange: "Пожалуйста, выберите полный временной диапазон",
  noDataToSave: "Нет данных для сохранения",
  saveSuccess: "Сохранение успешно",
  saveReport: "Сохранить отчет",
  fileUploading: "Загрузка файла",
  fileUploadComplete: "Загрузка файла завершена",
  autoRefresh: "Автообновление",
  showHiddenItems: "Показать скрытые элементы",
  hideHiddenItems: "Скрыть скрытые элементы",
  name: "Название",
  operationAddress: "Адрес операции",
  operationParams: "Параметры операции",
  value: "Значение",
  step: "Шаг",
  source: "Источник",
  sourceType: "Тип источника",
  result: "Результат",
  searchType: "Тип поиска",
  total: "Всего {num} записей",
  sameSearch: "Поиск одинакового содержимого",
  sameFilter: "Фильтр одинакового содержимого",
  showHideTime: "Показать/скрыть столбец времени",
  selectRowToOperate: "Пожалуйста, сначала выберите строку для операции",
  trip: {
    autoRefresh: "Автообновление"
  },
  operate: {
    name: "Название",
    operateAddress: "Адрес операции",
    operateParam: "Параметр операции",
    value: "Значение",
    step: "Шаг",
    source: "Источник",
    sourceType: "Тип источника",
    result: "Результат"
  },
  group: {
    uploadWave: "Загрузить волну",
    searchHistory: "История поиска",
    saveResult: "Сохранить результат",
    clearContent: "Очистить содержимое",
    contextMenu: {
      uploadWave: "Загрузить волну",
      getHistoryReport: "Получить исторический отчет",
      saveResult: "Сохранить результат",
      clearContent: "Очистить содержимое"
    },
    date: "Дата",
    search: "Поиск",
    save: "Сохранить",
    clearList: "Очистить список",
    loading: "Загрузка...",
    table: {
      reportId: "ID отчета",
      time: "Время",
      description: "Описание"
    },
    progress: {
      title: "Прогресс",
      searching: "Выполняется запрос {type}",
      loading: "Загрузка..."
    },
    refresh: {
      start: "Начать обновление",
      stop: "Остановить обновление"
    },
    hiddenItems: {
      show: "Показать скрытые элементы"
    },
    messages: {
      noFileToUpload: "Нет файлов для загрузки",
      selectDateRange: "Пожалуйста, выберите диапазон дат",
      noDataToSave: "Нет данных для сохранения",
      saveReport: "Сохранить отчет",
      saveSuccess: "Сохранение успешно"
    }
  },
  exporting: "Экспорт...",
  stopRefresh: "Остановить обновление",
  searchProgress: "Выполняется запрос {type}",
  exportLogSuccess: "Экспорт успешен: {path}",
  exportLogFailed: "Экспорт не удался: {msg}",
  exportLogCancelled: "Пользователь отменил операцию экспорта",
  entryID: "Номер",
  module: "Название компонента",
  msg: "Содержимое",
  level: "Уровень",
  type: "Тип",
  origin: "Источник",
  user: "Имя пользователя",
  pleaseSelectSavePath: "Пожалуйста, выберите путь сохранения...",
  items: "записей"
};
