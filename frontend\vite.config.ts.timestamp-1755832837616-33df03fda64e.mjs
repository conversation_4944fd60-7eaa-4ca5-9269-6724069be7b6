// vite.config.ts
import { defineConfig, loadEnv } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite/dist/node/index.js";
import { resolve as resolve2 } from "path";

// build/getEnv.ts
function wrapperEnv(envConf) {
  const ret = {};
  for (const envName of Object.keys(envConf)) {
    let realName = envConf[envName].replace(/\\n/g, "\n");
    realName = realName === "true" ? true : realName === "false" ? false : realName;
    if (envName === "VITE_PORT") realName = Number(realName);
    if (envName === "VITE_PROXY") {
      try {
        realName = JSON.parse(realName);
      } catch (error) {
      }
    }
    ret[envName] = realName;
  }
  return ret;
}

// build/proxy.ts
function createProxy(list = []) {
  const ret = {};
  for (const [prefix, target] of list) {
    const httpsRE = /^https:\/\//;
    const isHttps = httpsRE.test(target);
    ret[prefix] = {
      target,
      changeOrigin: true,
      ws: true,
      rewrite: (path) => path.replace(new RegExp(`^${prefix}`), ""),
      // https is require secure=false
      ...isHttps ? { secure: false } : {}
    };
  }
  return ret;
}

// build/plugins.ts
import { resolve } from "path";
import { VitePWA } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-pwa/dist/index.js";
import { visualizer } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import { createSvgIconsPlugin } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import { createHtmlPlugin } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-html/dist/index.mjs";
import vue from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import eslintPlugin from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-eslint/dist/index.mjs";
import viteCompression from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-compression/dist/index.mjs";
import vueSetupExtend from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-vue-setup-extend-plus/dist/vite.js";
import UnoCSS from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unocss/dist/vite.mjs";
import AutoImport from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-auto-import/dist/vite.js";
import { ElementPlusResolver } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-vue-components/dist/resolvers.mjs";
import Components from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-vue-components/dist/vite.mjs";
import Icons from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-icons/dist/vite.js";
import IconsResolver from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/unplugin-icons/dist/resolver.js";
import NextDevTools from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import { codeInspectorPlugin } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/code-inspector-plugin/dist/index.mjs";
var createVitePlugins = (viteEnv) => {
  const { VITE_GLOB_APP_TITLE, VITE_REPORT, VITE_DEVTOOLS, VITE_PWA, VITE_CODEINSPECTOR } = viteEnv;
  return [
    vue(),
    // devTools
    VITE_DEVTOOLS && NextDevTools({ launchEditor: "code" }),
    // vue 可以使用 jsx/tsx 语法
    vueJsx(),
    // esLint 报错信息显示在浏览器界面上
    eslintPlugin(),
    // name 可以写在 script 标签上
    vueSetupExtend({}),
    // 创建打包压缩配置
    createCompression(viteEnv),
    // 注入变量到 html 文件
    createHtmlPlugin({
      minify: true,
      inject: {
        data: { title: VITE_GLOB_APP_TITLE }
      }
    }),
    // 使用 svg 图标
    createSvgIconsPlugin({
      iconDirs: [resolve(process.cwd(), "src/assets/svg")],
      symbolId: "local-[dir]-[name]"
    }),
    // vitePWA
    VITE_PWA && createVitePwa(viteEnv),
    // 是否生成包预览，分析依赖包大小做优化处理
    VITE_REPORT && visualizer({ filename: "stats.html", gzipSize: true, brotliSize: true }),
    // 自动 IDE 并将光标定位到 DOM 对应的源代码位置。see: https://inspector.fe-dev.cn/guide/start.html
    VITE_CODEINSPECTOR && codeInspectorPlugin({
      bundler: "vite"
    }),
    // 自动导入组件
    AutoImport({
      imports: ["vue", "vue-router"],
      // Auto import functions from Element Plus, e.g. ElMessage, ElMessageBox... (without style)
      // 自动导入 Element Plus 相关函数，如：ElMessage, ElMessageBox... (不带样式，因为已在main.ts中导入完整样式)
      resolvers: [
        ElementPlusResolver({
          // 禁用样式自动导入，因为已在main.ts中导入完整样式
          importStyle: false
        }),
        // Auto import icon components
        // 自动导入图标组件
        IconsResolver({
          prefix: "Icon"
        })
      ],
      dts: "src/auto-import.d.ts"
      // 路径下自动生成文件夹存放全局指令
    }),
    Components({
      dirs: ["src/components"],
      // 配置需要默认导入的自定义组件文件夹，该文件夹下的所有组件都会自动 import
      resolvers: [
        // Auto register icon components
        // 自动注册图标组件
        IconsResolver({
          enabledCollections: ["ep"]
          // element-plus 图标库
        }),
        // Auto register Element Plus components
        // 自动导入 Element Plus 组件（不带样式，因为已在main.ts中导入完整样式）
        ElementPlusResolver({
          // 禁用样式自动导入，因为已在main.ts中导入完整样式
          importStyle: false,
          // 确保搜索表单组件能被正确解析
          resolveIcons: true
        })
      ],
      // 生成类型声明文件
      dts: true
    }),
    Icons({
      compiler: "vue3",
      autoInstall: true
    }),
    UnoCSS()
    // UnoCSS
  ];
};
var createCompression = (viteEnv) => {
  const { VITE_BUILD_COMPRESS = "none", VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE } = viteEnv;
  const compressList = VITE_BUILD_COMPRESS.split(",");
  const plugins = [];
  if (compressList.includes("gzip")) {
    plugins.push(
      viteCompression({
        ext: ".gz",
        algorithm: "gzip",
        deleteOriginFile: VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE
      })
    );
  }
  if (compressList.includes("brotli")) {
    plugins.push(
      viteCompression({
        ext: ".br",
        algorithm: "brotliCompress",
        deleteOriginFile: VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE
      })
    );
  }
  return plugins;
};
var createVitePwa = (viteEnv) => {
  const { VITE_GLOB_APP_TITLE } = viteEnv;
  return VitePWA({
    registerType: "autoUpdate",
    workbox: {
      // 添加此项配置，增加需要缓存的最大文件大小
      maximumFileSizeToCacheInBytes: 6 * 1024 * 1024
    },
    manifest: {
      name: VITE_GLOB_APP_TITLE,
      short_name: VITE_GLOB_APP_TITLE,
      theme_color: "#ffffff",
      icons: [
        {
          src: "/logo.png",
          sizes: "192x192",
          type: "image/png"
        },
        {
          src: "/logo.png",
          sizes: "512x512",
          type: "image/png"
        },
        {
          src: "/logo.png",
          sizes: "512x512",
          type: "image/png",
          purpose: "any maskable"
        }
      ]
    }
  });
};

// vite.config.ts
import { visualizer as visualizer2 } from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";

// package.json
var package_default = {
  name: "VisualDebug",
  private: true,
  type: "module",
  description: "\u53EF\u89C6\u5316\u5E73\u53F0\u5DE5\u7A0B\u8C03\u8BD5\u5DE5\u5177",
  license: "MIT",
  scripts: {
    dev: "vite --host --port 8080",
    "dev:force": "vite --host --port 8080 --force",
    build: "node ./node_modules/vite/bin/vite.js build",
    "build:dev": "vue-tsc && vite build --mode development",
    "build:test": "vue-tsc && vite build --mode test",
    "build:pro": "vue-tsc && vite build --mode production",
    "build:analyze": "vite build --mode production && npx vite-bundle-analyzer dist/stats.html",
    "type:check": "vue-tsc --noEmit --skipLibCheck",
    preview: "pnpm run build:dev && vite preview",
    "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src",
    "lint:prettier": 'prettier --write "src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}"',
    "lint:stylelint": 'stylelint --cache --fix "**/*.{vue,less,postcss,css,scss}" --cache --cache-location node_modules/.cache/stylelint/',
    "lint:lint-staged": "lint-staged",
    "cache:clear": "node scripts/clear-cache.js",
    "cache:clear:dev": "node scripts/clear-cache.js --dev",
    "cache:clear:build": "node scripts/clear-cache.js --build",
    "cache:clear:all": "node scripts/clear-cache.js --all",
    "deps:update": "npm update && npm audit fix",
    "deps:check": "npm outdated",
    "perf:monitor": "node scripts/performance-monitor.js",
    "perf:cache": "node scripts/performance-monitor.js --cache",
    "perf:build": "node scripts/performance-monitor.js --build",
    prepare: "husky install",
    release: "standard-version",
    commit: "git add -A && czg && git push"
  },
  dependencies: {
    "@antv/g2plot": "^2.4.32",
    "@antv/x6": "^2.18.1",
    "@antv/x6-plugin-clipboard": "2.1.6",
    "@antv/x6-plugin-dnd": "2.1.1",
    "@antv/x6-plugin-export": "^2.1.6",
    "@antv/x6-plugin-history": "2.2.4",
    "@antv/x6-plugin-keyboard": "2.2.3",
    "@antv/x6-plugin-scroller": "2.0.10",
    "@antv/x6-plugin-selection": "2.2.2",
    "@antv/x6-plugin-snapline": "2.1.7",
    "@antv/x6-plugin-transform": "^2.1.8",
    "@element-plus/icons-vue": "^2.3.1",
    "@highlightjs/vue-plugin": "^2.1.0",
    "@iconify/vue": "^4.1.2",
    "@vueuse/core": "^11.0.3",
    axios: "^1.7.7",
    "crypto-js": "^4.1.1",
    dayjs: "^1.11.13",
    "decimal.js": "^10.5.0",
    "default-passive-events": "^2.0.0",
    echarts: "^5.5.1",
    "element-plus": "^2.5.6",
    entities: "^4.5.0",
    "highlight.js": "^11.10.0",
    "markdown-it": "^14.1.0",
    md5: "^2.3.0",
    mitt: "^3.0.1",
    nprogress: "^0.2.0",
    pinia: "^2.2.2",
    "pinia-plugin-persistedstate": "^3.2.1",
    "print-js": "^1.6.0",
    qs: "^6.13.0",
    sortablejs: "^1.15.3",
    "split.js": "^1.6.5",
    "sprintf-js": "^1.1.3",
    "v-contextmenu": "^3.2.0",
    vue: "^3.5.5",
    "vue-cropper": "^1.1.1",
    "vue-i18n": "^9.13.1",
    "vue-router": "^4.4.5"
  },
  devDependencies: {
    "@commitlint/cli": "^19.5.0",
    "@commitlint/config-conventional": "^19.5.0",
    "@iconify/json": "^2.2.247",
    "@types/markdown-it": "^14.1.2",
    "@types/md5": "^2.3.5",
    "@types/nprogress": "^0.2.3",
    "@types/qs": "^6.9.15",
    "@types/sm-crypto": "^0.3.4",
    "@types/sortablejs": "^1.15.8",
    "@types/uuid": "^10.0.0",
    "@typescript-eslint/eslint-plugin": "^7.14.1",
    "@typescript-eslint/parser": "^7.14.1",
    "@vitejs/plugin-vue": "^5.1.3",
    "@vitejs/plugin-vue-jsx": "^4.0.1",
    autoprefixer: "^10.4.20",
    "code-inspector-plugin": "^0.16.1",
    "cz-git": "^1.9.4",
    czg: "^1.9.4",
    eslint: "^8.57.0",
    "eslint-config-prettier": "^9.1.0",
    "eslint-plugin-prettier": "^5.1.3",
    "eslint-plugin-vue": "^9.26.0",
    "hotkeys-js": "3.13.7",
    husky: "^9.0.11",
    "lint-staged": "^15.2.10",
    "naive-ui": "^2.39.0",
    postcss: "^8.4.45",
    "postcss-html": "^1.7.0",
    prettier: "^3.3.3",
    "rollup-plugin-visualizer": "^5.14.0",
    sass: "1.74.1",
    "sm-crypto": "^0.3.13",
    "standard-version": "^9.5.0",
    stylelint: "^16.9.0",
    "stylelint-config-html": "^1.1.0",
    "stylelint-config-recess-order": "^5.1.0",
    "stylelint-config-recommended-scss": "^14.1.0",
    "stylelint-config-recommended-vue": "^1.5.0",
    "stylelint-config-standard": "^36.0.1",
    "stylelint-config-standard-scss": "^13.1.0",
    typescript: "~5.4.0",
    unocss: "^0.62.3",
    "unplugin-auto-import": "^0.18.3",
    "unplugin-icons": "^0.19.3",
    "unplugin-vue-components": "^0.25.2",
    "unplugin-vue-setup-extend-plus": "^1.0.1",
    uuid: "^8.3.2",
    vite: "^5.4.5",
    "vite-plugin-compression": "^0.5.1",
    "vite-plugin-eslint": "^1.8.1",
    "vite-plugin-html": "^3.2.2",
    "vite-plugin-pwa": "^0.20.5",
    "vite-plugin-svg-icons": "^2.0.1",
    "vite-plugin-vue-devtools": "^7.3.5",
    "vue-tsc": "^2.1.6"
  },
  overrides: {},
  engines: {
    node: ">=16.0.0"
  },
  browserslist: {
    production: [
      "> 1%",
      "not dead",
      "not op_mini all"
    ],
    development: [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  config: {
    commitizen: {
      path: "node_modules/cz-git"
    }
  }
};

// vite.config.ts
import dayjs from "file:///E:/%E5%B7%A5%E5%85%B7%E8%BD%AF%E4%BB%B6/visualdebug/frontend/node_modules/dayjs/dayjs.min.js";

// build/element-plus-config.ts
var elementPlusCoreIncludes = [
  // Element Plus 核心库
  "element-plus",
  "element-plus/es",
  "@element-plus/icons-vue"
];
var elementPlusStylePaths = [
  // 表单组件
  "element-plus/es/components/form/style/css",
  "element-plus/es/components/form-item/style/css",
  "element-plus/es/components/input/style/css",
  "element-plus/es/components/input-number/style/css",
  "element-plus/es/components/select/style/css",
  "element-plus/es/components/option/style/css",
  "element-plus/es/components/checkbox/style/css",
  "element-plus/es/components/radio/style/css",
  "element-plus/es/components/switch/style/css",
  "element-plus/es/components/slider/style/css",
  "element-plus/es/components/rate/style/css",
  "element-plus/es/components/color-picker/style/css",
  "element-plus/es/components/date-picker/style/css",
  "element-plus/es/components/time-picker/style/css",
  "element-plus/es/components/upload/style/css",
  // 数据展示组件
  "element-plus/es/components/table/style/css",
  "element-plus/es/components/table-column/style/css",
  "element-plus/es/components/pagination/style/css",
  "element-plus/es/components/tag/style/css",
  "element-plus/es/components/progress/style/css",
  "element-plus/es/components/tree/style/css",
  "element-plus/es/components/badge/style/css",
  "element-plus/es/components/card/style/css",
  "element-plus/es/components/collapse/style/css",
  "element-plus/es/components/timeline/style/css",
  "element-plus/es/components/divider/style/css",
  "element-plus/es/components/image/style/css",
  "element-plus/es/components/calendar/style/css",
  // 导航组件
  "element-plus/es/components/menu/style/css",
  "element-plus/es/components/tabs/style/css",
  "element-plus/es/components/breadcrumb/style/css",
  "element-plus/es/components/breadcrumb-item/style/css",
  "element-plus/es/components/page-header/style/css",
  "element-plus/es/components/steps/style/css",
  "element-plus/es/components/dropdown/style/css",
  "element-plus/es/components/dropdown-menu/style/css",
  "element-plus/es/components/dropdown-item/style/css",
  // 反馈组件
  "element-plus/es/components/alert/style/css",
  "element-plus/es/components/loading/style/css",
  "element-plus/es/components/message/style/css",
  "element-plus/es/components/message-box/style/css",
  "element-plus/es/components/notification/style/css",
  "element-plus/es/components/dialog/style/css",
  "element-plus/es/components/popover/style/css",
  "element-plus/es/components/popconfirm/style/css",
  "element-plus/es/components/tooltip/style/css",
  "element-plus/es/components/drawer/style/css",
  "element-plus/es/components/result/style/css",
  // 布局组件
  "element-plus/es/components/container/style/css",
  "element-plus/es/components/header/style/css",
  "element-plus/es/components/aside/style/css",
  "element-plus/es/components/main/style/css",
  "element-plus/es/components/footer/style/css",
  "element-plus/es/components/row/style/css",
  "element-plus/es/components/col/style/css",
  "element-plus/es/components/space/style/css",
  // 其他组件
  "element-plus/es/components/button/style/css",
  "element-plus/es/components/button-group/style/css",
  "element-plus/es/components/link/style/css",
  "element-plus/es/components/text/style/css",
  "element-plus/es/components/scrollbar/style/css",
  "element-plus/es/components/backtop/style/css",
  "element-plus/es/components/avatar/style/css",
  "element-plus/es/components/empty/style/css",
  "element-plus/es/components/descriptions/style/css",
  "element-plus/es/components/descriptions-item/style/css",
  "element-plus/es/components/skeleton/style/css",
  "element-plus/es/components/skeleton-item/style/css",
  "element-plus/es/components/affix/style/css",
  "element-plus/es/components/anchor/style/css",
  "element-plus/es/components/anchor-link/style/css"
];
function getElementPlusIncludes(useStyleIncludes = false) {
  if (useStyleIncludes) {
    return [...elementPlusCoreIncludes, ...elementPlusStylePaths];
  }
  return elementPlusCoreIncludes;
}
function isElementPlusModule(id) {
  return id.includes("element-plus") || id.includes("@element-plus");
}
function getElementPlusChunkName(id) {
  if (id.includes("@element-plus/icons-vue")) {
    return "element-icons";
  }
  if (id.includes("element-plus/es/components")) {
    return "element-components";
  }
  if (id.includes("element-plus")) {
    return "element-core";
  }
  return "element-vendor";
}

// vite.config.ts
var __vite_injected_original_dirname = "E:\\\u5DE5\u5177\u8F6F\u4EF6\\visualdebug\\frontend";
var { dependencies, devDependencies, name } = package_default;
var __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name },
  lastBuildTime: dayjs().format("YYYY-MM-DD HH:mm:ss")
};
var vite_config_default = defineConfig(({ mode }) => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const viteEnv = wrapperEnv(env);
  return {
    base: viteEnv.VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias: {
        "@": resolve2(__vite_injected_original_dirname, "./src"),
        "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js",
        "async-validator": resolve2("node_modules/async-validator/dist-node/index.js"),
        mousetrap: "mousetrap/mousetrap.js"
      }
    },
    define: {
      __APP_INFO__: JSON.stringify(__APP_INFO__)
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler"
          // or "modern"
        }
      }
    },
    optimizeDeps: {
      include: [
        // 核心框架依赖 - 高优先级预构建
        "vue",
        "vue-router",
        "pinia",
        "pinia-plugin-persistedstate",
        // UI组件库 - 使用配置文件管理
        ...getElementPlusIncludes(false),
        // false表示不包含样式路径，因为已导入完整样式
        // 工具库 - 稳定依赖预构建
        "axios",
        "dayjs",
        "dayjs/locale/zh-cn",
        "lodash",
        "lodash-es",
        "@vueuse/core",
        "mitt",
        "nprogress",
        "qs",
        // 加密和工具
        "crypto-js",
        "md5",
        // 国际化
        "vue-i18n",
        // 数学计算
        "decimal.js",
        // 打印功能
        "print-js",
        // 拖拽排序
        "sortablejs",
        // 分割面板
        "split.js",
        // 字符串格式化
        "sprintf-js",
        // 键盘快捷键库
        "mousetrap"
      ],
      // 排除大型库和动态导入的依赖
      exclude: [
        // 图标库 - 按需加载（保留@iconify/json用于动态导入）
        "@iconify/vue",
        // 图表库 - 延迟加载
        "echarts",
        "@antv/g2plot",
        "@antv/x6",
        "@antv/x6-plugin-clipboard",
        "@antv/x6-plugin-dnd",
        "@antv/x6-plugin-export",
        "@antv/x6-plugin-history",
        "@antv/x6-plugin-keyboard",
        "@antv/x6-plugin-scroller",
        "@antv/x6-plugin-selection",
        "@antv/x6-plugin-snapline",
        "@antv/x6-plugin-transform",
        // 代码高亮 - 按需加载
        "highlight.js",
        "@highlightjs/vue-plugin",
        // Markdown处理 - 按需加载
        "markdown-it",
        // 图片裁剪 - 按需加载
        "vue-cropper",
        // 右键菜单 - 按需加载
        "v-contextmenu",
        // 实体编码 - 小型库
        "entities"
      ],
      // 开发环境强制重新预构建（生产环境设为false）
      force: process.env.NODE_ENV === "development" ? false : false,
      // 预构建入口
      entries: ["src/main.ts", "src/App.vue"]
    },
    server: {
      host: "0.0.0.0",
      port: viteEnv.VITE_PORT,
      open: viteEnv.VITE_OPEN,
      cors: true,
      // 开发服务器缓存配置
      fs: {
        // 允许访问工作区根目录之外的文件
        strict: false,
        // 缓存策略
        cachedChecks: true
      },
      // 预热常用文件，提升开发体验
      warmup: {
        clientFiles: ["src/main.ts", "src/App.vue", "src/layouts/index.vue", "src/routers/index.ts", "src/stores/index.ts"]
      },
      // Load proxy configuration from .env.development
      proxy: createProxy(viteEnv.VITE_PROXY)
    },
    plugins: [
      ...createVitePlugins(viteEnv),
      // Element Plus 配置已在 build/plugins.ts 中处理
      visualizer2({
        open: true,
        gzipSize: true,
        brotliSize: true,
        filename: "dist/stats.html"
      }),
      // 过滤 sourcemap 警告的插件
      {
        name: "suppress-sourcemap-warnings",
        buildStart() {
          const originalWarn = console.warn;
          console.warn = (...args) => {
            const message = args.join(" ");
            if (message.includes("Sourcemap for") && message.includes("points to missing source files")) {
              return;
            }
            if (message.includes("entities/lib/esm") && message.includes("points to missing source files")) {
              return;
            }
            originalWarn.apply(console, args);
          };
        },
        configureServer() {
          const originalWarn = console.warn;
          console.warn = (...args) => {
            const message = args.join(" ");
            if (message.includes("Sourcemap for") && message.includes("points to missing source files")) {
              return;
            }
            if (message.includes("entities/lib/esm") && message.includes("points to missing source files")) {
              return;
            }
            originalWarn.apply(console, args);
          };
        }
      }
    ],
    esbuild: {
      pure: viteEnv.VITE_DROP_CONSOLE ? ["console.log", "debugger"] : []
    },
    build: {
      outDir: "dist",
      minify: "esbuild",
      sourcemap: false,
      // 禁用 gzip 压缩大小报告，可略微减少打包时间
      reportCompressedSize: false,
      // 规定触发警告的 chunk 大小
      chunkSizeWarningLimit: 2e3,
      // 构建缓存配置
      emptyOutDir: true,
      // 静态资源处理
      assetsInlineLimit: 4096,
      // 小于4kb的资源内联为base64
      rollupOptions: {
        // 忽略有问题的 sourcemap 警告
        onwarn(warning, warn) {
          if (warning.code === "SOURCEMAP_ERROR") return;
          if (warning.message?.includes("entities/lib/esm") && warning.message?.includes("points to missing source files")) {
            return;
          }
          warn(warning);
        },
        // 缓存优化
        cache: true,
        // 外部依赖（如果需要CDN加载）
        external: [],
        output: {
          // 优化代码分割策略 - 基于缓存友好的文件名
          chunkFileNames: (chunkInfo) => {
            if (chunkInfo.name?.includes("vendor")) {
              return "assets/vendor/[name]-[hash].js";
            }
            if (chunkInfo.name?.includes("async")) {
              return "assets/async/[name]-[hash].js";
            }
            return "assets/chunks/[name]-[hash].js";
          },
          entryFileNames: "assets/entry/[name]-[hash].js",
          assetFileNames: (assetInfo) => {
            const extType = assetInfo.name?.split(".").pop() || "";
            if (["png", "jpg", "jpeg", "gif", "svg", "webp"].includes(extType)) {
              return "assets/images/[name]-[hash].[ext]";
            }
            if (["woff", "woff2", "ttf", "eot"].includes(extType)) {
              return "assets/fonts/[name]-[hash].[ext]";
            }
            if (["css"].includes(extType)) {
              return "assets/styles/[name]-[hash].[ext]";
            }
            return "assets/[ext]/[name]-[hash].[ext]";
          },
          // 手动分割代码块，优化缓存策略
          manualChunks: (id) => {
            if (id.includes("vue") && !id.includes("node_modules")) {
              return "vue-vendor";
            }
            if (id.includes("vue-router")) {
              return "vue-vendor";
            }
            if (id.includes("pinia")) {
              return "vue-vendor";
            }
            if (isElementPlusModule(id)) {
              return getElementPlusChunkName(id);
            }
            if (id.includes("@iconify")) {
              return "icons-vendor";
            }
            if (id.includes("axios") || id.includes("dayjs") || id.includes("lodash")) {
              return "utils-vendor";
            }
            if (id.includes("crypto-js") || id.includes("md5") || id.includes("qs")) {
              return "utils-vendor";
            }
            if (id.includes("echarts") || id.includes("@antv")) {
              return "charts-vendor";
            }
            if (id.includes("highlight.js") || id.includes("@highlightjs")) {
              return "highlight-vendor";
            }
            if (id.includes("vue-i18n")) {
              return "i18n-vendor";
            }
            if (id.includes("node_modules")) {
              return "vendor";
            }
            if (id.includes("src/views/")) {
              const match = id.match(/src\/views\/([^\/]+)/);
              if (match) {
                return `views-${match[1]}`;
              }
            }
            if (id.includes("src/components/")) {
              return "components";
            }
            if (id.includes("src/utils/") || id.includes("src/hooks/")) {
              return "utils";
            }
          }
        }
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
