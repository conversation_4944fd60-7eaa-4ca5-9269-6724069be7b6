# 装置列表国际化修复测试

## 问题描述
装置列表界面切换其他语言后，比如英文后，状态字段更新完还是中文。

## 问题根因
在装置断开连接的通知处理中，使用了硬编码的中文文本而不是国际化函数：

### 修复前的代码
```javascript
// Debug模块 - DeviceList.vue
debugStore.addConsole(`装置 ${targetDevice.name} 连接已断开`);
debugStore.addConsole(`当前装置连接已断开`);

// HMI模块 - DeviceList.vue  
debugStore.addConsole(`装置 ${targetDevice.name} 连接已断开`);
debugStore.addConsole(`当前装置连接已断开`);
```

### 修复后的代码
```javascript
// Debug模块 - DeviceList.vue
debugStore.addConsole(t("device.list.disconnectedNotify", { name: targetDevice.name }));
debugStore.addConsole(t("device.list.currentDisconnectedNotify"));

// HMI模块 - DeviceList.vue
debugStore.addConsole(t("hmi.device.deviceList.disconnectedNotify", { name: targetDevice.name }));
debugStore.addConsole(t("hmi.device.deviceList.currentDisconnectedNotify"));
```

## 修复内容

### 1. 添加国际化配置

#### 中文 (zh)
- `frontend/src/languages/zh/business/device.ts`
- `frontend/src/languages/zh/business/hmi.ts`

#### 英文 (en)
- `frontend/src/languages/en/business/device.ts`
- `frontend/src/languages/en/business/hmi.ts`

#### 俄语 (ru)
- `frontend/src/languages/ru/business/device.ts`
- `frontend/src/languages/ru/business/hmi.ts`

#### 西班牙语 (es)
- `frontend/src/languages/es/business/device.ts`
- `frontend/src/languages/es/business/hmi.ts`

#### 法语 (fr)
- `frontend/src/languages/fr/business/device.ts`
- `frontend/src/languages/fr/business/hmi.ts`

### 2. 新增的国际化键值

```javascript
// 设备模块
disconnectedNotify: "装置 {name} 连接已断开", // 中文
disconnectedNotify: "Device {name} connection disconnected", // 英文
currentDisconnectedNotify: "当前装置连接已断开", // 中文
currentDisconnectedNotify: "Current device connection disconnected", // 英文

// HMI模块
disconnectedNotify: "装置 {name} 连接已断开", // 中文
disconnectedNotify: "Device {name} connection disconnected", // 英文
currentDisconnectedNotify: "当前装置连接已断开", // 中文
currentDisconnectedNotify: "Current device connection disconnected", // 英文
```

### 3. 修改的文件
- `frontend/src/views/biz/debug/device/components/DeviceList.vue`
- `frontend/src/views/biz/hmi/device/components/DeviceList.vue`

## 测试步骤

1. 启动应用程序
2. 进入装置列表界面
3. 连接一个装置
4. 切换语言到英文
5. 断开装置连接（可以通过关闭装置或网络中断模拟）
6. 观察控制台消息是否显示为英文

## 预期结果

切换到英文后，装置断开连接的消息应该显示为：
- "Device [装置名称] connection disconnected"
- "Current device connection disconnected"

而不是之前的中文：
- "装置 [装置名称] 连接已断开"
- "当前装置连接已断开"

## 影响范围

此修复影响以下模块：
- Debug调试模块的装置列表
- HMI人机界面模块的装置列表
- 支持所有语言：中文、英文、俄语、西班牙语、法语
