# Руководство пользователя

## 1. Обзор функций

**VisualDebug** - это инструмент отладки, специально разработанный для устройств визуализации платформы Sieyuan (разработка и инженерная отладка и управление), интегрирующий богатые функции отладки, конфигурации, управления и вспомогательные функции, помогающий инженерам эффективно выполнять различные задачи отладки.

![Общий интерфейс инструмента](./help-ru/工具整体界面.png)

Основные функции включают:

- Отладка и управление устройствами
- Инструменты конфигурации
- IT-инструменты
- Настройки инструментов, конфигурация тем, переключение языков, дополнительные функции

Благодаря вышеперечисленным функциям VisualDebug может удовлетворить потребности полного цикла разработки и инженерной отладки, проектирования конфигураций и ежедневного управления устройствами визуализации платформы Sieyuan.

### 1.1 Отладка и управление устройствами

- Конфигурация информации подключения устройства
- Просмотр основной информации об устройстве
- Уставки, аналоговые величины, статусные величины, телесигнализация, телеизмерения, телеуправление, телерегулирование, выходные передачи, отчеты
- Синхронизация времени устройства, отладка переменных, импорт/экспорт уставок
- Загрузка файлов, скачивание файлов

![Отладка и управление устройствами](./help-ru/装置调试与管理.png)

### 1.2 Инструменты конфигурации

- Рисование графики конфигурации (предварительный просмотр, добавление, редактирование, пользовательские символы)
- Связывание информации об устройстве с графическими элементами

![Инструменты конфигурации](./help-ru/组态工具.png)

### 1.3 IT-инструменты

- Массовая загрузка, массовый импорт уставок
- Упаковка программ
- Форматирование XML/JSON
- Преобразование систем счисления, преобразование температуры
- Шифрование и дешифрование текста

![IT-инструменты](./help-ru/IT小工具.png)

### 1.4 Конфигурация проекта и системные настройки

- Импорт и экспорт конфигурации проекта
- Системная конфигурация, конфигурация параметров
- Настройка тем, переключение многоязычности

![Конфигурация проекта и системные настройки](./help-ru/工程配置导入和导出.png)

## 2. Основные модули

- **Панель меню**: Панель меню по умолчанию слева централизованно отображает все точки входа функций, включая отладку, конфигурацию, инструменты, настройки, темы, языки, дополнительно и т.д.
- **Отладка устройств**: Поддерживает подключение нескольких устройств, просмотр состояния устройства, параметров, переменных в реальном времени, синхронизацию времени устройства, загрузку файлов, скачивание файлов и т.д.
- **Функция конфигурации**: Рисование графики конфигурации (предварительный просмотр, добавление, редактирование, пользовательские символы), отображение информации интерфейса конфигурации устройства в реальном времени.
- **Консоль**: Отображение системных журналов, отладочной информации, сообщений об ошибках в реальном времени для удобства локализации проблем.
- **Многоязычная поддержка**: Можно переключаться между упрощенным китайским, английским и другими языками в настройках.
- **Переключение тем**: Можно свободно переключаться между светлой, темной и другими темами для улучшения визуального восприятия.
- **Управление конфигурацией**: Поддерживает импорт, экспорт, резервное копирование и восстановление файлов конфигурации проекта, удобно для миграции проектов и совместной работы.

![Общие модули инструмента](./help-ru/工具整体模块.png)

## 3. Требования к рабочей среде

Для обеспечения стабильной и эффективной работы программного обеспечения VisualDebug рекомендуется использовать следующую аппаратно-программную среду:

- **Операционная система**: Windows 10 и выше (рекомендуется 64-битная), поддерживает некоторые дистрибутивы Linux (например, Debian12+).
- **Процессор**: Двухъядерный и выше основной CPU архитектуры x86.
- **Память**: Рекомендуется 4 ГБ и выше, рекомендуется 8 ГБ и выше для лучшего опыта.
- **Дисковое пространство**: Для распаковки и запуска требуется не менее 500 МБ свободного дискового пространства.
- **Разрешение дисплея**: Рекомендуется 1366×768 и выше, рекомендуется разрешение Full HD 1920×1080.
- **Сетевая среда**: При отладке устройств необходимо обеспечить нахождение в одной локальной сети с целевым устройством.

- **Примечание:**
  - Рекомендуется запускать программное обеспечение с правами администратора, чтобы избежать проблем конфигурации из-за недостаточных прав.
  - Этот инструмент является портативным программным обеспечением, не требует сложной установки, может работать непосредственно после распаковки, не требует изменений реестра или переменных среды системы. Все пользовательские данные и файлы конфигурации сохраняются в каталоге программного обеспечения, удобно для резервного копирования и миграции.

## 4. Активация программного обеспечения

Программное обеспечение VisualDebug использует механизм локальной активации авторизации, при первом использовании необходимо выполнить операцию активации. Процесс активации следующий:

![Активация авторизации](./help-ru/授权激活.png)

1. **Получение кода машины**

   - После запуска программного обеспечения, если оно не активировано, автоматически появится окно активации, отображающее уникальный код машины.
   - Также можно просмотреть код машины на странице "Дополнительно" > "О программе" в панели меню.

2. **Запрос кода активации**

   - Отправьте код машины администратору программного обеспечения или персоналу технической поддержки для получения кода активации.

3. **Ввод кода активации**

   - В окне активации введите полученный код активации, нажмите кнопку "Активировать".
   - После успешной активации программное обеспечение перейдет в нормальное состояние использования.

4. **Примечания**

   - Код машины привязан к аппаратной информации компьютера, при смене оборудования может потребоваться повторная активация.
   - Код активации действителен только для соответствующего кода машины, не может использоваться на других машинах.
   - Если активация не удалась, проверьте правильность ввода кода активации или обратитесь в техническую поддержку.

## 5. Функции отладки

Модуль отладки является основной функциональной областью VisualDebug, предоставляющей полный набор инструментов отладки и управления устройствами. Через этот модуль пользователи могут подключаться к различным устройствам, просматривать данные устройств в реальном времени, выполнять операции управления и настройки.

### 5.1 Описание конфигурации устройства

Конфигурация устройства является первым шагом для использования функций отладки. Пользователи должны правильно настроить параметры подключения устройства, чтобы обеспечить нормальную связь между программным обеспечением и целевым устройством.

![Конфигурация устройства](./help-ru/装置配置说明.png)

**Шаги конфигурации:**

1. **Выбор типа устройства**
   - В левой панели выберите соответствующий тип устройства
   - Система поддерживает различные типы устройств и протоколы связи

2. **Настройка параметров подключения**
   - **IP-адрес**: Введите IP-адрес целевого устройства
   - **Порт**: Установите номер порта связи (по умолчанию обычно 102)
   - **Протокол**: Выберите соответствующий протокол связи
   - **Таймаут**: Установите время ожидания подключения

3. **Тестирование подключения**
   - Нажмите кнопку "Тест подключения" для проверки связи
   - Если тест прошел успешно, можно сохранить конфигурацию

4. **Сохранение конфигурации**
   - Нажмите кнопку "Сохранить" для сохранения параметров конфигурации
   - Сохраненная конфигурация будет отображаться в списке устройств

**Примечания:**
- Убедитесь, что компьютер и целевое устройство находятся в одной сети
- Проверьте правильность IP-адреса и номера порта
- Некоторые устройства могут требовать специальных настроек протокола

### 5.2 Управление информацией о группах

Функция управления группами позволяет организовать устройства в логические группы для удобства управления и мониторинга.

**Создание группы:**
1. Нажмите кнопку "Создать группу"
2. Введите название группы
3. Выберите устройства для включения в группу
4. Сохраните настройки группы

**Управление группами:**
- Редактирование названия группы
- Добавление/удаление устройств из группы
- Удаление группы
- Копирование настроек группы

### 5.3 Общий обзор информации о группах устройств

Страница обзора предоставляет сводную информацию о всех группах устройств и их текущем состоянии.

**Отображаемая информация:**
- Количество устройств в каждой группе
- Статус подключения устройств
- Общие параметры группы
- Статистика работы группы

### 5.4 Основная информация об устройстве

Раздел основной информации отображает детальные сведения о выбранном устройстве.

**Информация включает:**
- Название и тип устройства
- Версия прошивки
- Серийный номер
- IP-адрес и порт
- Время последнего подключения
- Статус связи

### 5.5 Управление и операции с телесигнализацией

Телесигнализация (ТС) предоставляет информацию о дискретных состояниях устройства.

**Функции:**
- Просмотр текущих состояний ТС
- Мониторинг изменений состояний
- Настройка алармов и уведомлений
- Экспорт данных ТС

**Операции:**
- Обновление списка ТС
- Фильтрация по состоянию
- Поиск по названию
- Экспорт в различные форматы

### 5.6 Управление и операции с телеизмерениями

Телеизмерения (ТИ) предоставляют аналоговые значения параметров устройства.

**Функции:**
- Просмотр текущих значений ТИ
- Мониторинг трендов
- Настройка пределов и алармов
- Архивирование данных

**Операции:**
- Обновление значений в реальном времени
- Настройка интервалов опроса
- Экспорт исторических данных
- Построение графиков

### 5.7 Управление и операции с телеуправлением

Телеуправление (ТУ) позволяет дистанционно управлять устройствами.

**Функции:**
- Отправка команд управления
- Подтверждение выполнения команд
- Мониторинг состояния команд
- Журнал выполненных команд

**Безопасность:**
- Подтверждение критических команд
- Блокировка несанкционированных операций
- Аудит всех команд управления

### 5.8 Управление и операции с телерегулированием

Телерегулирование (ТР) предназначено для установки аналоговых уставок.

**Функции:**
- Установка новых значений уставок
- Проверка допустимых диапазонов
- Подтверждение изменений
- Откат к предыдущим значениям

### 5.9 Управление и операции с выходными передачами

Выходные передачи управляют внешними устройствами и сигналами.

**Функции:**
- Управление состоянием выходов
- Мониторинг текущих состояний
- Настройка логики работы
- Диагностика выходных цепей

### 5.10 Управление и операции с уставками

Уставки определяют параметры работы защитных и автоматических функций устройства.

**Функции управления уставками:**
- Просмотр текущих значений уставок
- Редактирование значений уставок
- Проверка допустимых диапазонов
- Сохранение изменений в устройство

**Операции:**
- Чтение уставок из устройства
- Запись уставок в устройство
- Сравнение с эталонными значениями
- Создание резервных копий уставок

### 5.11 Управление и операции со всеми уставками

Функция массового управления уставками позволяет работать с полным набором параметров устройства.

**Возможности:**
- Массовое чтение всех уставок
- Групповое изменение параметров
- Импорт/экспорт полного набора уставок
- Сравнение конфигураций

### 5.12 Функция синхронизации времени устройства

Синхронизация времени обеспечивает точность временных меток в устройстве.

**Режимы синхронизации:**
- Автоматическая синхронизация по расписанию
- Ручная синхронизация по требованию
- Синхронизация с внешним источником времени

**Настройки:**
- Интервал автоматической синхронизации
- Источник эталонного времени
- Допустимое отклонение времени

### 5.13 Функция загрузки файлов

Загрузка файлов позволяет передавать файлы с компьютера на устройство.

**Поддерживаемые типы файлов:**
- Файлы конфигурации
- Файлы прошивки
- Файлы уставок
- Пользовательские файлы

**Процесс загрузки:**
1. Выберите файл для загрузки
2. Укажите целевую папку на устройстве
3. Запустите процесс передачи
4. Дождитесь завершения загрузки

### 5.14 Функция скачивания файлов

Скачивание файлов позволяет получать файлы с устройства на компьютер.

**Типы файлов для скачивания:**
- Журналы событий
- Файлы конфигурации
- Отчеты и статистика
- Диагностические файлы

**Процесс скачивания:**
1. Выберите файлы на устройстве
2. Укажите папку назначения
3. Запустите процесс скачивания
4. Проверьте целостность полученных файлов

### 5.15 Описание функций операций с устройством

Раздел операций предоставляет доступ к специальным функциям устройства.

**Доступные операции:**
- Перезагрузка устройства
- Сброс к заводским настройкам
- Диагностические тесты
- Калибровка измерительных каналов

### 5.16 Описание функции отладки переменных

Отладка переменных позволяет мониторить и изменять внутренние переменные устройства.

**Возможности:**
- Просмотр значений переменных в реальном времени
- Изменение значений переменных
- Установка точек останова
- Трассировка изменений

### 5.17 Описание функции отчетов SOE

SOE (Sequence of Events) отчеты содержат хронологическую последовательность событий.

**Содержание отчетов:**
- Временные метки событий
- Описание событий
- Приоритет событий
- Дополнительная информация

### 5.18 Описание функции отчетов о работе

Отчеты о работе предоставляют статистику функционирования устройства.

**Включаемая информация:**
- Время работы устройства
- Количество операций
- Статистика ошибок
- Производительность системы

### 5.19 Описание функции групповых отчетов

Групповые отчеты предоставляют информацию о работе группы устройств как единого целого.

**Содержание отчетов:**
- Сводная статистика по группе
- Сравнительный анализ устройств
- Общие тренды и закономерности
- Рекомендации по оптимизации

### 5.20 Описание функции отчетов о действиях

Отчеты о действиях фиксируют все операции, выполненные пользователями и системой.

**Регистрируемые действия:**
- Команды управления
- Изменения настроек
- Операции с файлами
- Системные события

### 5.21 Описание функции аудиторских отчетов

Аудиторские отчеты обеспечивают полную трассируемость всех операций в системе.

**Функции аудита:**
- Регистрация всех действий пользователей
- Контроль доступа к функциям
- Анализ безопасности системы
- Соответствие требованиям регулирования

### 5.22 Описание функции настроек

Раздел настроек позволяет конфигурировать параметры работы системы.

**Категории настроек:**
- Сетевые параметры
- Параметры интерфейса
- Настройки безопасности
- Параметры журналирования

### 5.23 Описание функции настроек темы

Настройки темы позволяют персонализировать внешний вид интерфейса.

**Доступные опции:**
- Выбор цветовой схемы
- Настройка размеров элементов
- Конфигурация макета
- Персонализация панелей

### 5.24 Описание функции настроек языка

Функция настроек языка обеспечивает многоязычную поддержку интерфейса.

**Поддерживаемые языки:**
- Русский
- Упрощенный китайский
- Английский
- Испанский
- Французский

### 5.25 Функция консоли

Консоль предоставляет интерфейс для мониторинга системных сообщений и отладки.

**Возможности консоли:**
- Просмотр системных журналов
- Фильтрация сообщений по типу
- Поиск по содержимому
- Экспорт журналов

## 6. Функции конфигурации

### 6.1 Назначение

Модуль конфигурации предназначен для создания и редактирования графических схем устройств и систем.

### 6.2 Расположение

Функции конфигурации доступны через главное меню в разделе "Конфигурация".

### 6.3 Макет интерфейса

Интерфейс конфигурации состоит из:
- Панели инструментов
- Области рисования
- Панели свойств
- Библиотеки элементов

### 6.4 Список устройств

Список устройств отображает все доступные для конфигурации устройства.

**Функции списка:**
- Добавление новых устройств
- Редактирование параметров устройств
- Удаление устройств
- Группировка устройств

### 6.5 Список конфигураций

Управление существующими конфигурациями и создание новых.

**Операции с конфигурациями:**
- Создание новой конфигурации
- Открытие существующей конфигурации
- Сохранение изменений
- Экспорт/импорт конфигураций

### 6.6 Рисование конфигурации

Инструменты для создания графических схем.

**Доступные инструменты:**
- Линии и соединения
- Геометрические фигуры
- Текстовые элементы
- Символы устройств

### 6.7 Мониторинг конфигурации

Отображение реальных данных на графической схеме.

**Функции мониторинга:**
- Обновление значений в реальном времени
- Цветовая индикация состояний
- Анимация процессов
- Алармы и уведомления

### 6.8 Пользовательские символы

Создание и управление пользовательскими графическими символами.

**Возможности:**
- Создание новых символов
- Редактирование существующих символов
- Импорт символов из библиотек
- Экспорт символов для совместного использования

## 7. IT-инструменты

### 7.1 Массовая загрузка

Функция массовой загрузки позволяет одновременно загружать файлы на несколько устройств.

**Возможности:**
- Выбор множественных целевых устройств
- Загрузка одного файла на все устройства
- Мониторинг прогресса загрузки
- Отчет о результатах операции

### 7.2 Функция упаковки программ

Инструмент для создания установочных пакетов и архивов.

**Функции:**
- Создание архивов проектов
- Упаковка конфигураций
- Создание резервных копий
- Подготовка файлов для передачи

### 7.3 Форматирование XML

Инструмент для форматирования и валидации XML-документов.

**Возможности:**
- Автоматическое форматирование XML
- Проверка синтаксиса
- Сжатие XML-кода
- Подсветка синтаксиса

### 7.4 Форматирование JSON

Инструмент для работы с JSON-данными.

**Функции:**
- Форматирование JSON
- Валидация структуры
- Сжатие JSON
- Конвертация между форматами

### 7.5 Преобразование систем счисления

Конвертер для преобразования чисел между различными системами счисления.

**Поддерживаемые системы:**
- Двоичная (2)
- Восьмеричная (8)
- Десятичная (10)
- Шестнадцатеричная (16)

### 7.6 Преобразование температуры

Конвертер температурных единиц.

**Поддерживаемые единицы:**
- Цельсий (°C)
- Фаренгейт (°F)
- Кельвин (K)
- Ранкин (°R)

### 7.7 Шифрование и дешифрование текста

Инструмент для защиты текстовой информации.

**Поддерживаемые алгоритмы:**
- AES
- TripleDES
- Rabbit
- RC4

## 8. Дополнительные функции

### 8.1 Импорт конфигурации проекта

Функция импорта позволяет загружать конфигурации из внешних файлов.

**Поддерживаемые форматы:**
- Собственный формат VisualDebug
- Стандартные форматы конфигураций
- XML-файлы конфигураций
- Архивы проектов

**Процесс импорта:**
1. Выберите файл конфигурации
2. Проверьте совместимость формата
3. Настройте параметры импорта
4. Запустите процесс импорта
5. Проверьте результаты импорта

### 8.2 Экспорт конфигурации проекта

Экспорт конфигураций для резервного копирования или передачи.

**Форматы экспорта:**
- Полный архив проекта
- Отдельные файлы конфигурации
- Документация проекта
- Отчеты по конфигурации

**Процесс экспорта:**
1. Выберите элементы для экспорта
2. Укажите формат экспорта
3. Настройте параметры экспорта
4. Выберите место сохранения
5. Запустите процесс экспорта

### 8.3 Функция скриншотов

Создание снимков экрана для документации и отчетов.

**Возможности:**
- Скриншот всего экрана
- Скриншот активного окна
- Скриншот выделенной области
- Автоматическое сохранение

**Настройки скриншотов:**
- Формат изображения (PNG, JPEG, BMP)
- Качество изображения
- Папка для сохранения
- Автоматическое именование файлов

### 8.4 Поиск в меню

Быстрый поиск функций в интерфейсе программы.

**Функции поиска:**
- Поиск по названию функции
- Поиск по ключевым словам
- Быстрый переход к найденной функции
- История поиска

**Использование:**
1. Нажмите Ctrl+F или используйте поле поиска
2. Введите ключевые слова
3. Выберите нужную функцию из результатов
4. Нажмите Enter для перехода

### 8.5 Справочная документация

Доступ к справочной информации и документации.

**Содержание справки:**
- Руководство пользователя
- Техническая документация
- Примеры использования
- Часто задаваемые вопросы

### 8.6 Информация о программе

Раздел с информацией о версии и лицензии программы.

**Отображаемая информация:**
- Версия программы
- Информация о лицензии
- Контактная информация
- Системная информация

## 9. Часто задаваемые вопросы (FAQ)

- **В: После подключения устройства меню групп отображается неправильно?**
  - Проверьте, обновлен ли urpc в каталоге shr устройства до версии, совместимой с инструментом.
  - Прочитайте debug_info.xml из каталога shr, загрузите локально и обратитесь в техническую поддержку для локализации проблемы.

- **В: Не удается подключиться к устройству?**
  - Проверьте питание устройства и сетевое подключение, убедитесь, что оно находится в той же локальной сети, что и компьютер.
  - Проверьте настройки брандмауэра, убедитесь, что программа имеет разрешение на доступ к сети.

- **В: Импорт конфигурации не удался?**
  - Убедитесь, что импортируемый файл конфигурации имеет правильный формат и поддерживается **VisualDebug**.

- **В: Зашифрованный файл Excel не может быть импортирован?**
  - Из-за влияния IT-политик зашифрованные файлы Excel не могут быть распознаны. Если необходимо импортировать зашифрованный файл Excel, сначала расшифруйте его самостоятельно.

- **В: Интерфейс программы отображается неправильно?**
  - Попробуйте переключить тему или перезапустить программу. Если проблема сохраняется, обратитесь в техническую поддержку.

- **В: После импорта уставок различающиеся уставки не отображаются?**
  - Убедитесь, что названия групп уставок в импортируемом файле Excel/xml/csv полностью совпадают с названиями групп в меню групп инструмента. Если они не совпадают, различия не могут быть сравнены.

- **В: В какой каталог устройства загружается файл, созданный функцией упаковки программ?**
  - Упакованный zip-файл имеет зашифрованный формат и должен быть загружен в каталог /dwld устройства. После завершения загрузки необходимо перезапустить устройство для вступления изменений в силу.

- **В: Почему не работает перезапуск после загрузки файла, хотя опция "Перезапустить после завершения загрузки" была отмечена?**
  ![Ошибка перезапуска](./help-ru/重启失败.png)
  - Перезапуск предъявляет требования к прошивке платы CPU. Если ls -l /sbin/reboot показывает символическую ссылку, как на изображении выше, то reboot не поддерживается и необходимо обновить прошивку.

## 10. Техническая поддержка

При возникновении неразрешимых проблем обращайтесь к нам следующими способами:

- **Компания**: ООО "Сыюань Электрик"
- **Отдел**: Центральный научно-исследовательский институт - Отдел разработки встраиваемых приложений
- **Профессиональная группа**: Группа разработки программных инструментов

Благодарим вас за использование данного программного обеспечения, желаем успешной отладки!
