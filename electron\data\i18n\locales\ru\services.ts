/**
 * Связанное с сервисами - Русский
 */
export default {
  // Сервис лицензий
  license: {
    cacheResult: "Использование кэшированного результата",
    verificationTime: "Время проверки",
    verificationFailed: "Проверка не удалась, время",
    businessErrorInfo: "Получение информации о бизнес-ошибке",
    getMachineCode: "Получение кода машины",
    checkAuth: "Проверка авторизации",
    activate: "Активация лицензии",
  },

  // Кроссплатформенный сервис
  cross: {
    startGoService: "Начало запуска Go-сервиса...",
    goServiceStartSuccess: "Go-сервис запущен успешно, время",
    goServiceStartFailed: "Запуск Go-сервиса не удался, время",
    startPythonService: "Начало запуска Python-сервиса...",
    pythonServiceStartSuccess: "Python-сервис запущен успешно, время",
    pythonServiceStartFailed: "Запуск Python-сервиса не удался, время",
    optimizeStartParams: "Оптимизация параметров запуска",
  },

  // Базовый сервис
  base: {
    getClientStart: "Начало получения клиента устройства, ID устройства",
    deviceNotFound: "Информация об устройстве не найдена, ID устройства",
    deviceNotConnected:
      "Устройство не подключено или клиент недействителен, ID устройства",
    getClientSuccess: "Успешно получен клиент устройства, ID устройства",
  },

  // Сервис подключения устройств
  deviceConnect: {
    deviceConnected: "Устройство подключено",
    connectionAttempt: "Попытка подключения",
    clientObtained: "Клиент получен, ID устройства",
    clientNotObtained: "Клиент не получен, ID устройства",
    connectionException: "Исключение подключения",
    connectionCheckException: "Исключение проверки подключения",
    connectDeviceByRpc: "Подключение устройства через RPC",
    callInterface: "Вызов интерфейса",
    useDeviceIdAsKey: "Использование ID устройства в качестве ключа",
    getConnectionFromGlobal:
      "Получение объекта подключения из глобальных переменных",
    notifyFrontendConnectionStatus:
      "Уведомление фронтенда об изменении статуса подключения",
    retryInterval: "Опционально: интервал между повторными попытками",
    cacheSuccessfulConnection: "Кэширование успешного объекта подключения",
    resetData: "Сброс данных",
    compareMd5AndSetFilePath: "Сравнение MD5 и установка пути к файлу",
    addDebugInfoToGlobal:
      "Добавление debugInfo и debugItemMap в realSingleGlobalDeviceInfo",
    connectionSuccessful: "Подключение успешно, ID устройства",
    disconnectDevice: "Отключение устройства",
    getConnection: "Получение подключения",
    deleteConnectionObject: "Удаление объекта подключения",
    disconnectSuccessful: "Отключение успешно, ID устройства",
    disconnectException: "Исключение отключения",
    startMessageMonitor: "Запуск мониторинга сообщений",
    stopMessageMonitor: "Остановка мониторинга сообщений",
    messageMonitorStarted: "Мониторинг сообщений запущен, ID устройства",
    messageMonitorStopped: "Мониторинг сообщений остановлен, ID устройства",
    messageMonitorException: "Исключение мониторинга сообщений",
    deviceNotConnected: "Устройство не подключено",
  },

  // Сервис информации об устройстве
  deviceInfo: {
    getDeviceInfoStart: "Начало получения информации об устройстве",
    exportStart: "Начало экспорта информации об устройстве",
    exportSuccess: "Экспорт информации об устройстве успешен",
  },

  // Сервис конфигурации
  configure: {
    getConfigureList: "Получение списка конфигураций",
    addConfigure: "Добавление конфигурации",
    setId: "Установка ID",
    projectNotExists: "Проект не существует",
    duplicateName: "Дублирующееся имя, пожалуйста, введите заново",
    addConfigureException: "Исключение добавления конфигурации",
    projectNotFound: "Проект не найден",
    projectPathNotFound: "Путь к проекту не найден",
    replaceWithNew: "Заменить новым содержимым",
    projectNotFoundShort: "Проект не найден",
    operationTypeIncorrect:
      "Неправильный тип операции, допустимые значения [project,hmi]",
    renameConfigureException: "Исключение переименования конфигурации",
    getConfigureListException: "Исключение получения списка конфигураций",
    configureSaveException: "Исключение сохранения конфигурации",
    openConfigureFolder: "Открытие папки конфигурации",
  },

  // Сервис окон
  window: {
    windowStateInitialized: "Управление состоянием окна инициализировано",
    windowStateInitFailed:
      "Инициализация управления состоянием окна не удалась",
    windowStateSaved: "Состояние окна сохранено",
    saveWindowStateFailed: "Сохранение состояния окна не удалось",
    windowStateRestored: "Состояние окна восстановлено",
    restoreWindowStateFailed: "Восстановление состояния окна не удалось",
    clickNotification: "Клик по уведомлению",
    closeNotification: "Закрытие уведомления",
    createWindow: "Создание окна",
    windowCreated: "Окно создано",
    windowCreationFailed: "Создание окна не удалось",
    getWindowId: "Получение ID окна",
    windowCommunication: "Межоконная связь",
    notificationCreated: "Уведомление создано",
    windowClosed: "Окно закрыто",
    windowMaximized: "Окно развернуто",
    windowMinimized: "Окно свернуто",
    windowDragged: "Перетаскивание окна завершено",
    screenshotTaken: "Скриншот сохранен",
    devToolsOpened: "Инструменты разработчика открыты",
    devToolsClosed: "Инструменты разработчика закрыты",
  },

  // Сервис системных событий
  systemEvents: {
    systemEventsInitialized: "Прослушивание системных событий инициализировано",
    eventListenersSet: "Слушатели системных событий установлены",
    setupEventListenersFailed: "Установка слушателей событий не удалась",
    systemSuspending: "Система переходит в спящий режим",
    systemResuming: "Система пробуждена",
    screenLocked: "Экран заблокирован",
    screenUnlocked: "Экран разблокирован",
    systemShuttingDown: "Система завершает работу",
    appBeforeQuit: "Приложение завершает работу",
    allWindowsClosed: "Все окна закрыты",
    appActivated: "Приложение активировано",
    windowBlurred: "Окно потеряло фокус",
    windowFocused: "Окно получило фокус",
    windowMinimized: "Окно свернуто",
    windowRestored: "Окно восстановлено",
    applicationStateSaved: "Состояние приложения сохранено",
    applicationStateRestored: "Состояние приложения восстановлено",
    powerInfoRetrieved: "Информация о питании получена",
    cleanupCompleted: "Очистка завершена",
  },

  // Сервис системного трея
  tray: {
    trayLoaded: "Трей загружен",
    trayCreated: "Трей создан",
    trayCreationFailed: "Создание трея не удалось",
  },

  // Сервис базы данных
  database: {
    databaseConnected: "База данных подключена",
    databaseConnectionFailed: "Подключение к базе данных не удалось",
    queryExecuted: "Запрос выполнен",
    queryFailed: "Запрос не удался",
    dataInserted: "Данные вставлены",
    dataUpdated: "Данные обновлены",
    dataDeleted: "Данные удалены",
    transactionStarted: "Транзакция начата",
    transactionCommitted: "Транзакция зафиксирована",
    transactionRolledBack: "Транзакция откачена",
    tableCreated: "Таблица создана",
    tableCreationFailed: "Создание таблицы не удалось",
    dataInsertFailed: "Вставка данных не удалась",
    dataDeleteFailed: "Удаление данных не удалось",
    dataUpdateFailed: "Обновление данных не удалось",
    dataDirRetrieved: "Каталог данных получен",
    dataDirRetrieveFailed: "Получение каталога данных не удалось",
    customDataDirSet: "Пользовательский каталог данных установлен",
    customDataDirSetFailed:
      "Установка пользовательского каталога данных не удалась",
  },

  // HMI сервис
  hmi: {
    graphDefined: "График определен",
    configurationLoaded: "Конфигурация загружена",
    viewCreated: "Представление создано",
    projectOpened: "Проект открыт",
    projectSaved: "Проект сохранен",
    elementAdded: "Элемент добавлен",
    elementModified: "Элемент изменен",
    elementDeleted: "Элемент удален",
  },

  // Сервис заданий
  job: {
    jobStarted: "Задание начато",
    jobCompleted: "Задание завершено",
    jobFailed: "Задание не удалось",
    jobCancelled: "Задание отменено",
    jobPaused: "Задание приостановлено",
    jobResumed: "Задание возобновлено",
    jobScheduled: "Задание запланировано",
    jobRemoved: "Задание удалено",
  },

  // Матричный сервис
  matrix: {
    matrixInitialized: "Матрица инициализирована",
    matrixCalculationStarted: "Вычисление матрицы начато",
    matrixCalculationCompleted: "Вычисление матрицы завершено",
    matrixOperationFailed: "Операция с матрицей не удалась",
    dataProcessed: "Обработка данных завершена",
    algorithmExecuted: "Алгоритм выполнен",
    encryptCompressionComplete: "Шифрование и сжатие завершено",

    // Связанное с экспортом
    exportDeviceListEntry: "Точка входа экспорта списка устройств",
    exportDeviceListStart: "Начало экспорта списка устройств",
    exportDeviceListComplete: "Экспорт списка устройств завершен",
    deviceListSheetName: "Список устройств",
    exportDownloadListStart: "Начало экспорта списка загрузок",
    exportParamListStart: "Начало экспорта списка параметров",

    // Перевод заголовков
    indexHeader: "Номер",
    deviceNameHeader: "Имя устройства",
    deviceAddressHeader: "Адрес устройства",
    devicePortHeader: "Порт устройства",
    encryptedHeader: "Зашифровано",
    downFileHeader: "Файл загрузки",
    importParamHeader: "Параметр импорта",
    connectTimeoutHeader: "Тайм-аут подключения",
    paramTimeoutHeader: "Тайм-аут параметра",
    readTimeoutHeader: "Тайм-аут чтения",
    fileNameHeader: "Имя файла",
    fileSizeHeader: "Размер файла",
    filePathHeader: "Путь к файлу",
    lastModifiedHeader: "Последнее изменение",
    paramNameHeader: "Название",
    paramDescHeader: "Описание",
    valueHeader: "Значение",
    minValueHeader: "Минимальное значение",
    maxValueHeader: "Максимальное значение",
    stepHeader: "Шаг",
    unitHeader: "Единица измерения",
  },

  // Дополнительный сервис
  more: {
    importPathNotExists: "Путь импорта не существует",
    exportPathNotExists: "Путь экспорта не существует",
    selectCorrectConfigFile:
      "Пожалуйста, выберите правильный файл конфигурации",
    exportProjectConfigException:
      "Исключение при экспорте конфигурации проекта",
    importProjectConfigException: "Исключение при импорте конфигурации проекта",
  },
};
