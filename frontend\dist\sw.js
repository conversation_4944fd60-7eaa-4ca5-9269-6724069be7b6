/**
 * Copyright 2018 Google Inc. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *     http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// If the loader is already loaded, just stop.
if (!self.define) {
  let registry = {};

  // Used for `eval` and `importScripts` where we can't get script URL by other means.
  // In both cases, it's safe to use a global var because those functions are synchronous.
  let nextDefineUri;

  const singleRequire = (uri, parentUri) => {
    uri = new URL(uri + ".js", parentUri).href;
    return registry[uri] || (
      
        new Promise(resolve => {
          if ("document" in self) {
            const script = document.createElement("script");
            script.src = uri;
            script.onload = resolve;
            document.head.appendChild(script);
          } else {
            nextDefineUri = uri;
            importScripts(uri);
            resolve();
          }
        })
      
      .then(() => {
        let promise = registry[uri];
        if (!promise) {
          throw new Error(`Module ${uri} didn’t register its module`);
        }
        return promise;
      })
    );
  };

  self.define = (depsNames, factory) => {
    const uri = nextDefineUri || ("document" in self ? document.currentScript.src : "") || location.href;
    if (registry[uri]) {
      // Module is already loading or loaded.
      return;
    }
    let exports = {};
    const require = depUri => singleRequire(depUri, uri);
    const specialDeps = {
      module: { uri },
      exports,
      require
    };
    registry[uri] = Promise.all(depsNames.map(
      depName => specialDeps[depName] || require(depName)
    )).then(deps => {
      factory(...deps);
      return exports;
    });
  };
}
define(['./workbox-b9370a86'], (function (workbox) { 'use strict';

  self.skipWaiting();
  workbox.clientsClaim();

  /**
   * The precacheAndRoute() method efficiently caches and responds to
   * requests for URLs in the manifest.
   * See https://goo.gl/S9QRab
   */
  workbox.precacheAndRoute([{
    "url": "assets/chunks/components-BkHQV7Qm.js",
    "revision": null
  }, {
    "url": "assets/chunks/element-components-CkwEs5_B.js",
    "revision": null
  }, {
    "url": "assets/chunks/element-core-DyPKvxS2.js",
    "revision": null
  }, {
    "url": "assets/chunks/element-icons-DbNYNmpr.js",
    "revision": null
  }, {
    "url": "assets/chunks/index-BsuDHVIl.js",
    "revision": null
  }, {
    "url": "assets/chunks/utils-DeqEhvvR.js",
    "revision": null
  }, {
    "url": "assets/chunks/views-biz-DnwQrgNu.js",
    "revision": null
  }, {
    "url": "assets/entry/index-ByIOTQIq.js",
    "revision": null
  }, {
    "url": "assets/styles/element-85kFTNYz.css",
    "revision": null
  }, {
    "url": "assets/styles/element-core-BlK98vAp.css",
    "revision": null
  }, {
    "url": "assets/styles/element-dark-DWIGLUio.css",
    "revision": null
  }, {
    "url": "assets/styles/font-D0q-O3lU.css",
    "revision": null
  }, {
    "url": "assets/styles/highlight-vendor-ZgkIHsf0.css",
    "revision": null
  }, {
    "url": "assets/styles/iconfont-DRZ30ItQ.css",
    "revision": null
  }, {
    "url": "assets/styles/iconfont-kWcO7Ai6.css",
    "revision": null
  }, {
    "url": "assets/styles/index-CD6qC75v.css",
    "revision": null
  }, {
    "url": "assets/styles/vendor-CaccPJo9.css",
    "revision": null
  }, {
    "url": "assets/styles/vue-vendor-DNbd9zSU.css",
    "revision": null
  }, {
    "url": "assets/vendor/charts-vendor-DS7xuoj-.js",
    "revision": null
  }, {
    "url": "assets/vendor/highlight-vendor-DzhJ4giz.js",
    "revision": null
  }, {
    "url": "assets/vendor/i18n-vendor-BPpKJ4WV.js",
    "revision": null
  }, {
    "url": "assets/vendor/icons-vendor-DBJjj0eo.js",
    "revision": null
  }, {
    "url": "assets/vendor/utils-vendor-C_ezCHls.js",
    "revision": null
  }, {
    "url": "assets/vendor/vendor-P-ltm-Yc.js",
    "revision": null
  }, {
    "url": "assets/vendor/vue-vendor-EiOvILk3.js",
    "revision": null
  }, {
    "url": "index.html",
    "revision": "bed7c8299ef6f41d929270e280eaff70"
  }, {
    "url": "registerSW.js",
    "revision": "402b66900e731ca748771b6fc5e7a068"
  }, {
    "url": "stats.html",
    "revision": "307dbe4df8c4ce4b119bdf57bcd7cdca"
  }, {
    "url": "logo.png",
    "revision": "2189ea651ccbb1794f70f4dd2176ee74"
  }, {
    "url": "manifest.webmanifest",
    "revision": "7866e6299427eba5ee0eb954c2a32df6"
  }], {});
  workbox.cleanupOutdatedCaches();
  workbox.registerRoute(new workbox.NavigationRoute(workbox.createHandlerBoundToURL("index.html")));

}));
