import{a5 as a}from"./utils-DeqEhvvR.js";import"../vendor/vue-vendor-EiOvILk3.js";import"../vendor/utils-vendor-C_ezCHls.js";import"../vendor/highlight-vendor-DzhJ4giz.js";import"../vendor/vendor-P-ltm-Yc.js";import"./element-core-DyPKvxS2.js";import"./element-icons-DbNYNmpr.js";import"../vendor/i18n-vendor-BPpKJ4WV.js";import"./element-components-CkwEs5_B.js";import"./components-BkHQV7Qm.js";import"../vendor/charts-vendor-DS7xuoj-.js";import"./views-biz-DnwQrgNu.js";import"../vendor/icons-vendor-DBJjj0eo.js";/**
 * @description 按钮权限指令
 * @license Apache License Version 2.0
 */const c={mounted(o,t){const{value:e,arg:s}=t,i=a().authButtonListGet??[];if(e instanceof Array&&e.length){const n=t.modifiers.and||s==="and"?"every":"some";e[n](m=>i.includes(m))||o.remove()}else i.includes(e)||o.remove()}};/**
 * @description  全局指令注册
 * @license Apache License Version 2.0
 */const r={auth:c},B={install:function(o){Object.keys(r).forEach(t=>{o.directive(t,r[t])})}};export{B as default};
