export default {
  language: {
    title: "Язы<PERSON>",
    zh: "Упрощенный китайский",
    en: "Английский",
    es: "Испанский",
    fr: "Французский",
    ru: "Русский",
    tooltip: "Многоязычность"
  },
  about: {
    title: "О программе",
    introduction: "Введение",
    description:
      "Инструмент отладки визуализационной платформы нового поколения, разработанный на основе новейшего технологического стека Vue3, TypeScript, Vite4, Pinia, Element-Plus, Electron и др.",
    versionInfo: "Информация о версии",
    toolName: "Название инструмент<PERSON>",
    version: "Номер версии",
    machineCode: "Код машины",
    loading: "Загрузка...",
    machineCodeError: "Получение не удалось",
    copySuccess: "Код машины скопирован в буфер обмена",
    copyError: "Копирование не удалось",
    versionFeatures: "Особенности версии",
    features: {
      visualTool:
        "Включает подключение инструментов визуализации, просмотр информации об устройстве, установленные значения, аналоговые значения, значения состояния, телесигнализацию, телеизмерения, телеуправление, отчеты, синхронизацию времени устройства, импорт/экспорт установленных значений, функции отладки переменных",
      configTool:
        "Включает предварительный просмотр инструментов конфигурации, добавление, редактирование, пользовательские символы, функции связывания информации об устройстве",
      themeTool: "Включает настройку тем, IT-инструменты, функции импорта/экспорта конфигурации устройства"
    }
  },
  footer: {
    copyright: "{version}"
  },
  header: {
    minimize: "Свернуть",
    maximize: "Развернуть",
    restore: "Восстановить",
    close: "Закрыть",
    company: {
      name: "Siyuan Electric",
      englishName: "Sieyuan"
    },
    collapse: {
      expand: "Развернуть устройство",
      fold: "Свернуть устройство",
      expandTool: "Развернуть список инструментов",
      foldTool: "Свернуть список инструментов"
    },
    breadcrumb: {
      home: "Главная"
    },
    assemblySize: {
      title: "Настройка размера",
      default: "По умолчанию",
      large: "Большой",
      small: "Маленький"
    },
    avatar: {
      profile: "Личный кабинет",
      switchApp: "Переключить приложение",
      logout: "Выйти",
      logoutConfirm: {
        title: "Дружеское напоминание",
        message: "Вы уверены, что хотите выйти?",
        confirm: "Подтвердить",
        cancel: "Отмена"
      },
      logoutSuccess: "Выход выполнен успешно!"
    },
    changeModule: {
      title: "Переключить модуль"
    },
    enginConfig: {
      configType: "Тип конфигурации",
      openDirectory: "Открыть каталог файлов",
      cancel: "Отмена",
      confirm: "Подтвердить",
      all: "Все",
      deviceList: "Список устройств",
      configureList: "Список конфигураций",
      exportSuccess: "Экспорт конфигурации успешен",
      importSuccess: "Импорт конфигурации успешен",
      disconnectDeviceFirst: "Сначала отключите подключенное устройство",
      overrideConfirm: "Список конфигураций уже существует, перезаписать?",
      warmTips: "Дружеское напоминание",
      importConfigFile: "Импорт файла конфигурации"
    },
    userInfo: {
      title: "Личная информация",
      cancel: "Отмена",
      confirm: "Подтвердить"
    },
    password: {
      title: "Изменить пароль",
      cancel: "Отмена",
      confirm: "Подтвердить"
    },
    globalSetting: {
      title: "Settings",
      tooltip: "Settings"
    },
    moreInfo: {
      title: "Больше",
      tooltip: "Больше",
      items: {
        importConfig: "Импорт Config",
        printScreen: "Скриншот",
        search: "Поиск в меню",
        exportConfig: "Экспорт Config",
        about: "О программе",
        help: "Помощь"
      },
      importConfig: {
        title: "Импорт Config",
        placeholder: "Выберите файл конфигурации"
      },
      exportConfig: {
        title: "Экспорт Config",
        placeholder: "Выберите каталог для экспорта"
      }
    },
    searchMenu: {
      placeholder: "Поиск в меню: поддерживает названия меню, пути",
      empty: "Нет меню"
    },
    theme: {
      title: "Тема",
      tooltip: "Тема"
    }
  },
  main: {
    maximize: {
      exit: "Выйти из полноэкранного режима"
    }
  },
  theme: {
    title: "Layout",
    quickTheme: {
      title: "Theme"
    },
    layoutSettings: {
      title: "Layout"
    },
    layout: {
      title: "Стиль макета",
      columns: "Колонки",
      classic: "Классический",
      transverse: "Горизонтальный",
      vertical: "Вертикальный"
    },
    global: {
      title: "Глобальная тема",
      primary: "Цвет темы",
      dark: "Темный режим",
      grey: "Серый режим",
      weak: "Режим для дальтоников",
      special: "Специальный режим"
    },
    mode: {
      light: "Светлый",
      dark: "Темный"
    },
    interface: {
      title: "Interface",
      watermark: "Водяной знак",
      breadcrumb: "Хлебные крошки",
      breadcrumbIcon: "Иконка хлебных крошек",
      tabs: "Панель вкладок",
      tabsIcon: "Иконка панели вкладок",
      footer: "Нижний колонтитул",
      drawerForm: "Форма выдвижного ящика"
    },
    presetThemes: {
      title: "Предустановленные темы",
      default: {
        name: "Тема по умолчанию",
        description: "Классическая синяя тема"
      },
      dark: {
        name: "Темная тема",
        description: "Темный режим для защиты глаз"
      },
      techBlue: {
        name: "Технологический синий",
        description: "Современный технологический синий цвет"
      },
      deepBlue: {
        name: "Глубокий синий",
        description: "Глубокий стабильный синий цвет"
      },
      nature: {
        name: "Природная тема",
        description: "Свежая зеленая серия"
      },
      forestGreen: {
        name: "Лесной зеленый",
        description: "Глубокий лесной зеленый цвет"
      },
      warm: {
        name: "Теплая тема",
        description: "Теплая оранжевая серия"
      },
      sunsetOrange: {
        name: "Закатный оранжевый",
        description: "Теплый закатный оранжевый цвет"
      },
      elegant: {
        name: "Элегантная тема",
        description: "Благородная фиолетовая серия"
      },
      lavender: {
        name: "Лаванда",
        description: "Мягкий лавандовый фиолетовый"
      },
      sakura: {
        name: "Сакура розовый",
        description: "Романтический розовый цвет сакуры"
      },
      rose: {
        name: "Розовый красный",
        description: "Страстный розово-красный цвет"
      },
      lime: {
        name: "Лаймовый зеленый",
        description: "Энергичный лаймово-зеленый цвет"
      },
      skyBlue: {
        name: "Небесно-голубой",
        description: "Чистый небесно-голубой цвет"
      },
      eyeCare: {
        name: "Режим защиты глаз",
        description: "Серая тема для защиты глаз"
      }
    },
    colors: {
      techBlue: {
        name: "Технологический синий",
        description: "Современное технологическое ощущение"
      },
      natureGreen: {
        name: "Природный зеленый",
        description: "Свежий и естественный"
      },
      vibrantOrange: {
        name: "Яркий оранжевый",
        description: "Теплая энергия"
      },
      elegantPurple: {
        name: "Элегантный фиолетовый",
        description: "Благородная элегантность"
      },
      romanticPink: {
        name: "Романтический розовый",
        description: "Нежная романтика"
      },
      freshCyan: {
        name: "Свежий голубой",
        description: "Свежий и изящный"
      },
      brightYellow: {
        name: "Яркий желтый",
        description: "Яркий и живой"
      },
      warmOrange: {
        name: "Теплый оранжевый",
        description: "Теплый и комфортный"
      },
      limeGreen: {
        name: "Лаймовый зеленый",
        description: "Свежий лайм"
      },
      deepBlue: {
        name: "Глубокий синий",
        description: "Глубокий и стабильный"
      },
      golden: {
        name: "Золотой",
        description: "Классический золотой"
      },
      chinaRed: {
        name: "Китайский красный",
        description: "Традиционный красный"
      }
    }
  },
  tabs: {
    moreButton: {
      refresh: "Обновить",
      closeCurrent: "Закрыть текущую",
      closeLeft: "Закрыть левые",
      closeRight: "Закрыть правые",
      closeOthers: "Закрыть другие",
      closeAll: "Закрыть все"
    }
  }
};
