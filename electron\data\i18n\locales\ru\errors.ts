/**
 * Сообщения об ошибках - Русский
 */
export default {
  success: "Успешно",
  deviceNotConnected: "Устройство не подключено",
  invalidParam: "Ош<PERSON>бка параметра",
  operateFailed: "Операция не удалась",
  noData: "Нет данных",
  internalError: "Внутренняя ошибка",
  connectionExists: "Соединение уже существует",
  fileContentEmpty: "Содержимое файла пустое",
  deviceNotConnectedOrDisconnected: "Устройство не подключено или отключено.",
  getServiceErrorInfo: "Соответствующая информация об ошибке не получена",
  saveReportFileError: "Ошибка сохранения файла отчета rpt",
  getConfigureListError: "Исключение при получении списка конфигураций",
  loadConfigureError: "Исключение при загрузке конфигурации",
  cancelUploadError: "Исключение при отмене загрузки файла записи волн",
  openWaveFileError: "Исключение при открытии файла записи волн",
  getFileDataSuccess: "Получение содержимого данных файла успешно!",
  getHistoryReportError: "Ошибка получения исторического отчета",
  getSuccessful: "Получение успешно",
  errorHandledInCatch: "Ошибка обработана в catch",
  waveFileNotFound: "Файл записи волн не найден",
  waveFileSizeZero: "Размер файла записи волн равен 0",
  uploadException: "Исключение загрузки",
  uploadFinished: "Загрузка завершена",
  saveReportXlsxError: "Ошибка сохранения файла отчета xlsx",
  invalidXmlStructure: "Недействительная структура XML: отсутствует configVersion",
  failedToGetTreeMenu: "Не удалось получить древовидное меню",
  missingHeaders: "Отсутствуют заголовки",
  excelParseFailed: "Анализ Excel не удался",
  paramModifyError: "Ошибка изменения элемента установленного значения, ошибочный элемент:",
  paramConfirmError: "Ошибка подтверждения установленного значения, причина ошибки:",
  errorReason: ", причина ошибки:",
  invalidValue: "Недействительное значение",
  errorItem: "Ошибочный элемент:",
  description: ", описание",
  excelFileParseError: "Анализ файла Excel не удался:",
  csvFileParseError: "Анализ файла CSV не удался:",
  xmlFileParseError: "Анализ файла XML не удался:",
  connectionFailed: "Подключение не удалось",
  connectionTimeout: "Тайм-аут подключения",
  dataLengthMismatch: "Длина данных vkeys, возвращенных устройством, не соответствует длине в запросе",
  invalidFilePath: "Недействительный путь к файлу. Пожалуйста, предоставьте действительный путь к файлу .xlsx.",
  failedToCreateDirectory: "Не удалось создать каталог",
  failedToExportData: "Не удалось экспортировать данные",
  worksheetNotFound: "Файл Excel не содержит указанный рабочий лист.",
  noHeaders: "Файл Excel не содержит заголовков.",
  parseExcelUnknownError: "Произошла неизвестная ошибка при анализе файла Excel",
  errorParsingXml: "Ошибка при анализе XML",
  failedToUpdateXmlFile: "Не удалось обновить файл XML",
  // Сообщения об ошибках контроллера
  addDeviceConfigFailed: "Не удалось добавить конфигурацию устройства",
  updateDeviceConfigFailed: "Не удалось изменить конфигурацию устройства",
  deleteDeviceConfigFailed: "Не удалось удалить конфигурацию устройства",
  deleteSuccess: "Удаление успешно",
  deleteFailed: "Удаление не удалось",
  getDeviceList: "Получить устройство",
  getDeviceConfigListFailed: "Не удалось получить список конфигураций устройств",
  exportFilePathEmpty: "Путь экспорта файла пуст",
  exportFileExtensionError: "Неправильное расширение файла экспорта",
  exportFolderCreateFailed: "Не удалось создать папку экспорта",
  variableNameEmpty: "Имя переменной не может быть пустым",
  exportPathEmpty: "Путь экспорта не может быть пустым",
  importPathEmpty: "Путь импорта не может быть пустым",
  deviceAlreadyConnected: "Устройство уже подключено",
  unknownServiceError: "Неизвестная ошибка сервиса",
  errorInfo: "Информация об ошибке",
  subscribeEventError: "Ошибка подписки на событие",
  subscribeEventFailed: "Подписка на событие не удалась",
  subscribeEventServiceError: "Ошибка сервиса подписки на событие",
  unsubscribeEventError: "Ошибка отписки от события",

  // Новая информация об ошибках
  noDataRetrieved: "Данные не получены",
  errorDetail: "Детали ошибки",
  vkeysDataLengthMismatch: "Длина данных vkeys, возвращенных устройством, не соответствует длине в запросе",
  qkeysDataLengthMismatch: "Длина данных qkeys, возвращенных устройством, не соответствует длине в запросе"
};
