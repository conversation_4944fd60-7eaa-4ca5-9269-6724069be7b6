export default {
  // 通用操作
  common: {
    add: "新增",
    index: "序号",
    delete: "删除",
    clear: "清空",
    import: "导入",
    export: "导出",
    execute: "执行",
    moveUp: "上移",
    moveDown: "下移",
    loading: "加载中...",
    success: "成功",
    failed: "失败",
    confirm: "确定",
    cancel: "取消",
    yes: "是",
    no: "否",
    operation: "操作",
    tips: "提示",
    title: "提示"
  },

  // 搜索相关
  search: {
    placeholder: "搜索功能"
  },

  // 功能列表相关
  functionList: {
    unnamedDevice: "未命名装置",
    batchDownload: {
      name: "批量下载",
      desc: "多装置文件批量下载与定值批量导入"
    },
    xmlFormatter: {
      name: "XML格式化",
      desc: "快速实现XML数据层级化整理"
    },
    jsonFormatter: {
      name: "JSON格式化",
      desc: "JSON 数据智能格式化，支持语法校验"
    },
    radixConverter: {
      name: "进制转换",
      desc: "支持二进制、十进制、十六进制等多进制数值互转"
    },
    temperatureConverter: {
      name: "温度转换",
      desc: "摄氏度、华氏度、开尔文等多种温度单位智能换算"
    },
    encryption: {
      name: "文本加解密",
      desc: "基于 AES、RSA、Base64 等算法的文本快速加密与解密"
    },
    packageProgram: {
      name: "程序打包",
      desc: "将装置运行程序文件打包导出，支持自定义保存目录和一键定位文件夹"
    }
  },

  // 矩阵内容相关
  matrixContent: {
    loading: "加载中...",
    tabs: {
      deviceList: "装置列表",
      downloadConfig: "下载配置",
      paramConfig: "定值配置"
    }
  },

  // 任务步骤
  taskSteps: {
    connect: "连接",
    download: "下载",
    import: "导入",
    disconnect: "断开",
    complete: "完成"
  },

  // 任务消息
  messages: {
    connectDevice: "连接装置",
    executeFileDownload: "执行文件下载",
    downloadingFile: "下载文件中",
    downloadFileFailed: "下载文件失败",
    fileDownloadCompleted: "文件下载执行完成",
    executeParamImport: "执行定值导入",
    paramValidationFailed: "定值格式校验失败",
    paramImportFailed: "定值导入失败",
    paramImportCompleted: "定值导入执行完成",
    taskCompleted: "任务执行完成",
    deviceConnectionFailed: "装置连接失败",
    deviceRebootSuccess: "重启装置成功"
  },

  // 装置列表
  deviceList: {
    title: "装置列表",
    deviceListExcel: "装置列表.xlsx",
    exportDeviceList: "导出装置列表",
    importDeviceList: "导入装置列表",
    exportSuccess: "导出装置列表成功",
    exportFail: "导出装置列表失败",
    importSuccess: "导入装置列表成功",
    importFail: "导入装置列表失败",
    exportSuccessMsg: "导出装置列表成功",
    exportFailMsg: "导出装置列表失败",
    importSuccessMsg: "导入装置列表成功",
    importFailMsg: "导入装置列表失败: {msg}",
    deviceName: "装置名称",
    deviceAddress: "装置地址",
    devicePort: "装置端口",
    isEncrypted: "是否加密",
    encrypted: "已加密",
    notEncrypted: "未加密",
    status: "状态",
    operation: "操作",
    reboot: "重启",
    noReboot: "不重启",
    addDevice: "新增装置",
    deleteDevice: "删除装置",
    clearDevices: "清空装置",
    deviceExists: "该装置已存在",
    deviceDeleted: "装置{ip}已删除",
    downloadFile: "是否下载文件？",
    importParam: "是否导入参数？",
    connectTimeout: "连接超时时间",
    paramTimeout: "参数修改超时时间",
    readTimeout: "全局请求超时时间",
    progress: "进度"
  },

  // 下载文件配置
  downList: {
    title: "下载文件配置",
    deviceDirectory: "装置目录",
    fileName: "文件名称",
    fileSize: "文件大小",
    filePath: "文件路径",
    lastModified: "最后修改时间",
    addFile: "添加待下载的文件",
    addFolder: "添加待下载的文件夹",
    fileExists: "文件{path}已存在，添加失败！",
    fileDeleted: "文件{path}已删除",
    filesDeleted: "文件已删除",
    defaultExportFileName: "下载文件列表.xlsx",
    exportTitle: "导出下载文件列表",
    importTitle: "导入下载文件列表",
    exportSuccess: "文件列表导出成功",
    exportFailed: "文件列表导出失败",
    importSuccess: "文件列表导入成功",
    importFailed: "文件列表导入失败",
    fileExistsMsg: "文件{path}已存在，添加失败！"
  },

  // 参数定值配置
  paramList: {
    title: "参数定值配置",
    paramGroup: "定值组",
    groupName: "组名",
    paramName: "名称",
    paramDesc: "描述",
    paramValue: "值",
    minValue: "最小值",
    maxValue: "最大值",
    step: "步长",
    unit: "单位",
    searchParamName: "参数名",
    searchParamDesc: "参数描述",
    importSuccess: "参数定值导入成功",
    importFailed: "参数定值导入失败",
    exportSuccess: "参数定值导出成功",
    exportFailed: "参数定值导出失败",
    clearSuccess: "定值列表清空成功"
  },

  // 进度对话框
  progressDialog: {
    title: "处理中",
    pleaseWait: "请稍候..."
  },

  packageProgram: {
    saveDir: "保存目录",
    selectSaveDir: "选择保存目录",
    packageBtn: "打包",
    locateDir: "定位文件夹",
    delete: "删除",
    sequence: "序号",
    fileName: "文件名称",
    fileSize: "文件大小",
    filePath: "文件路径",
    lastModified: "最后修改时间",
    operation: "操作",
    saveDirEmpty: "请先选择保存目录！",
    packageSuccess: "程序打包完成！",
    tip: "提示",
    confirmButton: "确定",
    // 新增导入导出相关
    defaultExportFileName: "程序打包文件清单.xlsx",
    exportTitle: "导出程序打包文件清单",
    importTitle: "导入程序打包文件清单",
    exportSuccess: "文件清单导出成功",
    exportFailed: "文件清单导出失败",
    importSuccess: "文件清单导入成功",
    importFailed: "文件清单导入失败",
    fileExists: "文件{path}已存在，添加失败！",
    selectDirSuccess: "选择目录成功：{dir}",
    locateDirSuccess: "定位目录成功：{dir}",
    addFileStart: "打开文件选择器...",
    addFileSuccess: "成功添加 {count} 个文件/文件夹",
    addFileNone: "未添加任何新文件/文件夹",
    deleteSuccess: "成功删除 {count} 个文件/文件夹",
    clearSuccess: "已清空所有文件/文件夹",
    moveUpSuccess: "上移：{name}",
    moveDownSuccess: "下移：{name}",
    noFileSelected: "请先勾选需要打包的文件！",
    noDeviceSelected: "请先选择需要打包的装置！",
    packageFailed: "打包失败：{msg}",
    zipPath: "打包路径: {zipPath}",
    openFileButton: "打开文件"
  }
};
