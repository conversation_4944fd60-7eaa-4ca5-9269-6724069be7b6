<!doctype html><html lang="en"><head><meta charset="UTF-8"/><link rel="icon" type="image/svg+xml" href="./vue.svg"/><meta name="viewport" content="width=device-width,initial-scale=1"/><title>VisualDebug</title><script type="module" crossorigin src="./assets/entry/index-ByIOTQIq.js"></script><link rel="modulepreload" crossorigin href="./assets/vendor/vendor-P-ltm-Yc.js"><link rel="modulepreload" crossorigin href="./assets/vendor/highlight-vendor-DzhJ4giz.js"><link rel="modulepreload" crossorigin href="./assets/vendor/utils-vendor-C_ezCHls.js"><link rel="modulepreload" crossorigin href="./assets/chunks/element-icons-DbNYNmpr.js"><link rel="modulepreload" crossorigin href="./assets/chunks/element-core-DyPKvxS2.js"><link rel="modulepreload" crossorigin href="./assets/vendor/i18n-vendor-BPpKJ4WV.js"><link rel="modulepreload" crossorigin href="./assets/chunks/element-components-CkwEs5_B.js"><link rel="modulepreload" crossorigin href="./assets/vendor/charts-vendor-DS7xuoj-.js"><link rel="modulepreload" crossorigin href="./assets/chunks/components-BkHQV7Qm.js"><link rel="modulepreload" crossorigin href="./assets/chunks/views-biz-DnwQrgNu.js"><link rel="modulepreload" crossorigin href="./assets/vendor/icons-vendor-DBJjj0eo.js"><link rel="modulepreload" crossorigin href="./assets/vendor/vue-vendor-EiOvILk3.js"><link rel="modulepreload" crossorigin href="./assets/chunks/utils-DeqEhvvR.js"><link rel="stylesheet" crossorigin href="./assets/styles/highlight-vendor-ZgkIHsf0.css"><link rel="stylesheet" crossorigin href="./assets/styles/vendor-CaccPJo9.css"><link rel="stylesheet" crossorigin href="./assets/styles/element-core-BlK98vAp.css"><link rel="stylesheet" crossorigin href="./assets/styles/vue-vendor-DNbd9zSU.css"><link rel="stylesheet" crossorigin href="./assets/styles/index-CD6qC75v.css"><link rel="manifest" href="./manifest.webmanifest"><script id="vite-plugin-pwa:register-sw" src="./registerSW.js"></script></head><body><div id="app"><style>#app,body,html{width:100%;height:100%;padding:0;margin:0}.loading-box{display:flex;flex-direction:column;align-items:center;justify-content:center;width:100%;height:100%}.loading-box .loading-wrap{display:flex;align-items:center;justify-content:center;padding:98px}.dot{position:relative;box-sizing:border-box;display:inline-block;width:32px;height:32px;font-size:32px;transform:rotate(45deg);animation:ant-rotate 1.2s infinite linear}.dot i{position:absolute;display:block;width:14px;height:14px;background-color:#409eff;border-radius:100%;opacity:.3;transform:scale(.75);transform-origin:50% 50%;animation:ant-spin-move 1s infinite linear alternate}.dot i:first-child{top:0;left:0}.dot i:nth-child(2){top:0;right:0;animation-delay:.4s}.dot i:nth-child(3){right:0;bottom:0;animation-delay:.8s}.dot i:nth-child(4){bottom:0;left:0;animation-delay:1.2s}@keyframes ant-rotate{to{transform:rotate(405deg)}}@keyframes ant-spin-move{to{opacity:1}}</style><div class="loading-box"><div class="loading-wrap"><span class="dot dot-spin"><i></i><i></i><i></i><i></i></span></div></div></div><script>// 设置主题样式
      const globalState = JSON.parse(window.localStorage.getItem("simple-global"));
      if (globalState) {
        const dot = document.querySelectorAll(".dot i");
        const html = document.querySelector("html");
        dot.forEach(item => (item.style.background = globalState.primary));
        if (globalState.isDark) html.style.background = "#141414";
      }</script></body></html>