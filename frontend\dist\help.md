# 使用说明

## 1. 功能概述

**VisualDebug** 是一款专为 Sieyuan 可视化平台装置（研发和工程调试与管理）打造的调试工具，集成了丰富的调试、组态、管理与辅助功能，助力工程师高效完成各类调试任务。

![工具整体界面](./help/工具整体界面.png)

主要功能包括：

- 装置调试与管理
- 组态工具
- IT工具
- 工具设置、主题配置、语言切换、更多功能

通过以上功能，VisualDebug 可满足 Sieyuan 可视化平台装置的全流程研发与工程调试、组态设计与装置日常管理需求。

### 1.1 装置调试与管理

- 装置连接信息配置
- 装置基本信息查看
- 设定量、模拟量、状态量、遥信、遥测、遥控、遥调、出口传动、报告
- 装置对时、变量调试、定值导入导出
- 文件上传、文件下载

![装置调试与管理](./help/装置调试与管理.png)

### 1.2 组态工具

- 组态图形绘制（预览、新增、编辑、自定义图符）
- 装置信息与图形元素关联

![组态工具](./help/组态工具.png)

### 1.3 IT工具

- 批量下载、批量导入定值
- 程序打包
- XML/JSON 格式化
- 进制转换、温度转换
- 文本加解密

![IT小工具](./help/IT小工具.png)

### 1.4 工程配置与系统设置

- 工程配置导入、导出
- 系统配置、参数配置
- 主题定制、多语言切换

![工程配置与系统设置](./help/工程配置导入和导出.png)

## 2. 主要模块

- **菜单栏**：默认左侧的菜单栏集中展示所有功能入口，包括调试、组态、工具、设置、主题、语言、更多等。
- **设备调试**：支持多设备连接，实时连接查看设备状态、参数、变量，装置对时、文件上传、文件下载等。
- **组态功能**：组态图形绘制（预览、新增、编辑、自定义图符），实时展示展示装置的组态界面信息。
- **控制台**：实时显示系统日志、调试信息、错误提示，便于问题定位。
- **多语言支持**：可在设置中切换简体中文、英文等多种语言。
- **主题切换**：可在浅色、深色等多种主题间自由切换，提升视觉体验。
- **配置管理**：支持工程配置文件的导入、导出、备份与恢复，方便项目迁移与协作。

![工具整体模块](./help/工具整体模块.png)

## 3. 运行环境要求

为确保 VisualDebug 软件能够稳定高效运行，建议使用以下软硬件环境：

- **操作系统**：Windows 10 及以上版本（推荐 64 位），支持部分 Linux 发行版（如 Debian12+）。
- **处理器**：双核及以上主流 x86 架构 CPU。
- **内存**：建议 4GB 及以上，推荐 8GB 以上以获得更佳体验。
- **存储空间**：解压运行需预留至少 500MB 可用磁盘空间。
- **显示分辨率**：建议 1366×768 及以上，推荐 1920×1080 全高清分辨率。
- **网络环境**：调试装置时需保证与目标装置处于同一局域网。

- **注意：**
  - 建议以管理员权限运行软件，以避免因权限不足导致的配置问题。
  - 该工具为绿色软件，无需复杂安装，解压后即可直接运行，无需对系统进行注册表或环境变量的修改。所有用户数据、配置文件均保存在软件目录下，便于备份和迁移。

## 4. 软件激活

VisualDebug 软件采用本地激活授权机制，首次使用时需完成激活操作。激活流程如下：

![授权激活](./help/授权激活.png)

1. **获取机器码**

   - 启动软件后，若未激活，会自动弹出激活窗口，显示本机唯一机器码。
   - 也可在菜单栏"更多">"关于"页面查看机器码。

2. **申请激活码**

   - 将机器码发送给软件管理员或技术支持人员，申请获取激活码。

3. **输入激活码**

   - 在激活窗口输入收到的激活码，点击"激活"按钮。
   - 激活成功后，软件将进入正常使用状态

4. **注意事项**
   - 激活码与机器码一一对应，仅限在申请的装置上使用。默认只需要首次进行激活。
   - 若更换电脑或重装系统，需重新申请激活码。
   - 激活信息保存在操作系统凭据管理器-Windows凭据-普通凭据下，请勿随意删除。

![凭据管理器](./help/凭据管理器.png)

> 如遇激活失败或授权相关问题，请联系技术支持协助处理。

## 5. 调试功能

### 5.1 装置配置说明

装置配置功能用于管理和维护需要调试的装置信息，便于后续的连接、参数管理。主要操作如下：

1. **进入装置配置界面**

   - 在菜单栏点击"调试">"装置列表"进入装置管理页面。

2. **添加新装置**

   - 点击"新增装置"按钮，填写装置名称、IP地址、端口（默认58000）、是否加密等信息。
   - 点击"展开高级选项"，可以配置连接超时时间（默认5000毫秒），全局请求超时时间（默认30000毫秒），定值修改超时时间（默认30000毫秒）

3. **编辑/删除装置**

   - 在装置列表中，选择对应装置右键弹出菜单上的"编辑"按钮可修改装置信息。
   - 点击"删除"按钮可移除不需要的装置，删除操作需谨慎。

4. **装置状态与连接**

   - 装置列表会实时显示装置的在线/离线状态。
   - 支持一键连接/断开装置，连接状态会有明显标识。

5. **装置搜索与过滤**

   装置列表支持**搜索过滤**功能，便于快速定位目标装置：

   - 在装置管理页面右上角输入框中输入装置名称、IP地址或端口号的任意部分，列表会自动筛选匹配的装置。
   - 支持模糊搜索，无需输入完整信息即可定位。
   - 清空搜索框后，恢复显示全部装置。

6. **装置展开/收缩功能**

   - 在装置配置列表页面中间位置有展开/收缩按钮，支持**展开装置**和**收缩装置**，方便用户快速浏览或收起装置的信息。

![装置配置](./help/装置配置.png)

> **提示：**
>
> - 装置配置数据会自动保存到本地配置文件，重启软件后自动加载。
> - 可定期导出装置配置，防止数据丢失。

### 5.2 分组信息管理

连接装置成功后，软件会自动显示该装置的**分组信息**，方便用户对装置下的各类功能、数据进行管理。分组信息界面如图所示：

![分组信息界面示例](./help/分组信息界面示例.png)

1. **分组信息展示**

   - 左侧区域会自动列出当前装置下的所有分组（如设定量、状态量、遥测、遥信、遥控、遥调、出口传动、报告等），每个分组下包含对应的功能项或数据点。
   - 分组结构支持多级嵌套，便于层级化管理。

2. **分组搜索**

   - 在分组列表上方提供**搜索框**，可输入分组名称或关键字，快速定位目标分组或功能项。
   - 支持模糊搜索，无需输入完整名称即可筛选。

3. **创建自定义菜单**
   - 用户可根据实际需求，点击"新建菜单"按钮，创建自定义菜单，将常用的分组或功能项添加到自定义菜单中，提升操作效率。
   - 新增自定义菜单的时可以配置菜单的名称和描述，创建成功后菜单的描述显示在分组菜单上。

![创建自定义菜单界面示例](./help/创建自定义菜单界面示例.png)

4. **创建自定义报告**
   - 支持在自定义菜单下新建自定义报告，将相关功能项归类管理。
   - 新建自定义报告时可自定义报告名称，描述，关键字，继承报告，按照关键字过滤继承报告的数据

![创建自定义报告界面示例](./help/创建自定义报告界面示例.png)

5. **自定义菜单操作**
   - 右键自定义菜单，可进行"编辑菜单"、"删除菜单"等操作。

![自定义菜单操作界面示例](./help/自定义菜单操作界面示例.png)

> **提示：**
>
> - 自定义菜单和分组信息会随装置连接自动刷新，确保展示最新的分组结构，默认创建的分组信息只针对本装置生效。
> - 自定义菜单和分组仅在本地保存，重启软件后依然有效。

### 5.3 装置分组信息总览

为方便用户快速了解当前装置的分组及数据点分布情况，软件提供了**装置分组信息总览**功能，界面如下所示：

![装置分组信息总览界面](./help/装置分组信息总览界面.png)

1. **总览卡片区**

   - 左侧以大卡片形式展示当前装置的"装置定值总数"，便于一目了然地把握装置的定值规模。
   - 右侧以多色卡片分别统计各类分组的数量，如遥测量、遥信量、遥控量、出口传动量等，帮助用户快速了解各分组类型的分布。

2. **分组数据占比图**
   - 右侧以环形图（饼图）直观展示各分组数据点的占比情况，不同分组以不同颜色区分，图例清晰标注分组名称及数量。
   - 鼠标悬停在图表区域，可显示对应分组的详细数据点数量及占比百分比。

> **提示：**
>
> - 分组信息总览仅统计当前已连接装置的分组及数据点，断开连接后将不再显示。
> - 若分组数据异常或统计不符，请检查装置配置或联系技术支持。

### 5.4 装置基本信息

在连接装置后，软件会自动读取并展示当前装置的**基本信息**，便于用户快速了解设备的核心参数。装置基本信息界面如下图所示：

![装置基本信息界面示例](./help/装置基本信息界面示例.png)

1. **信息展示区域**

   - 以表格形式清晰展示装置的主要参数，包括但不限于：
     - Serial（序列号）：设备唯一标识码。
     - dev_name（装置名称）：如 BY_CIN_31K。
     - ver_time（版本时间）：固件或配置的生成时间。
     - dev_model（装置型号）：如 APEC-311MC。
     - ver_code（版本号）：如 07_02。
     - 校验码：如 C62F7FC0。
     - sec_billno（序列号）：如 000020。
     - msg_serial（消息序列）：如 D0000000。

2. **操作说明**

   - 点击"导出"按钮可将当前装置的基本信息导出为本地文件，便于归档或技术支持时使用。
   - 若装置信息显示异常，请检查装置连接状态或联系技术支持。

3. **数据来源**
   - 装置基本信息界面对应debug_info.xml中的DEV_INFO菜单。
   - Serial 来自 cat /proc/cpuinfo | grep Serial 命令的输出结果，除Serial 外的其他信息来自装置配置文件factoryinfo.xml。

> **提示：**
>
> - 装置基本信息仅在设备成功连接后显示，断开连接后将自动隐藏。
> - 部分参数可能因装置型号不同而略有差异，具体以实际界面为准。

### 5.5 遥信量管理与操作

遥信量是指设备状态的开关量信息（如分合闸、告警、保护等），在**VisualDebug**中可进行统一管理和操作。遥信量界面如下图所示：

![遥信量管理界面示例](./help/遥信量管理界面示例.png)

1. **界面说明**

   - 遥信量列表以表格形式展示，包含序号、名称、描述、当前值、品质等字段，便于用户快速浏览和定位。
   - 支持通过顶部筛选栏按名称、描述、值等关键字进行模糊查询，提升查找效率。
   - 可通过"刷新"、"导出"等按钮进行遥信量的刷新和数据导出操作。

2. **字段说明**

   - **序号**：遥信量的唯一编号，便于排序和查找。
   - **名称**：遥信量的标识名称，通常与设备实际点位一一对应。
   - **描述**：对遥信量的功能或用途进行简要说明。
   - **当前值**：实时显示遥信量的当前状态（如0/1，分/合等）。
   - **品质**：表示当前遥信数据的有效性或质量状态。

3. **常用操作**

   - 支持遥信量界面勾选配置自动刷新，确保数据实时更新。
   - 支持遥信量数据导出为Excel等格式，便于后续分析和归档。
   - 可通过分页功能快速浏览大量遥信数据。

4. **数据来源**
   - 遥信量对应debug_info.xml中的 STATE_TABLE- YX_TABLE。
   - 遥信数据来自装置配置文件device_config.xml中 FUNCS - STATE_TABLE - YX_TABLE。
   - DCA_ST_URGENT_TABLE， DCA_ST_IMPORTANT_TABLE，DCA_ST_NORMAL_TABLE 三组为外采遥信组

> **提示：**
>
> - 遥信量数据实时刷新，确保展示设备最新状态。
> - 若遥信量显示异常或无数据，请检查设备连接及配置。
> - 导出功能支持将结果导出为Excel等格式，便于后续分析和归档。

### 5.6 遥测量管理与操作

遥测量是指设备采集的模拟量数据（如电压、电流、温度等），**VisualDebug**支持对遥测量的统一管理和操作。遥测量管理界面如下图所示：

![遥测量管理界面示例](./help/遥测量管理界面示例.png)

1. **界面说明**

   - 遥测量列表以表格形式展示，包含序号、名称、描述、值、品质等字段，便于用户直观查看和分析各项遥测数据。
   - 支持通过顶部筛选栏按名称、描述、值等关键字进行模糊查询，提升查找效率。
   - 可通过"刷新"、"导出"等按钮进行遥测量的刷新和数据导出操作。

2. **字段说明**

   - **序号**：遥测量的唯一编号，便于排序和查找。
   - **名称**：遥测量的标识名称，通常与设备实际点位一一对应。
   - **描述**：对遥测量的功能或用途进行简要说明。
   - **值**：实时显示遥测量的当前数值（如电压、电流等）。
   - **品质**：表示当前遥测数据的有效性或质量状态。

3. **常用操作**

   - 支持遥测量界面勾选配置自动刷新，确保数据实时更新。
   - 支持遥测量数据导出为Excel等格式，便于后续分析和归档。
   - 可通过分页功能快速浏览大量遥测数据。

4. **数据来源**
   - 遥测量对应debug_info.xml中的 STATE_TABLE - YC_TABLE。
   - 遥测数据来自装置配置文件device_config.xml中 FUNCS - STATE_TABLE - YC_TABLE。
   - DCA_MX_TABLE 为外采遥测组。

> **提示：**
>
> - 遥测量数据实时刷新，确保展示设备最新模拟量状态。
> - 若遥测量显示异常或无数据，请检查设备连接及相关配置。
> - 导出功能支持将结果导出为Excel等格式，便于后续分析和归档。

### 5.7 遥控量管理与操作

遥控量是指对设备进行远程控制的开关量操作（如分闸、合闸、投退等），**VisualDebug**支持对遥控量的统一管理和操作。遥控量管理界面如下图所示：

![遥控量管理界面示例](./help/遥控量管理界面示例.png)

1. **界面说明**

   - 遥控量列表以表格形式展示，包含序号、短地址、描述、控分/控合、控制类型、操作按钮等字段，便于用户直观查看和操作各项遥控点。
   - 支持通过顶部筛选栏按短地址、描述等关键字进行模糊查询，提升查找效率。
   - 可通过"刷新"、"导出"等按钮进行遥控量的刷新和数据导出操作。
   - 每一行均可直接进行遥控操作（选择如控分、控合等，选择类型如不检、检同期、检无压等），然后可进行直控或者选控。

2. **字段说明**

   - **序号**：遥控量的唯一编号，便于排序和查找。
   - **短地址**：遥控量的标识名称，通常与设备实际点位一一对应。
   - **描述**：遥控量对应功能或用途进行简要说明。
   - **控分/控合**：显示可执行的遥控操作类型（如控分、控合等）。
   - **类型**：选择控制的类型（如不检、检同期、检无压等）。
   - **操作**：包含遥控执行按钮（如"直控"、"选控"等），支持下发遥控命令。

3. **数据来源**
   - 遥控量对应debug_info.xml中的 CTRL_TABLE - YK_TABLE。
   - 遥控数据来自装置配置文件device_config.xml中 FUNCS - CTRL_TABLE - YK_TABLE。

> **提示：**
>
> - 遥控操作涉及设备实际动作，请务必确认操作对象和命令，避免误操作。
> - 若遥控量显示异常或操作无响应，请检查设备连接、使能开关权限配置及相关安全设置。
> - 导出功能支持将结果导出为Excel等格式，便于后续分析和归档。

### 5.8 遥调量管理与操作

遥调量是指对设备进行远程参数调整的功能（如速率值、阈值等的远程修改），**VisualDebug**支持对遥调量的统一管理和操作。遥调量管理界面如下图所示：

![遥调量管理界面示例](./help/遥调量管理界面示例.png)

1. **界面说明**

   - 遥调量列表以表格形式展示，包含序号、短地址、描述、值、操作按钮等字段，便于用户直观查看和调整各项遥调参数。
   - 支持通过顶部筛选栏按短地址、描述等关键字进行模糊查询，提升查找效率。
   - 可通过"刷新"、"导出"等按钮进行遥调量的刷新和数据导出操作。
   - 每一行均可直接进行遥调操作（如输入新设定值，点击"选控"或者"直控"按钮进行下发）。

2. **字段说明**

   - **序号**：遥调量的唯一编号，便于排序和查找。
   - **短地址**：遥调量的标识名称，通常与设备实际点位一一对应。
   - **描述**：对遥调量的功能或用途进行简要说明。
   - **值**：用户可输入新的设定值，准备下发到设备。
   - **操作**：包含遥调执行按钮（如"选控"或者"直控"），支持下发遥调命令。

3. **数据来源**
   - 遥调量对应debug_info.xml中的 CTRL_TABLE - YT_TABLE。
   - 遥调数据来自装置配置文件device_config.xml中 FUNCS - CTRL_TABLE - YT_TABLE。

> **提示：**
>
> - 遥调操作涉及设备参数变更，请务必确认设定值和操作对象，避免误操作。
> - 若遥调量显示异常或操作无响应，请检查设备连接、使能开关权限配置及相关安全设置。
> - 导出功能支持将结果导出为Excel等格式，便于后续分析和归档。

### 5.9 出口传动管理与操作

出口传动功能用于对设备出口的开关状态进行远程监控与操作，便于用户对各个出口的运行状态进行统一管理和控制。界面如下图所示：

![出口传动管理界面示例](./help/出口传动管理界面示例.png)

1. **界面说明**

   - 出口传动列表以表格形式展示，包含序号、短地址、描述、操作等字段，便于用户直观查看和操作各个出口点。
   - 支持通过顶部筛选栏按短地址、描述等关键字进行模糊查询，提升查找效率。
   - 可通过"刷新"按钮进行数据刷新，确保出口状态的实时性。
   - 每一行均可直接进行出口操作（如"动作"），操作按钮直观明了。

2. **字段说明**

   - **序号**：出口传动点的唯一编号，便于排序和查找。
   - **短地址**：出口传动点的标识名称，通常与设备实际点位一一对应。
   - **描述**：对出口传动点的功能或用途进行简要说明。
   - **操作**：包含"动作"等操作按钮，支持对出口进行远程控制。

3. **数据来源**
   - 出口传动对应debug_info.xml中的 CTRL_TABLE - DRIVE_TABLE。
   - 出口传动数据来自装置配置文件device_config.xml中 FUNCS - CTRL_TABLE - DRIVE_TABLE。

> **提示：**
>
> - 出口传动操作涉及设备实际动作，请务必确认操作对象和命令，避免误操作。
> - 若出口传动状态显示异常或操作无响应，请检查设备连接、使能开关权限配置及相关安全设置。

### 5.10 定值管理与操作

定值功能用于对设备的定值参数进行集中管理和远程配置，便于用户批量查看、修改和下发各类定值。定值管理界面如下图所示：

![定值管理界面示例](./help/定值管理界面示例.png)

1. **界面说明**

   - 定值列表以表格形式展示，包含序号、名称、描述、值、最小值、最大值、步长、单位、操作等字段，方便用户直观查看和管理各项定值参数。
   - 支持通过顶部筛选栏按名称、描述等关键字进行模糊查询，提升查找效率。
   - 提供"刷新"、"确认"、"导入"、"导出"等批量操作按钮，支持定值的Excel导入导出等功能，便于数据归档和批量配置。
   - 每一行均可直接进行定值操作（如输入新值、点击"确认"按钮进行下发），支持单条或多条定值的下发。

2. **字段说明**

   - **序号**：定值的唯一编号，便于排序和查找。
   - **名称**：定值点的标识名称，通常与设备实际点位一一对应。
   - **描述**：对定值参数的功能或用途进行简要说明。
   - **值**：当前定值参数的设定值，用户可直接输入新值进行修改。
   - **最小值**：该定值参数的出厂最小值。
   - **最大值**：该定值参数的出厂最大值。
   - **步长**：该定值参数的步长。
   - **单位**：定值参数的计量单位。
   - **操作**：包含"确认"操作按钮，支持定值的远程下发。

3. **定值分页说明**

   当定值参数默认启用分页功能。分页功能可有效提升大数据量场景下的浏览和操作效率，避免页面卡顿。分页相关说明如下：

   - **分页控件**：界面底部会显示分页条，支持快速跳转、每页条数切换等操作。
   - **每页条数**：可根据实际需求选择每页显示15、30、50、100条定值数据。
   - **翻页操作**：切换页码后，系统会自动加载对应页的数据。

4. \*\*定值导入与导出说明

定值管理模块支持定值参数的导入与导出，便于批量配置和归档。具体说明如下：

**导入导出格式支持**

- 支持 `xlsx`、`csv`、`xml` 等主流文件格式的定值导入与导出，满足不同场景的数据交互需求。

**定值导入流程**

- 点击"导入"按钮，选择本地的定值文件（支持上述格式）。
- 系统会自动校验导入文件中各定值设定值的合法性（如超出最小/最大值、步长不符等会提示错误）。
- 导入时，系统会自动比对导入文件中的定值与当前装置实际定值的差异。
- 若存在差异，系统会弹出"定值差异窗口"，详细列出所有有差异的定值项。
- 用户可在差异窗口中勾选需要导入的定值项，支持部分或全部导入，提升灵活性和安全性。
- 确认后，系统仅对勾选的定值项进行导入和下发。

**定值导出流程**

- 点击"导出"按钮，可将当前装置的全部定值参数导出为 `xlsx`、`csv`、`xml` 等格式文件，便于归档和后续批量操作。

5. **数据来源**
   - 定值对应debug_info.xml中的 SETTING。
   - 定值数据来自装置配置文件device_config.xml中 PARA_GROUPS。
   - DCA_SP_TABLE为外采定值组。

![定值导入比对界面](./help/定值导入比对界面.png)

> **提示：**
>
> - 定值操作涉及设备参数变更，请务必确认设定值和操作对象，避免误操作。
> - 若定值显示异常或操作无响应，请检查设备连接、及相关安全设置。
> - 导入导出功能支持Excel、xml、csv等格式，便于批量管理和归档。
> - 支持批量选中多条定值进行统一下发操作，提升配置效率。

> - 定值导入前请务必核对文件内容，确保数据准确，避免误操作。
> - 差异窗口仅显示与当前装置定值不一致的项，便于快速定位和选择。
> - 导入操作不可逆，建议先导出当前定值作为备份。
> - 导入过程中如有格式或内容错误，系统会自动提示并阻止错误数据导入。

### 5.11 全部定值管理与操作

全部定值功能用于集中展示当前装置的所有定值参数，支持按分组进行筛选和管理，便于用户全局把控和批量操作。全部定值界面如下图所示：

![全部定值管理界面示例](./help/全部定值管理界面示例.png)

1. **界面说明**

   - 全部定值列表以表格形式展示，包含序号、分组、名称、描述、当前值、最小值、最大值、步长、单位、操作等字段。
   - 顶部提供分组筛选下拉框，用户可根据实际需求选择某一分组，仅显示该分组下的定值参数，也可选择"全部"查看所有定值。
   - 支持通过关键字模糊查询定值名称、描述等，提升查找效率。
   - 可通过"刷新"、"确认"、"导入"、"导出"等按钮进行定值的刷新、批量导入导出等操作。
   - 每一行均可直接进行定值操作（如输入新值、点击"确认"按钮进行下发），支持单条或多条定值的下发。

2. **字段说明**

   - **序号**：定值的唯一编号，便于排序和查找。
   - **定值组**：定值所属的分组名称，便于分类管理和筛选。
   - **名称**：定值点的标识名称，通常与设备实际点位一一对应。
   - **描述**：对定值参数的功能或用途进行简要说明。
   - **值**：当前定值参数的设定值，用户可直接输入新值进行修改。
   - **最小值**：该定值参数的出厂最小值。
   - **最大值**：该定值参数的出厂最大值。
   - **步长**：该定值参数的步长。
   - **单位**：定值参数的计量单位。
   - **操作**：包含"确认"操作按钮，支持定值的远程下发。

3. **分组筛选与分页说明**
   - 顶部分组筛选下拉框支持快速切换分组，切换后仅显示对应分组下的定值参数。
   - 支持分页浏览全部定值数据，提升大数据量场景下的操作流畅性。

导入和导出定值文件选择定值文件格式：
![选择定值文件格式](./help/选择定值文件格式.png)

全部定值Excel格式：
![全部定值Excel格式](./help/全部定值Excel格式.png)

全部定值CSV格式：
![全部定值CSV格式](./help/全部定值CSV格式.png)

全部定值XML格式：
![全部定值XML格式](./help/全部定值XML格式.png)

4. **定值导入与导出说明**
   - 支持全部定值的批量导入与导出，导入导出流程与定值管理章节一致，详见上文"定值导入与导出说明"。
   - 导入时可根据分组自动匹配对应定值，系统会校验数据合法性并弹出差异窗口，支持按需选择导入项。

![全部定值导入比对](./help/全部定值导入比对.png)

> **提示：**
>
> - 全部定值操作涉及设备参数批量变更，请务必核对分组及设定值，避免误操作。
> - 分组筛选有助于快速定位和管理目标定值，建议合理使用。
> - 导入导出功能支持Excel、xml、csv等格式，便于批量管理和归档。
> - 支持批量选中多条定值进行统一下发操作，提升配置效率。
> - 导入操作不可逆，建议先导出当前全部定值作为备份。

### 5.12 装置对时功能

装置对时功能用于将当前计算机的系统时间同步到装置，确保装置内部时钟的准确性，便于后续装置数据读取、日志记录等功能的时序一致。装置对时界面如下图所示：

![装置对时界面示例](./help/装置对时界面示例.png)

1. **界面说明**

   - 界面顶部显示当前装置的时间信息，包括系统当前时间、装置当前时间（即将同步的目标时间）。
   - 当前时间默认填充为当前计算机系统时间，用户可手动调整目标时间，也可点击"此刻"按钮将系统时间快速填入。
   - 提供"此刻"、"读取"、"写入"操作按钮，分别设置装置时间为当前时间、读取装置当前时间，以及写入装置时间。

2. **操作说明**
   - **此刻**：设置装置时间对应的时间控件为当前操作系统的时间。
   - **读取**：读取目标装置的当前时间，显示在装置时间对应的时间控件内。
   - **写入**：将装置时间对应的时间控件内的时间写入到装置。
   - 操作完成后，界面会提示操作结果，若对时操作请检查装置连接状态。

> **提示：**
>
> - 对时操作会直接修改装置内部时钟，请务必确认目标时间准确，避免因时序错误影响后续数据和事件记录。
> - 建议定期对装置进行校时，确保数据一致性和准确性。
> - 若对时操作无响应或失败，请检查装置连接、及网络状态，必要时联系技术支持。

### 5.13 文件上传功能

文件上传功能用于将装置中的文件批量或单个上传到本地计算机目录，便于用户备份、分析或后续处理。文件上传界面如下图所示：

![文件上传界面示例](./help/文件上传界面示例.png)

1. **界面说明**

   - 左上方可选择装置目录（如 `/shr`），支持下拉切换不同目录，快速定位目标文件。
   - 文件列表以表格形式展示，包含序号、文件名称、文件大小、最后修改时间、进度、状态、操作等字段。
   - 支持多选文件进行批量上传、取消上传、清空列表等操作。
   - 每个文件的上传进度以进度条直观显示，状态栏实时反馈上传结果。

2. **主要操作说明**

   - **获取文件**：点击"获取文件"按钮，系统会从装置当前目录读取文件列表并展示在表格中。
   - **上传文件**：勾选需要上传的文件，点击"上传文件"按钮，即可将选中文件上传到本地指定目录。
   - **取消上传**：上传过程中可随时点击"取消上传"按钮，终止正在进行的上传任务。
   - **设置路径**：可通过"设置路径"按钮选择本地保存目录，所有上传文件将保存至该目录下。
   - **清空列表**：点击"清空列表"按钮可移除所有已加载的文件记录，便于重新操作。

3. **字段说明**
   - **序号**：文件在当前列表中的编号，便于排序和查找。
   - **文件名称**：装置中的文件名，支持点击排序。
   - **文件大小**：文件的实际大小，单位为KB或MB。
   - **最后修改时间**：文件在装置上的最后更新时间，便于判断文件新旧。
   - **进度**：显示当前文件上传的进度百分比。
   - **状态**：显示文件当前的上传状态（如"上传中"、"已完成"、"已取消"等）。
   - **操作**：支持单个文件的上传、取消上传等操作。

> **提示：**
>
> - 文件上传前请先设置本地保存路径，避免文件丢失或覆盖。
> - 支持批量选择多个文件同时上传，提升操作效率。
> - 上传过程中请勿断开装置连接，否则可能导致上传失败。
> - 若上传失败或进度异常，请检查装置连接状态及本地磁盘空间。
> - 文件列表支持刷新，确保展示装置最新文件信息。

### 5.14 文件下载功能

文件下载功能用于将本地计算机中的文件下载到装置指定目录，便于用户进行文件更新。文件下载界面如下图所示：

![文件下载界面示例](./help/文件下载界面示例.png)

1. **界面说明**

   - 顶部可选择目标装置目录（如 `/shr`），支持下拉切换不同目录，快速定位目标下载位置。
   - 文件列表以表格形式展示，包含序号、文件名称、下载重命名、文件大小、进度、状态、操作等字段。
   - 支持批量选择多个本地文件进行下载，操作便捷高效。
   - 每个文件的下载进度以进度条直观显示，状态栏实时反馈下载结果。

2. **主要操作说明**

   - **选择文件**：点击"选择文件"按钮，弹出本地文件选择窗口，支持多选，将选中的文件添加到下载列表。
   - **程序打包**：点击"程序打包"按钮，弹出程序打包窗口，支持多选，将选中的文件打包压缩为zip文件。
   - **下载文件**：勾选需要下载的文件，点击"下载文件"按钮，即可将本地文件下载到装置指定目录。
   - **取消下载**：下载过程中可随时点击"取消下载"按钮，终止正在进行的下载任务。
   - **导入列表**：点击"导入列表"按钮，可将本地已保存的下载任务列表（如上次未完成的下载任务）批量导入到当前下载列表，便于任务恢复和批量操作。
   - **导出列表**：点击"导出列表"按钮，可将当前下载任务列表（包括文件名、目标路径等信息）导出为本地文件，便于备份、迁移或后续继续下载。
   - **清空列表**：点击"清空列表"按钮可移除所有已加载的文件记录，便于重新操作。
   - **下载重启开关**：在下载列表上方提供"下载完成后重启装置"勾选开关。勾选后，所有下载任务完成后，系统会自动向装置发送重启指令，实现自动重启，便于固件或配置文件更新后立即生效。未勾选时，下载完成后装置不会自动重启，用户可根据实际需求选择是否启用该功能。

3. **字段说明**
   - **序号**：文件在当前列表中的编号，便于排序和查找。
   - **文件名称**：本地计算机中的文件名，支持点击排序。
   - **文件重命名**：设置文件将被下载到装置的新的命名。
   - **文件大小**：文件的实际大小，单位为KB或MB。
   - **进度**：显示当前文件下载的进度百分比。
   - **状态**：显示文件当前的下载状态（如"下载中"、"已完成"、"已取消"等）。
   - **操作**：支持单个文件的下载、取消下载等操作。

> **提示：**
>
> - 文件下载前请先设置装置目标路径，避免文件存放位置错误或覆盖重要文件。
> - 支持批量选择多个文件同时下载，提升操作效率。
> - 下载过程中请勿断开装置连接，否则可能导致下载失败。
> - 若下载失败或进度异常，请检查装置连接状态及装置磁盘空间。
> - 文件列表支持刷新，确保展示装置最新文件信息。

#### 5.14.1 程序打包功能

程序打包功能用于将多个本地文件打包压缩为zip文件后，再统一下载到装置指定目录，便于批量部署和管理。界面如下图所示：

![程序打包界面](./help/程序打包界面示例.png)

1. **界面说明**

   - 顶部可选择打包之后的保存路径（如 `e://App`），支持修改保存目录，快速定位目标打包位置。
   - 文件列表以表格形式展示，包含序号、文件名称、文件大小、文件路径、最后修改时间、操作等字段。
   - 支持批量选择多个本地文件进行打包，操作便捷高效。
   - 每个文件可单独移除或重新选择，方便灵活调整打包内容。

2. **主要操作说明**

   - **选择文件**：点击"选择文件"按钮，弹出文件选择窗口，支持多选，将选中的文件添加到打包列表。
   - **打包**：点击"打包"按钮，将当前列表中的所有文件压缩为一个zip文件，自动命名并添加到下载列表。
   - **添加到下载列表**：打包完成后，可以将zip文件添加到下载任务列表，后续可统一下载到装置指定目录。
   - **移除文件**：点击每行的"删除"按钮，可将该文件从打包列表中移除。
   - **清空列表**：点击"清空"按钮可移除所有已加载的文件记录，便于重新操作。

3. **字段说明**
   - **序号**：文件在当前列表中的编号，便于排序和查找。
   - **文件名称**：本地计算机中的文件名，支持点击排序。
   - **文件大小**：文件的实际大小，单位为KB或MB。
   - **文件路径**：文件在本地计算机中的完整路径，便于确认来源。
   - **最后修改时间**：文件的最后更新时间，便于判断文件新旧。
   - **操作**：支持单个文件的移除等操作。

> **提示：**
>
> - 程序打包适用于需要批量部署、升级或迁移多个文件的场景，提升操作效率。
> - 打包前请确认所有文件均为最新版本，避免遗漏或覆盖重要文件。
> - 打包完成后可在下载任务列表中统一管理和下载zip文件。
> - 若打包失败或文件异常，请检查本地磁盘空间及文件权限。

#### 5.14.2 文件选择界面说明

**文件选择界面**支持灵活选择本地文件和文件夹，便于批量操作。主要功能说明如下：

![文件下载文件选择界面](./help/文件下载文件选择界面.png)

1. **界面结构**

   - 左侧为本地文件目录树，支持展开、收起、切换不同磁盘或文件夹，便于快速定位目标文件。
   - 右侧为已选择的文件列表，展示所有待操作的文件及其详细信息（如文件名、路径、大小等）。

2. **文件与文件夹选择**

   - 可通过点击左侧目录树中的文件或文件夹进行选择。
   - 支持**多选**：按住`Shift`键可实现区间多选。
   - 选中的文件会在右侧"已选择列表"中展示，可随时移除不需要的文件。

3. **操作说明**

   - 点击"确定"按钮后，所有已选文件将被添加到后续的下载或打包任务中。
   - 点击"取消"按钮可关闭文件选择窗口，不做任何更改。
   - 右上角"清空列表"按钮可一键移除所有已选文件，便于重新选择。

4. **注意事项**
   - 支持选择本地任意目录下的文件和文件夹，提升批量操作效率。
   - 建议在选择大量文件时，优先选择文件夹进行整体添加，避免遗漏。
   - 文件选择完成后，可在后续的下载或打包列表中进一步调整、重命名或移除文件。

> **提示：**
>
> - 文件选择时，按住`Shift`键可实现区间多选，适合连续选择多个文件。
> - 文件选择界面加载大量文件可能需要稍微等待刷新过程。

### 5.15 装置操作功能说明

**VisualDebug**在"装置操作"菜单下，集成了多项常用的装置管理与维护功能，便于用户对当前连接的装置进行快速操作。相关界面如下图所示：

![装置操作界面示例](./help/装置操作界面示例.png)

1. **功能入口说明**

   - 进入"装置操作"菜单后，界面顶部展示了多个常用操作按钮，包括：
     - **手动录波**：手动触发装置进行录波操作，便于采集当前时刻的波形数据。
     - **装置复归**：对装置进行复归操作，用于恢复装置至初始状态。
     - **清除报告**：一键清除装置内已生成的所有报告数据，便于空间管理或重新测试。
     - **清除录波**：一键清除装置内所有录波文件，释放存储空间或清理历史数据。

2. **主要操作说明**

   - **手动录波**
     - 点击"手动录波"按钮后，装置将立即执行一次录波操作，生成新的录波文件，便于后续分析。
     - 录波完成后，可在文件上传模块中查看和获取录波文件。
   - **装置复归**
     - 点击"装置复归"按钮，装置将执行复归操作，恢复到初始运行状态。
   - **清除报告**
     - 点击"清除报告"按钮后，装置内所有已生成的报告文件将被清空，请谨慎操作。
     - 清除后无法恢复，建议提前备份重要报告数据。
   - **清除录波**
     - 点击"清除录波"按钮后，装置内所有录波文件将被清空，释放存储空间。
     - 清除后无法恢复，建议提前下载所需录波文件。

3. **操作结果状态显示**

- 在每次执行装置操作（如手动录波、装置复归、清除报告、清除录波）后，界面下方会以状态图片和文字的方式直观显示操作结果。例如：
  - 操作成功时，显示绿色对勾图标及"操作成功"提示。
  - 操作失败时，显示红色叉号图标及失败原因说明。
- 用户可根据状态图片和提示信息，快速判断操作是否完成及后续处理建议。

4. **注意事项**
   - 以上操作均需装置处于正常连接状态，操作前请确认网络畅通。
   - 部分操作（如清除报告、清除录波）为不可逆操作，建议操作前做好数据备份。
   - 若操作无响应或失败，请检查装置连接状态或联系技术支持。

> **提示：**
>
> - "手动录波"适用于需要即时采集波形数据的场景，便于故障分析和调试。
> - "装置复归"适用于调试和维护后恢复装置正常状态。
> - "清除报告"和"清除录波"有助于管理装置存储空间，建议定期清理无用数据。

### 5.16 变量调试功能说明

**VisualDebug**提供了**变量调试**功能，便于用户对装置内部的各类变量进行实时监控、调试和修改。该功能适用于调试、测试及参数优化等多种场景。界面如下图所示：

![装置变量调试示例](./help/装置变量调试示例.png)

1. **界面说明**

   - 变量调试界面以表格形式展示所有可调试变量，包含序号、名称、描述、类型、值等字段，便于用户直观查看和操作。
   - 支持通过顶部筛选栏按变量名称等关键字进行模糊查询，快速定位目标变量。
   - 每一行变量右侧提供"确认"，"删除"操作按钮，"确认"操作支持对变量值进行在线修改。

2. **主要字段说明**

   - **序号**：变量的唯一编号，便于排序和查找。
   - **名称**：变量的标识名称，通常与装置内部参数一一对应。
   - **描述**：对变量的功能或用途进行简要说明。
   - **类型**：变量的数据类型（如FLOAT、INT等），需根据类型输入对应格式的值。
   - **值**：实时显示变量的当前数值，便于监控和分析。
   - **操作**：包含"确认"，"删除"操作，支持对变量进行写入操作和删除操作。

3. **常用操作说明**

   - 点击"导入"按钮可批量导入变量配置，提升调试效率。
   - 点击"导出"按钮可将当前变量列表导出为本地文件，便于归档或技术支持。

4. **注意事项**
   - 修改变量值前请确认操作对象及数值范围，避免因误操作导致装置异常。
   - 写入操作需装置处于正常连接状态，操作前请确认网络畅通。
   - 若变量写入失败或显示异常，请检查装置连接状态或联系技术支持。

> **提示：**
>
> - 变量调试功能适用于装置参数优化、功能验证等多种场景，建议仅由专业人员操作。
> - 批量操作时请仔细核对变量及数值，避免误操作影响装置运行。

### 5.17 SOE报告功能说明

**VisualDebug**提供了**SOE报告**功能，便于用户对装置产生的各类事件进行详细追溯、分析和管理。SOE报告界面如下图所示：

![SOE报告功能界面](./help/SOE报告功能界面示例.png)

1. **界面说明**

   - 顶部提供日期范围选择、事件类型筛选、关键字搜索等功能，支持精准定位目标事件。
   - 事件列表以表格形式展示，包含**报告编号**、**时间**、**描述**等字段，支持按时间等排序。
   - 右上角提供"搜索"、"保存"、"自动刷新"、"清除列表"等快捷操作按钮。

2. **主要字段说明**

   - **报告编号**：每条SOE事件的唯一标识，便于追溯和引用。
   - **时间**：事件发生的精确时间，精度可达毫秒级，便于分析事件先后顺序。
   - **描述**：对事件内容的简要说明，如"遥信分闸动作"、"遥控合闸成功"等。

3. **常用操作说明**

   - 通过顶部筛选栏选择日期范围、输入关键字，快速筛选目标事件。
   - 点击"保存"按钮，可将当前筛选结果导出为Excel或者rpt等格式，便于归档或技术支持。

4. **注意事项**

   - SOE事件数据来源于装置实时采集，需保证装置与软件正常通信。
   - 若SOE数据异常或缺失，请检查装置状态或联系技术支持。

5. **数据来源**
   - SOE报告配置来自装置配置文件device_config.xml中的 FUNCS - REPORT_TABLE - SOE_TABLE。

> **提示：**
>
> - SOE报告功能适用于故障追溯、运行分析、事件统计等多种场景，建议定期导出归档。

### 5.18 运行报告功能说明

**VisualDebug**提供了**运行报告**功能，便于用户对装置的运行状态、事件记录及相关操作进行全面追溯和管理。运行报告界面如下图所示：

![运行报告功能界面](./help/运行报告功能界面示例.png)

1. **界面说明**

   - 顶部提供日期范围选择、事件类型筛选、关键字搜索等功能，便于精准查找目标运行记录。
   - 事件列表以表格形式展示，包含**报告编号**、**时间**、**描述**等字段，支持排序与筛选。
   - 右上角提供"搜索"、"保存"、"自动刷新"、"清除列表"等快捷操作按钮，方便用户实时获取和保存报告数据。

2. **主要字段说明**

   - **报告编号**：每条运行事件的唯一标识，便于追溯和引用。
   - **时间**：事件发生的精确时间，便于分析事件顺序和定位问题。
   - **描述**：对事件内容的简要说明，如"开关分闸操作"、"遥控合闸成功"等。

3. **常用操作说明**

   - 通过顶部筛选栏选择日期范围、输入关键字，快速筛选目标运行事件。
   - 点击"刷新"按钮，实时获取最新运行报告数据。
   - 点击"保存"按钮，可将当前筛选结果导出为Excel或者rpt等格式，便于归档或技术支持。

4. **注意事项**

   - 运行报告数据来源于装置实时采集，需保证装置与软件正常通信。
   - 若运行报告数据异常或缺失，请检查装置状态或联系技术支持

5. **数据来源**
   - 运行报告配置来自装置配置文件device_config.xml中的 FUNCS - REPORT_TABLE - RUN_TABLE。

> **提示：**
>
> - 运行报告功能适用于运行状态追溯、事件分析、运维统计等多种场景，建议定期导出归档。

### 5.19 整组报告功能说明

**VisualDebug**提供了**整组报告**功能，便于用户对装置内同一组相关的报告数据进行集中管理、批量操作和统一导出。整组报告界面如下图所示：

![整组报告功能界面](./help/整组报告功能界面示例.png)

1. **界面说明**

   - 顶部提供日期范围选择、关键字搜索等功能，便于精准定位目标报告组。
   - 事件列表以表格形式展示，包含**报告编号**、**时间**、**描述**等字段，支持排序和筛选。
   - 右上角提供"搜索"、"保存"、"自动刷新"、"清除列表"等快捷操作按钮，提升批量管理效率。
   - 列表中每条记录右侧提供"召唤报告"、"取历史报告"、"保存结果"、"清除页面内容"等操作菜单，便于对单条或多条报告进行管理。

2. **主要字段说明**

   - **报告编号**：每条报告的唯一标识，便于追溯和引用。
   - **时间**：报告生成的精确时间，便于分析和归档。
   - **描述**：对报告内容的简要说明。

3. **常用操作说明**

   - 通过顶部筛选栏选择日期范围、输入关键字，快速筛选目标报告组。
   - 点击单条报告右键菜单的"召唤录波"按钮，可召唤装置的录波文件。
   - 支持列表刷新，确保展示装置最新的整组报告信息。

4. **注意事项**

   - 整组报告数据来源于装置实时采集，需保证装置与软件正常通信。
   - 若整组报告数据异常或缺失，请检查装置状态或联系技术支持。

5. **数据来源**
   - 整组报告配置来自装置配置文件device_config.xml中的 FUNCS - REPORT_TABLE - FAULT_TABLE。

> **提示：**
>
> - 整组报告功能适用于批量数据归档、事件集中分析、技术支持等多种场景，建议定期整理和导出。
> - 导出报告时可选择rpt格式。

### 5.20 动作报告功能说明

动作报告功能用于对装置产生的各类动作事件进行集中展示、查询和管理，便于用户追溯操作历史、分析装置行为。动作报告界面如下图所示：

![动作报告功能界面](./help/动作报告功能界面示例.png)

1. **界面说明**

   - 顶部提供日期范围选择、关键字搜索等功能，支持精准查询目标动作事件。
   - 事件列表以表格形式展示，包含**报告编号**、**时间**、**描述**等字段，支持排序和筛选。
   - 右上角提供"搜索"、"保存"、"自动刷新"、"清除列表"等快捷操作按钮，方便用户实时获取和保存动作报告数据。
   - 列表中每条记录右侧提供操作菜单，包括"相同内容搜索"、"相同内容过滤"、"保存结果"、"显示/隐藏时间列"等。

2. **主要字段说明**

   - **报告编号**：每条动作事件的唯一标识，便于追溯和引用。
   - **时间**：动作事件发生的精确时间，便于分析事件顺序和定位问题。
   - **描述**：对动作事件内容的简要说明，如"遥控分闸成功"、"保护动作跳闸"等。

3. **常用操作说明**

   - 通过顶部筛选栏选择日期范围、输入关键字，快速筛选目标动作事件。
   - 点击"搜索"按钮，获取符合条件的最新动作报告数据。
   - 点击"保存"按钮，可将当前筛选结果导出为Excel或rpt等格式，便于归档或技术支持。

4. **注意事项**

   - 动作报告数据来源于装置实时采集，需保证装置与软件正常通信。
   - 若动作报告数据异常或缺失，请检查装置状态或联系技术支持。

5. **数据来源**
   - 动作报告配置来自装置配置文件device_config.xml中的 FUNCS - REPORT_TABLE - TRIP_TABLE。

> **提示：**
>
> - 动作报告功能适用于操作追溯、事件分析、运维统计等多种场景，建议定期导出归档。

### 5.21 审计报告功能说明

审计报告功能用于对装置及系统的各类操作、事件、访问记录等进行集中审计、追溯和管理，帮助用户全面掌握系统运行和操作历史，提升安全性与合规性。审计报告界面如下图所示：

![审计报告功能界面](./help/审计报告功能界面示例.png)

1. **界面说明**

   - 顶部提供日期范围选择等功能，便于精准查询目标审计记录。
   - 审计记录以表格形式展示，包含**报告编号**、**名称**、**时间**、**操作地址**、**操作参数**、**值**、**步骤**、**源**、**源类型**、**结果**等字段，支持排序和筛选。
   - 右上角提供"搜索"、"保存"、"清除列表"等快捷操作按钮，方便用户批量管理和导出审计数据。

2. **主要字段说明**

   - **报告编号**：每条审计记录的唯一标识编号，便于追溯和引用。
   - **名称**：操作或事件的名称，简要描述本条审计内容。
   - **时间**：操作或事件发生的具体时间，精确到秒，便于分析和定位。
   - **操作地址**：被操作的对象地址或路径，如寄存器地址、文件路径等。
   - **操作参数**：本次操作涉及的参数内容，详细记录操作细节。
   - **值**：操作涉及的具体数值或内容，如写入的值、读取的结果等。
   - **步骤**：操作所属的流程步骤编号或名称，便于还原完整操作流程。
   - **源**：发起本次操作的来源，如用户账号、系统模块、外部接口等。
   - **源类型**：来源的类型分类，如"用户操作"、"系统自动"、"远程接口"等。
   - **结果**：本次操作的执行结果，如"成功"、"失败"，或具体的错误信息。

3. **常用操作说明**

   - 通过顶部筛选栏选择日期范围，快速筛选目标审计记录。
   - 点击"保存"按钮，可将当前筛选结果导出为Excel或rpt等格式，便于归档、分析或合规审查。
   - 点击"搜索"按钮，实时获取最新的审计数据，确保信息时效性。

4. **注意事项**
   - 审计报告数据来源于系统和装置的实时日志采集，需保证装置与软件正常通信。
   - 若审计数据异常或缺失，请检查装置状态、网络连接或联系技术支持。
   - 审计数据量较大时，建议定期归档和清理，避免影响系统性能。

> **提示：**
>
> - 审计报告功能适用于安全合规、操作追溯、异常分析等多种场景，建议定期导出归档。
> - 导出数据时请妥善保存，防止敏感信息泄露。

### 5.22 设置功能说明

设置功能用于对软件的系统参数和数据刷新策略进行集中管理，提升个性化和高效性。设置界面分为"系统配置"和"参数配置"两部分。

1. **界面说明**
   - 通过左下角齿轮图标"设置"进入设置页面，左侧为功能菜单，右侧为具体配置内容。
   - 包含"系统配置"和"参数配置"两个子菜单。

系统配置功能如下截图：
![系统配置功能界面](./help/系统配置功能界面.png)

参数配置功能如下截图：
![参数配置功能界面](./help/参数配置功能界面.png)

2. **系统配置**

   - **主要字段说明：**
     - **系统名称**：当前软件的名称（如 VisualDebug），不可修改。
     - **系统版本**：当前软件的版本号（如 1.00.001），不可修改。
     - **第三方波形工具路径**：可选择本地第三方波形工具的可执行文件路径，便于后续集成调用。
   - **操作说明：**
     - 选择或填写相关信息后，点击"保存"按钮完成设置。
     - "重置"按钮可恢复为默认值。

3. **参数配置**

   - **主要字段说明：**
     - **定值刷新间隔**：定值数据自动刷新的时间间隔（ms）。
     - **报告刷新间隔**：报告数据自动刷新的时间间隔（ms）。
     - **状态量刷新间隔**：状态量数据自动刷新的时间间隔（ms）。
     - **变量刷新间隔**：变量数据自动刷新的时间间隔（ms）。
   - **操作说明：**
     - 可通过"+"或"-"按钮调整数值，或直接输入所需间隔。
     - 设置完成后点击"保存"按钮生效，"重置"按钮恢复为默认值。

4. **注意事项**
   - 所有设置均会自动保存到本地配置文件，重启软件后依然有效。
   - 建议根据实际需求合理设置刷新间隔，避免过于频繁导致系统资源占用过高。
   - 若设置项异常或保存失败，请联系技术支持。

### 5.23 主题设置功能说明

点击左侧菜单的主题按钮，弹出菜单主题设置和布局设置，主题设置功能用于自定义软件界面的视觉风格，提升个性化体验和可用性。主题设置界面分为主题模式、预设主题、主题颜色、特殊模式和布局设置等部分，具体说明如下：

主题设置功能如下截图：
![主题设置功能界面](./help/主题设置功能界面.png)

布局设置功能如下截图：
![布局设置功能界面](./help/布局设置功能界面.png)

1. **主题模式切换**

   - 支持"浅色模式"和"深色模式"两种主题风格，用户可根据环境和个人喜好自由切换。
   - 切换后界面整体色调、背景、字体等会自动适配，提升视觉舒适度。
   - 操作方法：点击主题设置弹窗顶部的"浅色"或"深色"按钮即可切换。

2. **预设主题**

   - 提供多种预设主题风格（如默认主题、深色主题、自然主题、温暖主题、优雅主题、护眼模式等），一键切换整体配色方案。
   - 每个预设主题下方有简要描述，便于选择。
   - 操作方法：在"预设主题"区域点击对应主题卡片即可应用。

3. **主题颜色自定义**

   - 支持自定义主题主色调，内置多种主色（如科技蓝、自然绿、活力橙、优雅紫、浪漫粉、清新青、明亮黄、温暖橙、清柠绿、深蓝宝、金色、中国红等）。
   - 选择后界面主色、按钮、图标等会自动适配。
   - 操作方法：在"主题颜色"区域点击所需颜色卡片即可生效。

4. **特殊模式**

   - 支持"灰色模式"和"色弱模式"两种特殊视觉模式，满足特定用户需求。
   - 灰色模式：界面整体变为灰阶，适用于特殊纪念日等场景。
   - 色弱模式：优化色彩对比度，提升色弱用户的可用性。
   - 操作方法：在"特殊模式"区域切换对应开关即可启用/关闭。

5. **布局设置**

   - 提供多种布局样式（如左侧菜单、顶部菜单、混合菜单等），可根据实际需求切换。
   - 支持全局主题开关，统一界面风格。
   - 界面设置项丰富，包括：
     - 水印开关：开启后界面显示公司水印，提升品牌识别。
     - 页脚开关：控制是否显示页面底部信息。
     - 面包屑、面包屑图标、标签栏、标签栏图标、抽屉菜单等界面元素可按需开启或关闭。
   - 操作方法：点击主题菜单下的"布局设置"，在弹窗中选择布局样式、切换各项界面设置开关。

6. **注意事项**
   - 所有主题和布局设置均会自动保存，重启软件后依然生效。
   - 切换主题、颜色或布局后，部分界面可能需刷新以完全生效。
   - 若主题设置异常或保存失败，请联系技术支持。

> **提示：**
>
> - 合理使用主题和布局设置，可显著提升软件的视觉体验和操作效率。
> - 特殊模式适用于特定场景，建议根据实际需求开启。
> - 主题和布局设置仅影响本地界面显示，不影响数据和功能。

### 5.24 语言设置功能说明

**VisualDebug**支持多语言切换，方便不同语言用户的使用需求。语言设置界面如下图所示：

![语言设置功能界面](./help/语言设置功能界面.png)

1. **入口说明**

   - 在软件左下角菜单栏，点击"语言"图标（地球仪样式），弹出语言选择菜单。
   - 菜单中列出所有可用语言，当前支持：简体中文、英文、西班牙语、法语。

2. **切换方法**

   - 点击所需语言选项，界面会自动切换为对应语言，无需重启软件。
   - 切换后，所有菜单、按钮、提示信息等界面文本将即时更新为所选语言。
   - 语言设置会自动保存，重启软件后依然生效。

3. **注意事项**
   - 若部分内容未能即时切换，请尝试刷新界面或重启软件。
   - 语言切换仅影响本地界面显示，不影响数据和功能。
   - 如遇翻译不准确或界面异常，请联系技术支持。

> **提示：**
>
> - 合理使用多语言切换功能，可提升跨国团队协作和多语种用户体验。
> - 未来版本将持续增加更多语言支持，敬请关注。

### 5.25 控制台功能

**功能简介：**
控制台用于实时显示系统日志、调试信息、操作结果和错误提示，帮助用户快速定位和排查问题。

![控制台功能界面](./help/控制台功能界面.png)

**主要功能与操作说明：**

- 实时输出装置连接、操作执行、异常告警等各类日志信息。
- 支持日志内容的全选、复制、清空操作，便于用户保存或反馈日志。
- 右上角提供"清除"按钮，可一键清空当前日志内容。
- 右上角提供"最小化"按钮，可将控制台区域收起，节省界面空间。
- 日志内容支持自动滚动，始终显示最新信息。

**典型应用场景：**

- 调试过程中实时查看装置状态和操作反馈。
- 出现异常时快速定位问题原因。
- 复制日志内容用于技术支持或问题反馈。

## 6. 组态功能

### 6.1 目的

本章节描述了组态工具的操作说明，为使用者描述了操作流程步骤，方便使用者快速了解和使用组态工具。

### 6.2 位置

打开工具，左侧导航栏-【组态】

![组态功能位置界面](./help/组态功能位置界面.png)

### 6.3 界面布局

- 左上角：显示装置列表和调试列表通用，两边的装置列表复用一份数据。
- 左下角：组态列表，右键支持新增、编辑、删除、重命名等操作。左键点击显示组态界面。
- 右侧：显示组态界面

### 6.4 设备列表

组态监控装置数据，需要连接装置，左上角显示装置列表。

#### 6.4.1 新增

右上角的【+】可新增装置信息。

![新增装置界面](./help/新增装置界面.png)

#### 6.4.2 操作

选中装置列表中的某一项装置，右键选择对应的操作。此处操作和调试共用一份数据，删除后调试那边的装置列表也随之调整。

![装置列表操作界面](./help/装置列表操作界面.png)

### 6.5 组态列表

界面左下角按树形显示组态信息，根节点组态监控。组态按项目分类，所以需要先新增项目，在项目下新增组态。

#### 6.5.1 新增项目

右键【设备监控】节点，选择新增项目。
![新增项目界面](./help/新增项目界面.png)

输入项目名称，点击【确定】。
![新增项目界面2](./help/新增项目界面2.png)

设备监控节点下出现新增加的项目节点。
![新增项目界面3](./help/新增项目界面3.png)

#### 6.5.2 新增组态

组态属于不同的项目，需要选中项目节点，右键选择新增。
![新增组态界面1](./help/新增组态界面1.png)

输入组态名称，点击【确定】，项目节点下出现组态名称节点。
![新增组态界面2](./help/新增组态界面2.png)

#### 6.5.3 编辑组态

组态新增后是空白界面，需要添加图符才可成为组态界面。右键【设备数据监控】，选择【编辑组态】。
![编辑组态界面1](./help/编辑组态界面1.png)

具体操作参考【组态绘制】章节。
![编辑组态界面2](./help/编辑组态界面2.png)

#### 6.5.4 删除组态

选中项目下的组态节点右键选择【删除】。

---

### 6.6 组态绘制

组态编辑界面画布采用上（导航）+下（左中右）布局方式：

- 顶部：工具栏，提供撤销、保存、置前、置后、删除功能
- 左侧：图符列表，可拖拽到画布中。
- 中间：组态画布，需要显示的图符都在此画布展示。
- 右侧: 图符属性设置，可设置颜色、大小、位置等属性

#### 6.6.1 工具栏

- 撤销：取消当前操作
- 重做：恢复撤销的动作
- 置前：将选中的图符显示到最前面（可理解为不同层级，可实现图符重叠功能）。
- 置后：将选中的图符显示到最后面。
- 删除：删除选中的图符（可通过撤销恢复）
- 保存：保存当前画布内容（每次编辑画图后都需要手动点保存，工具不支持自动保存）

#### 6.6.2 图符

- **基础组件**：提供基础的图元符号包括：线、文本、矩形、圆、椭圆、三角形、弧形。添加到画图：鼠标左键按住图元拖拽到画布中，松开鼠标左键。将图符添加到拖拽的位置。

![基础组件界面](./help/基础组件界面.png)

- **电气符号**：默认提供常用的电气符号，操作方式和基础组件一样。
- **自定义组件**：显示自己绘制的图符（通过设备监控-右键-新增自定义组件扩展），操作方式和基础组件一样。

#### 6.6.3 画布

- **添加图符**：左侧图符列表界面选择对应的图符拖拽到画布
- **组合**：将几个图符合并成一个整体，移动时可整体移动、变换。框选：按住鼠标左键移动画出一个矩形框。操作：鼠标左键【框选】至少两个图符然后右键-【组合】

![画布组件界面](./help/画布组件界面.png)

- **解组**：将组合后的图符还原为单个图符，组合的反操作。必须是组合过的图符才支持解组。
- **拷贝**：框选或者点击图符，按快捷键ctrl+c复制，再按ctrl+v粘贴。
- **变换**：左键选中图符，出现变换框，可变换大小和旋转。边框中间的按钮表示这个方向改变大小，外层的单个按钮拖拽旋转。

![画布组件界面2](./help/画布组件界面2.png)

- **微调位置**：选中图符，按住键盘方向键可微调位置每次增加1px,如【→】表示往右移动1px。其他三个方向类似。

#### 6.6.4 关联短地址

图符通过关联短地址和设备数据关联。选中图符右键【关联短地址】

![关联短地址界面](./help/关联短地址界面.png)

- 遥信/遥测：遥信遥测的短地址，如Bxx.PxCx.M-CCMU.CMS_req_warn，工具根据此短地址从装置取值。
- 格式化：格式化显示值，如%.3f表示小数点后3为。
- 系数：显示值=装置值\*系数，默认为1
- 遥控：遥控短地址，配置后点击图符执行遥控操作。
- 遥控方式：选控|直控
- 遥控值：控合|控分
- 遥设：遥设短地址，点击图符

#### 6.6.5 显示配置

- **文本**：文本类型支持将装置值映射为文本值显示如0=断开，1=闭合
- **图符**：图符类型将装置值映射为图符显示，如0=红色，1=蓝色

#### 6.6.6 属性

点击图符，右侧显示可操作的属性。所有操作执行后记得点击工具栏保存按钮。

![属性界面](./help/属性界面.png)

### 6.7 组态监控

点击左侧组态，右侧显示监控界面。绑定的短地址会显示对应的值。
![组态监控界面](./help/组态监控界面.png)

#### 6.7.1 遥控

配置了遥控短地址的，点击图符自动执行遥控操作，成功弹出成功提示。失败弹出对应的失败信息。
![组态遥控界面](./help/组态遥控界面.png)

#### 6.7.2 遥设

配置了遥设短地址的图符点击弹出遥设对话框，输入遥设值。点写入。等待执行结束。
![组态遥设界面](./help/组态遥设界面.png)

### 6.8 自定义图符

除了工具提供的默认电气符号外，也支持自定义图符。当需要自己画各种图符时推荐使用此功能。自定义图符所有项目共用。

#### 6.8.1 入口

设备监控->新增自定义组件
![自定义图符界面](./help/自定义图符界面.png)

#### 6.8.2 绘制

从左侧拖拽图符到画布，组合成我们需要的图符。右侧设置颜色等属性，点击左侧选择类型输入名称，保存。

#### 6.8.3 使用

组态编辑界面->自定义图符

## 7. IT工具

本章节介绍软件内置的6项IT工具，帮助用户在调试、数据处理、工程管理等场景下高效完成常用数据转换与辅助操作。

### 7.1 批量下载

**功能简介：**
批量下载工具集成了三大功能模块：装置列表配置、下载文件配置、参数定值配置，支持多台装置的文件和定值参数批量下载与导入，极大提升工程文件管理与归档效率。

**功能结构与主要操作说明：**

1. **装置列表配置**

   - 展示所有可操作的装置，支持批量选择、添加、删除、排序等操作。
   - 可设置每台装置的连接信息、端口、是否加密、是否参与下载文件和定值导入等参数。
   - 支持一键全选/反选、批量导入导出装置信息。

2. **下载文件配置**

   - 展示所选装置的文件目录，支持多文件批量选择、导入、导出、删除、排序等操作。
   - 显示文件名、大小、路径、最后修改时间等详细信息。
   - 支持设置本地保存路径，所有下载文件将自动保存至指定目录。
   - 支持一键导入/导出文件列表，便于任务批量管理和恢复。

3. **参数定值配置**
   - 展示装置的全部定值参数，支持分组筛选、关键字搜索、批量选择、导入、导出等操作。
   - 显示参数名称、描述、最小值、最大值、步长、单位、当前值等详细信息。
   - 支持批量导出定值参数，便于归档和后续批量配置。

**典型应用场景：**

- 工程调试结束后批量归档多台装置的配置文件和定值参数。
- 多台设备定值参数统一导出备份，便于项目管理和数据分析。
- 快速恢复或迁移装置配置，提升工程效率。

### 7.2 程序打包功能

**功能简介：**
程序打包工具用于将多个本地文件批量压缩为zip文件，便于后续统一下载到装置指定目录，实现批量部署、升级或迁移。支持灵活选择文件、文件夹，操作便捷高效。

![程序打包功能界面](./help/程序打包界面示例.png)

**主要操作说明：**

- 点击"选择文件"按钮，弹出文件选择窗口，支持多选，将选中的文件或文件夹添加到打包列表。
- 文件列表中可查看文件名称、大小、路径、最后修改时间等详细信息。
- 支持单个文件移除、全部清空，灵活调整打包内容。
- 点击"打包"按钮，将当前列表中的所有文件压缩为一个zip文件，自动命名。

**典型应用场景：**

- 工程现场批量部署、升级多个程序文件。
- 多文件统一打包后下载到装置，提升操作效率。
- 需要整体迁移、归档或备份多个相关文件时。

### 7.3 XML格式化

**功能简介：**
XML格式化工具可将原始XML字符串快速美化为结构清晰、易于阅读的格式，支持自定义缩进和格式化预览，便于数据查看与编辑。

![XML格式化功能示例](./help/XML格式化功能示例.png)

**主要操作说明：**

- 在"待格式化XML"输入框中粘贴或输入原始XML内容。
- 可通过"折叠内容"开关选择是否折叠显示，调整"缩进尺寸"自定义每级缩进的空格数。
- 点击格式化按钮后，下方"格式化后XML"区域自动显示美化后的XML内容。
- 支持一键复制格式化结果，便于后续使用。

**典型应用场景：**

- 查看、编辑和美化装置配置、调试日志等XML文件。
- 处理第三方系统导出的XML数据，提升数据可读性和排查效率。

### 7.4 JSON格式化

**功能简介：**
JSON格式化工具可将原始JSON字符串快速美化为结构清晰、易于阅读的格式，支持字典排序、缩进自定义和格式化预览，便于数据查看、调试与编辑。

![JSON格式化功能示例](./help/JSON格式化功能示例.png)

**主要操作说明：**

- 在"待格式化JSON"输入框中粘贴或输入原始JSON内容。
- 可通过"字典排序"开关选择是否对JSON键名进行排序，调整"缩进尺寸"自定义每级缩进的空格数。
- 下方"格式化后JSON"区域自动显示美化后的JSON内容。
- 支持一键复制格式化结果，便于后续使用。

**典型应用场景：**

- 查看、编辑和美化接口返回、配置文件等JSON数据。
- 处理第三方系统导出的JSON内容，提升数据可读性和排查效率。

### 7.5 进制转换

**功能简介：**
进制转换工具支持在不同进制（如二进制、八进制、十进制、十六进制、Base64及自定义进制）之间快速转换数值，适用于协议调试、数据分析等多种场景。

![进制转换功能示例](./help/进制转换功能示例.png)

**主要操作说明：**

- 在"待转换数字"输入框中输入任意进制的数值（如十进制、十六进制等）。
- 工具会自动将输入的数值实时转换为二进制、八进制、十进制、十六进制、Base64等常用进制，并在对应栏位显示结果。
- 支持自定义进制转换，可通过下方自定义进制输入框设置目标进制，自动显示转换结果。
- 所有结果均可直接复制，便于后续使用。

**典型应用场景：**

- 协议调试、寄存器地址分析、报文内容进制转换。
- 设备参数、工程数据的多进制互转与校验。

### 7.6 温度转换

**功能简介：**
温度转换工具支持多种常见和专业温标（如开尔文、摄氏度、华氏度、兰金、德莱尔、牛顿、雷奥默尔、罗默）之间的快速换算，满足工程、科研等多场景需求。

![温度转换功能示例](./help/温度转换功能示例.png)

**主要操作说明：**

- 在任意温标输入框中输入温度数值，工具会自动计算并同步显示所有其他温标的换算结果。
- 支持常见温标（K、℃、℉、°R）及专业温标（°De、°N、°Ré、°Rø）之间的相互转换。
- 所有结果均可直接复制，便于后续使用。

**典型应用场景：**

- 设备温度参数调试与校验。
- 工程现场、科研实验等多温标单位换算。

### 7.7 文本加解密

**功能简介：**
文本加解密工具支持多种主流加密算法（如AES、TripleDES、Rabbit、RC4等），可对文本数据进行快速加密和解密，适用于敏感信息保护、技术支持等场景。

**支持算法：**
当前工具支持以下主流加密算法，用户可根据实际需求选择：

- **AES**（高级加密标准）
- **TripleDES**（三重数据加密标准）
- **Rabbit**（流加密算法）
- **RC4**（流加密算法）

![文本加解密功能示例](./help/文本加解密功能示例.png)

**主要操作说明：**

- 加密：在"待加密文本"输入框中输入明文内容，填写密钥，选择加密算法，系统自动生成加密后的密文。
- 解密：在"待解密文本"输入框中输入密文内容，填写密钥，选择解密算法，系统自动还原出明文内容。
- 支持多种加解密算法选择，结果区域可直接复制，便于后续使用。

**典型应用场景：**

- 敏感参数、配置文件的加密存储与传输。
- 技术支持、问题排查过程中加密信息的快速解密与验证。

## 8. 更多功能

点击左侧菜单栏底部的"更多"按钮，可快速访问以下辅助功能，提升软件的易用性和扩展性：

![更多功能示例说明](./help/更多功能示例说明.png)

### 8.1 导入工程配置

支持将导出的工具配置文件导入到当前工具软件环境。可选择配置类型（如全部、部分），并通过文件选择器指定导入文件路径。导入后，相关配置会自动应用于当前项目，便于项目迁移和批量配置。

**操作步骤：**

1. 点击"更多"菜单下的"导入工程配置"选项
2. 在弹出的文件选择器中选择要导入的配置文件
3. 选择导入类型（全部配置或部分配置）
4. 确认导入，系统会自动应用配置到当前环境

**注意事项：**

- 导入前建议备份当前配置，避免数据丢失
- 导入过程中请勿关闭软件或进行其他操作
- 如遇导入失败，请检查文件格式和内容完整性

![导入工程配置功能示例](./help/导入工程配置功能示例.png)

### 8.2 导出工程配置

支持将当前工程的全部或部分配置导出为文件，便于备份、归档或迁移到其他环境。可选择导出目录，导出后生成的配置文件可用于后续导入操作。

**操作步骤：**

1. 点击"更多"菜单下的"导出工程配置"选项
2. 选择导出类型（全部配置或部分配置）
3. 选择导出目录
4. 确认导出，系统会生成配置文件

**导出内容：**

- 全部
- 装置列表
- 组态列表

**使用场景：**

- 项目备份和归档
- 配置迁移到其他环境
- 团队协作配置共享
- 技术支持问题排查

![导出工程配置功能示例](./help/导出工程配置功能示例.png)

### 8.3 截图功能

一键截取当前软件界面，便于保存调试过程、问题反馈或技术支持。截图文件可复制到剪切板或者保存到指定目录，支持后续查看和分享。

**功能特点：**

- 支持全屏截图和区域截图
- 支持复制到剪切板或者保存到指定目录

**操作步骤：**

1. 点击"更多"菜单下的"截图"选项
2. 选择截图模式（全屏或区域）
3. 如选择区域截图，拖拽选择截图区域

**应用场景：**

- 调试过程记录
- 问题反馈和技术支持
- 操作步骤文档制作
- 界面设计参考

**注意事项：**

- 截图前请确保界面显示完整
- 建议在截图时隐藏敏感信息
- 截图文件会占用磁盘空间，建议定期清理

### 8.4 菜单搜索

支持通过菜单名称或路径进行快速搜索，便于在功能众多的情况下，迅速定位所需功能入口。输入关键字后，系统自动筛选匹配的菜单项，提高操作效率。

**搜索功能：**

- 支持模糊搜索和精确搜索
- 实时显示搜索结果
- 搜索结果高亮显示

**操作步骤：**

1. 点击"更多"菜单下的"菜单搜索"选项
2. 在搜索框中输入关键字
3. 系统自动显示匹配的菜单项
4. 点击搜索结果直接跳转到对应功能

![菜单搜索功能示例](./help/菜单搜索功能示例.png)

### 8.5 帮助文档

打开本帮助文档，查阅软件功能说明、操作指引、常见问题解答等内容，便于新手快速上手和遇到问题时自助查找解决方案。

**文档内容：**

- 软件功能介绍和概述
- 详细操作步骤说明
- 常见问题解答（FAQ）
- 故障排除指南
- 最佳实践建议

**使用方式：**

- 在线查看：点击"更多"菜单下的"帮助"选项
- 离线查看：帮助文档已集成在软件中
- 搜索功能：支持关键字搜索快速定位
- 目录导航：通过目录快速跳转到指定章节

**适用人群：**

- 新用户快速上手
- 日常使用参考
- 问题排查和解决
- 功能深入学习和研究

![帮助功能示例](./help/帮助功能示例.png)

### 8.6 关于信息

查看软件的基本信息，包括工具名称、版本号、机器码等。可用于软件注册、技术支持和版本管理。

**显示信息：**

- 软件名称和版本号
- 版权信息和公司信息
- 机器码（用于软件激活）
- 构建时间和版本标识

**主要用途：**

- 软件版本确认
- 激活码申请（需要机器码）

**操作说明：**

1. 点击"更多"菜单下的"关于"选项
2. 查看软件详细信息
3. 复制机器码用于激活申请

**注意事项：**

- 机器码是软件激活的唯一标识
- 请妥善保管机器码信息
- 版本信息有助于问题排查

![关于功能示例](./help/关于功能示例.png)

> **提示：**
>
> - "更多"菜单下的功能为辅助性工具，旨在提升软件的灵活性和用户体验。
> - 导入导出操作建议定期进行，防止数据丢失。
> - 如遇功能异常或操作疑问，请参考帮助文档或联系技术支持。
> - 建议定期备份重要配置和截图文件。
> - 合理使用搜索功能可显著提升操作效率。

## 9. 常见问题解答（FAQ）

- **Q: 装置设备连接上之后分组菜单显示不正确？**
  - 检查装置shr目录下的urpc是否更新为工具配套版本。
  - 读取shr目录下debug_info.xml上传本地，联系技术支持进行问题定位。
- **Q: 无法连接设备怎么办？**
  - 检查设备电源和网络连接，确保与电脑处于同一局域网。
  - 检查防火墙设置，确保软件有网络访问权限。
- **Q: 配置导入失败？**
  - 请确认导入的配置文件格式正确，且为**VisualDebug**支持的格式。
- **Q: 加密的Excel文件无法导入？**
  - 由于IT策略影响，加密的Excel无法识别，如需导入加密的Excel文件，请先自行解密。
- **Q: 软件界面显示异常？**
  - 尝试切换主题或重启软件，如仍有问题请联系技术支持。
- **Q: 定值导入后差异的定值未显示出来？**
  - 请确认导入的定值文件Excel/xml/csv中的定值组名称是否和工具分组菜单上的组名称是否完全一致，不一致是无法比较出差异。
- **Q: 程序打包功能打包出来的文件下载到装置哪个目录？**
  - 打包出来的zip为加密格式，需要下载到装置的/dwld目录，下载完成后需要重启才能生效。
- **Q: 文件下载界面上勾选了下载完成重启为什么不生效？**
  ![重启失败](./help/重启失败.png)
  - 重启对CPU板的固件有要求。若ls -l /sbin/reboot显示为上图软连接，则不支持reboot，需要升级固件。

## 10. 技术支持

如遇到无法解决的问题，请通过以下方式联系我们：

- **公司**：思源电气股份有限公司
- **部门**: 中央研究院-嵌入式应用开发部
- **专业组**: 工具软件开发组

感谢您使用本软件，祝您调试顺利！
