/**
 * 启动性能监控工具
 */

interface PerformanceMetrics {
  startTime: number;
  domContentLoaded: number;
  windowLoaded: number;
  appMounted: number;
  routeReady: number;
  resourcesLoaded: number;
}

class StartupPerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private startTime: number;

  constructor() {
    this.startTime = performance.now();
    this.metrics.startTime = this.startTime;
    this.initializeListeners();
  }

  private initializeListeners() {
    // DOM内容加载完成
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => {
        this.metrics.domContentLoaded = performance.now();
        this.logMetric("DOM Content Loaded", this.metrics.domContentLoaded);
      });
    } else {
      this.metrics.domContentLoaded = performance.now();
    }

    // 窗口加载完成
    if (document.readyState !== "complete") {
      window.addEventListener("load", () => {
        this.metrics.windowLoaded = performance.now();
        this.logMetric("Window Loaded", this.metrics.windowLoaded);
      });
    } else {
      this.metrics.windowLoaded = performance.now();
    }
  }

  // 记录应用挂载时间
  markAppMounted() {
    this.metrics.appMounted = performance.now();
    this.logMetric("App Mounted", this.metrics.appMounted);
  }

  // 记录路由准备完成时间
  markRouteReady() {
    this.metrics.routeReady = performance.now();
    this.logMetric("Route Ready", this.metrics.routeReady);
  }

  // 记录资源加载完成时间
  markResourcesLoaded() {
    this.metrics.resourcesLoaded = performance.now();
    this.logMetric("Resources Loaded", this.metrics.resourcesLoaded);
    this.generateReport();
  }

  private logMetric(name: string, time: number) {
    const duration = time - this.startTime;
    console.log(`🚀 [Performance] ${name}: ${duration.toFixed(2)}ms`);
  }

  // 生成性能报告
  private generateReport() {
    console.group("📊 启动性能报告");

    const report = {
      DOM内容加载: this.getTimeDiff("domContentLoaded"),
      窗口加载完成: this.getTimeDiff("windowLoaded"),
      应用挂载完成: this.getTimeDiff("appMounted"),
      路由准备完成: this.getTimeDiff("routeReady"),
      资源加载完成: this.getTimeDiff("resourcesLoaded"),
      总启动时间: this.getTimeDiff("resourcesLoaded")
    };

    Object.entries(report).forEach(([key, value]) => {
      if (value !== null) {
        const status = this.getPerformanceStatus(key, value);
        console.log(`${status} ${key}: ${value}ms`);
      }
    });

    // 性能建议
    this.generateSuggestions();

    console.groupEnd();
  }

  // 获取性能状态图标
  private getPerformanceStatus(metric: string, value: number): string {
    const thresholds: { [key: string]: number } = {
      DOM内容加载: 100,
      窗口加载完成: 200,
      应用挂载完成: 500,
      路由准备完成: 300,
      资源加载完成: 1000,
      总启动时间: 2000
    };

    const threshold = thresholds[metric] || 1000;
    if (value <= threshold * 0.5) return "🟢"; // 优秀
    if (value <= threshold) return "🟡"; // 良好
    return "🔴"; // 需要优化
  }

  private getTimeDiff(metric: keyof PerformanceMetrics): number | null {
    const time = this.metrics[metric];
    return time ? Number((time - this.startTime).toFixed(2)) : null;
  }

  private generateSuggestions() {
    const appMountTime = this.getTimeDiff("appMounted");
    const routeTime = this.getTimeDiff("routeReady");
    const resourceTime = this.getTimeDiff("resourcesLoaded");

    console.group("💡 性能优化建议");

    if (appMountTime && appMountTime > 1000) {
      console.warn("应用挂载时间较长，建议优化main.ts中的同步导入");
    }

    if (routeTime && routeTime > 1500) {
      console.warn("路由初始化时间较长，建议优化动态路由加载");
    }

    if (resourceTime && resourceTime > 3000) {
      console.warn("资源加载时间较长，建议实现更多的懒加载策略");
    }

    if (appMountTime && appMountTime < 500) {
      console.log("✅ 应用挂载速度良好");
    }

    console.groupEnd();
  }

  // 获取Web Vitals指标
  getWebVitals() {
    if ("web-vital" in window) {
      // 如果有web-vitals库，可以获取更详细的指标
      return;
    }

    // 简单的性能指标
    const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;

    if (navigation) {
      console.group("🌐 Web性能指标");
      console.log(`DNS查询: ${(navigation.domainLookupEnd - navigation.domainLookupStart).toFixed(2)}ms`);
      console.log(`TCP连接: ${(navigation.connectEnd - navigation.connectStart).toFixed(2)}ms`);
      console.log(`请求响应: ${(navigation.responseEnd - navigation.requestStart).toFixed(2)}ms`);
      console.log(`DOM解析: ${(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart).toFixed(2)}ms`);
      console.groupEnd();
    }
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new StartupPerformanceMonitor();

// 导出便捷方法
export const markAppMounted = () => performanceMonitor.markAppMounted();
export const markRouteReady = () => performanceMonitor.markRouteReady();
export const markResourcesLoaded = () => performanceMonitor.markResourcesLoaded();
