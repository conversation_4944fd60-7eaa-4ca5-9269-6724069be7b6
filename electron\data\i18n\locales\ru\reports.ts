/**
 * Связанное с отчетами - Русский
 */
export default {
  service: "Серви<PERSON> отчетов",
  description: "Отвечает за бизнес-логику запроса, экспорта, очистки исторических отчетов, отчетов об операциях, отчетов о неисправностях",
  getCommonReport: "Получить общий отчет",
  getCommonReportEntry: "Журнал входа в метод получения общего отчета",
  getGroupReport: "Получить групповой отчет",
  getOperateReport: "Получить отчет об операциях",
  getAuditReport: "Получить отчет аудита",
  exportCommonReport: "Экспорт общего отчета",
  clearReport: "Очистить отчет",
  refreshReport: "Обновить отчет",
  uploadWave: "Загрузка файла записи волн",
  cancelUpload: "Отмена загрузки файла записи волн",
  openWaveFile: "Открыть файл записи волн",
  getOperateReportEnd: "Получение отчета об операциях завершено",
  getAuditReportEnd: "Получение отчета аудита завершено",
  workbookName: "отчет",
  exportContent: "Поля экспортируемого содержимого",
  headers: {
    reportId: "Номер отчета",
    reportTime: "Время отчета",
    description: "Описание",
    name: "Название",
    time: "Время",
    operateAddress: "Адрес операции",
    operateParam: "Параметр операции",
    value: "Значение",
    step: "Шаг",
    source: "Источник",
    sourceType: "Тип источника",
    result: "Результат",
    module: "Модуль",
    message: "Сообщение",
    type: "Тип",
    level: "Уровень",
    origin: "Происхождение",
    user: "Пользователь"
  }
};
