# 翻译完整度检查脚本

本目录包含用于检查 VisualDebug 项目俄语翻译完整度的脚本工具。

## 脚本说明

### 1. check-translation-completeness.js
基础翻译完整度检查脚本，检查文件级别的翻译完整性。

**功能：**
- 检查前端翻译文件是否完整
- 检查后端翻译文件是否完整  
- 检查帮助文档结构是否匹配
- 简单的翻译键匹配检查

### 2. deep-translation-check.js
深度翻译结构检查脚本，详细分析翻译对象的嵌套结构。

**功能：**
- 解析 TypeScript 翻译文件的对象结构
- 递归检查嵌套翻译键的完整性
- 提供详细的缺失和多余键信息
- 生成匹配度统计报告

## 使用方法

### 方法一：直接运行脚本

```bash
# 基础检查
node scripts/check-translation-completeness.js

# 深度检查
node scripts/deep-translation-check.js
```

### 方法二：添加到 package.json

在项目根目录的 `package.json` 中添加以下脚本：

```json
{
  "scripts": {
    "check:translation": "node scripts/check-translation-completeness.js",
    "check:translation:deep": "node scripts/deep-translation-check.js",
    "check:translation:all": "npm run check:translation && npm run check:translation:deep"
  }
}
```

然后运行：

```bash
# 基础检查
npm run check:translation

# 深度检查  
npm run check:translation:deep

# 全面检查
npm run check:translation:all
```

## 输出说明

### 颜色标识
- 🟢 **绿色**: 检查通过，翻译完整
- 🔴 **红色**: 发现问题，需要修复
- 🟡 **黄色**: 警告信息，建议检查
- 🔵 **蓝色**: 信息提示
- 🟣 **紫色**: 标题和分隔符
- 🔷 **青色**: 统计数据

### 检查结果类型

#### ✅ 完整匹配
```
✅ common/layout.ts 翻译完整
```

#### ❌ 缺失翻译
```
❌ 缺失俄语翻译文件: business/tools/xml.ts
❌ tools/debug.ts 缺失翻译键: backup, messageMonitor
```

#### ⚠️ 多余内容
```
⚠️ 多余的俄语翻译文件: extra/unused.ts
⚠️ tools/tools.ts 多余翻译键: obsoleteKey
```

#### 📊 匹配度统计
```
📊 匹配度: 95/100 (95%)
检查的文件对: 25
完美匹配: 20 (80%)
总翻译键: 1500
匹配的键: 1425 (95%)
```

## 检查范围

### 前端翻译
- `frontend/src/languages/zh/` - 中文翻译
- `frontend/src/languages/ru/` - 俄语翻译

检查的文件类型：
- `*.ts` - TypeScript 翻译文件
- 递归检查所有子目录

### 后端翻译
- `electron/data/i18n/locales/zh/` - 中文翻译
- `electron/data/i18n/locales/ru/` - 俄语翻译

检查的文件类型：
- `*.ts` - TypeScript 翻译文件
- `*.js` - JavaScript 翻译文件

### 帮助文档
- `frontend/public/help.md` - 中文帮助文档
- `frontend/public/help-ru.md` - 俄语帮助文档

检查内容：
- 章节数量匹配 (`## 数字. 标题`)
- 子章节数量匹配 (`### 数字.数字 标题`)

## 常见问题解决

### 1. 缺失翻译文件
**问题**: `❌ 缺失俄语翻译文件: business/tools/xml.ts`

**解决**: 创建对应的俄语翻译文件，参考中文版本的结构。

### 2. 缺失翻译键
**问题**: `❌ tools/debug.ts 缺失翻译键: backup, messageMonitor`

**解决**: 在俄语翻译文件中添加缺失的翻译键。

### 3. 文件解析失败
**问题**: `❌ 解析对象失败: tools/debug.ts - Unexpected token`

**解决**: 检查 TypeScript 文件语法，确保 export default 对象格式正确。

### 4. 帮助文档结构不匹配
**问题**: `❌ 帮助文档结构不完整`

**解决**: 检查俄语帮助文档的章节结构，确保与中文版本一致。

## 开发建议

### 1. 定期检查
建议在以下情况运行翻译检查：
- 添加新的翻译文件后
- 修改翻译结构后
- 发布版本前
- CI/CD 流程中

### 2. 自动化集成
可以将检查脚本集成到：
- Git pre-commit hooks
- CI/CD 管道
- 开发环境的 watch 模式

### 3. 翻译规范
- 保持翻译文件结构与中文版本一致
- 使用有意义的翻译键名
- 避免硬编码文本
- 定期更新翻译内容

## 脚本维护

### 更新检查逻辑
如需修改检查逻辑，请编辑对应的脚本文件：
- 基础检查: `check-translation-completeness.js`
- 深度检查: `deep-translation-check.js`

### 添加新的检查项
可以扩展脚本以支持：
- 翻译内容质量检查
- 翻译键命名规范检查
- 翻译文本长度检查
- 特殊字符和格式检查

---

**注意**: 这些脚本仅用于检查翻译完整性，不会修改任何文件。发现问题后需要手动修复翻译文件。
