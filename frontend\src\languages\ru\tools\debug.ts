export default {
  search: {
    placeholder: "Поиск устройства",
    button: "Поиск",
    success: "Поиск успешен"
  },
  device2: {
    search: {
      placeholder: "Поиск устройства",
      add: "Добавить устройство",
      duplicate: "Этот IP и порт уже существуют, пожалуйста, не добавляйте повторно"
    },
    list: {
      empty: "Устройство не найдено",
      unnamed: "Безымянное устройство",
      status: {
        connected: "Подключено",
        disconnected: "Отключено"
      },
      contextMenu: {
        connect: "Подключить",
        edit: "Редактировать",
        disconnect: "Отключить",
        remove: "Удалить"
      },
      message: {
        disconnectFirst: "Пожалуйста, сначала отключите соединение перед редактированием",
        disconnectFirstDelete: "Пожалуйста, сначала отключите соединение перед удалением",
        connectSuccess: "Устройство {name}: подключение успешно",
        connectExists: "Устройство {name}: соединение уже существует",
        connectFailed: "Устройство {name}: подключение не удалось",
        connectFailedReason: "Причина неудачи подключения устройства: {reason}",
        disconnected: "Устройство {name}: отключено",
        operationFailed: "Устройство {name}: операция не удалась"
      }
    },
    report: {
      group: {
        openWaveConfirm: "Открыть файл волн через сторонний инструмент?",
        tips: "Дружеское напоминание",
        noWaveTool: "Путь к стороннему инструменту анализа волн не настроен"
      },
      common: {
        selectRow: "Пожалуйста, выберите строку для операции"
      }
    },
    backup: {
      savePath: "Путь сохранения",
      setPath: "Пожалуйста, установите путь резервного копирования",
      setPathTitle: "Установить путь резервного копирования",
      locateFolder: "Найти папку",
      startBackup: "Начать резервное копирование",
      cancelBackup: "Отменить резервное копирование",
      backup: "Резервное копирование",
      sequence: "Номер",
      backupType: "Тип резервного копирования",
      backupDesc: "Описание резервного копирования",
      progress: "Прогресс",
      status: "Статус",
      noTypeSelected: "Пожалуйста, выберите тип резервного копирования",
      backupSuccess: "Резервное копирование успешно",
      backupFailed: "Резервное копирование не удалось",
      openFolderFailed: "Не удалось открыть папку",
      backupTypes: {
        paramValue: "Значения параметров",
        faultInfo: "Информация о неисправностях",
        cidConfigPrjLog: "Журнал проекта конфигурации CID",
        waveReport: "Отчет о волнах"
      },
      backupDescTypes: {
        paramValue: "Резервное копирование всех установленных значений параметров устройства",
        faultInfo: "Резервное копирование информации о записи неисправностей устройства",
        cidConfigPrjLog: "Резервное копирование файлов конфигурации CID и журналов проекта",
        waveReport: "Резервное копирование файлов отчетов анализа волн"
      },
      backupStatus: {
        userCancelled: "Отменено пользователем",
        transferring: "Передача"
      },
      console: {
        pathNotSet: "Путь резервного копирования не установлен, невозможно начать резервное копирование",
        noTypeSelected: "Тип резервного копирования не выбран, невозможно начать резервное копирование",
        startBackup: "Начало резервного копирования, тип: {types}, путь: {path}",
        backupException: "Исключение резервного копирования: {error}",
        pathSelected: "Выбран путь резервного копирования: {path}",
        pathNotSelected: "Путь резервного копирования не выбран",
        pathNotSetForLocate: "Путь резервного копирования не установлен, невозможно найти папку",
        folderOpened: "Открыта папка резервного копирования: {path}",
        openFolderFailed: "Не удалось открыть папку резервного копирования: {error}",
        taskCompleted: "Обработка задачи завершена",
        taskCancelled: "Задача отменена",
        typeError: "Ошибка типа [{type}]: {error}",
        typeCompleted: "Резервное копирование типа [{type}] завершено",
        typeCancelled: "Тип [{type}] отменен",
        typeFailed: "Тип [{type}] не удался"
      }
    },
    remoteControl: {
      directControl: "Прямое управление",
      selectControl: "Селективное управление"
    },
    messageMonitor: {
      title: "Мониторинг сообщений",
      start: "Начать мониторинг",
      stop: "Остановить мониторинг",
      clear: "Очистить",
      export: "Экспорт",
      expand: "Развернуть",
      collapse: "Свернуть",
      close: "Закрыть",
      messageType: "Сообщение",
      noMessages: "Нет данных сообщений",
      noMessagesToExport: "Нет данных сообщений для экспорта",
      startSuccess: "Начат мониторинг сообщений",
      stopSuccess: "Остановлен мониторинг сообщений",
      clearSuccess: "Очистка сообщений успешна",
      exportSuccess: "Экспорт сообщений успешен",
      exportFailed: "Экспорт сообщений не удался",
      toggleFailed: "Переключение состояния мониторинга не удалось"
    }
  }
};
