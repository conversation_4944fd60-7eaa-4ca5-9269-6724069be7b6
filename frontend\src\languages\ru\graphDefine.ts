export default {
  equipmentList: {
    sequence: "Номе<PERSON>",
    name: "Название",
    type: "Тип",
    operation: "Операция",
    preview: "Предпросмотр",
    copy: "Копировать",
    delete: "Удалить",
    confirmDelete: "Вы уверены, что хотите удалить?",
    tip: "Информация подсказки",
    error: "Ош<PERSON>бка"
  },
  graphComponent: {
    deviceType: "Тип устройства",
    deviceName: "Название устройства",
    save: "Сохранить"
  },
  contextMenu: {
    group: "Группировать",
    ungroup: "Разгруппировать",
    setStatus: "Установить статус",
    copy: "Копировать",
    delete: "Удалить",
    rename: "Переименовать"
  },
  graphCreate: {
    needTwoDevices: "Переключатель или разъединитель требует выбора двух графических устройств",
    needCorrectStatus: "Пожалуйста, установите правильные атрибуты состояния для переключателя или разъединителя",
    needOneDevice: "Пожалуйста, выберите одно графическое устройство"
  },
  graphDefine: {
    waitCanvasInit: "Пожалуйста, дождитесь завершения инициализации холста",
    selectOneGraph: "Пожалуйста, выберите один символ",
    tip: "Подсказка"
  },
  setStatus: {
    open: "Открыть",
    close: "Закрыть",
    none: "Нет"
  },
  graphTools: {
    undo: "Отменить",
    redo: "Повторить",
    front: "На передний план",
    back: "На задний план",
    delete: "Удалить",
    save: "Сохранить",
    equipmentList: "Список оборудования"
  },
  graphEditor: {
    dataConfig: "Конфигурация данных",
    loadEquipmentFailed: "Не удалось загрузить символ"
  }
};
