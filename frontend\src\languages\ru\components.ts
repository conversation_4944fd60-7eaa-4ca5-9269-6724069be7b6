export default {
  checkCard: {
    default: "По умолчанию"
  },
  chooseModule: {
    title: "Выбрать приложение",
    noModule: "Модуль не найден!",
    setDefault: "Установить по умолчанию",
    cancel: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    confirm: "Подтвердить"
  },
  closer: {
    title: "Подтверждение выхода",
    message: "Вы хотите выйти?",
    confirm: "Подтвердить",
    minimize: "Свернуть в трей",
    cancel: "Отмена"
  },
  codeHighLight: {
    noCode: "Нет"
  },
  cropUpload: {
    title: "Обрезка изображения",
    zoomIn: "Увеличить",
    zoomOut: "Уменьшить",
    rotateLeft: "Повернуть влево",
    rotateRight: "Повернуть вправо",
    uploadImage: "Нажмите для загрузки изображения",
    uploadTip: "Пожалуйста, загрузите файл изображения, рекомендуется не более 2M",
    cancel: "Отмена",
    confirm: "Подтвердить"
  },
  error: {
    forbidden: "Извините, у вас нет доступа к этой странице~🙅‍♂️🙅‍♀️",
    notFound: "Извините, страница, которую вы ищете, не существует~🤷‍♂️🤷‍♀️",
    serverError: "Извините, ваша сеть пропала~🤦‍♂️🤦‍♀️",
    back: "Вернуться на предыдущую страницу"
  },
  form: {
    input: {
      placeholder: "Пожалуйста, заполните {label}"
    },
    select: {
      placeholder: "Пожалуйста, выберите {label}"
    },
    button: {
      add: "Добавить",
      edit: "Редактировать",
      delete: "Удалить",
      view: "Просмотр"
    },
    search: {
      inputPlaceholder: "Пожалуйста, введите",
      selectPlaceholder: "Пожалуйста, выберите",
      rangeSeparator: "до",
      startPlaceholder: "Время начала",
      endPlaceholder: "Время окончания"
    }
  },
  selectIcon: {
    title: "Выбор иконки",
    placeholder: "Пожалуйста, выберите иконку",
    searchPlaceholder: "Поиск иконки",
    noSearchResult: "Иконка, которую вы ищете, не найдена~",
    moreIcons: "Больше иконок",
    enterIconifyCode: "Пожалуйста, введите код иконки iconify, например mdi:home-variant",
    iconifyAddress: "Адрес iconify",
    localIcons: "Локальные иконки"
  },
  selector: {
    add: "Добавить",
    addCurrent: "Добавить текущий",
    addSelected: "Добавить выбранные",
    delete: "Удалить",
    deleteCurrent: "Удалить текущий",
    deleteSelected: "Удалить выбранные",
    cancel: "Отмена",
    confirm: "Подтвердить",
    selected: "Выбрано",
    maxSelect: "Максимум выбрать",
    singleSelectOnly: "Можно выбрать только один",
    maxSelectLimit: "Максимум выбрать {count}",
    person: "человек"
  },
  upload: {
    view: "Просмотр",
    edit: "Редактировать",
    delete: "Удалить",
    uploadImage: "Пожалуйста, загрузите изображение",
    uploadSuccess: "Изображение загружено успешно!",
    uploadFailed: "Загрузка изображения не удалась, пожалуйста, загрузите снова!",
    invalidFormat: "Загруженное изображение не соответствует требуемому формату!",
    fileSizeExceeded: "Размер загружаемого изображения не может превышать {size}M!",
    maxFilesExceeded: "В настоящее время можно загрузить максимум {limit} изображений, пожалуйста, удалите и загрузите снова!",
    fileSizeZero: "Файл {fileName} имеет размер 0, загрузка невозможна!",
    tips: "Дружеское напоминание"
  },
  treeFilter: {
    searchPlaceholder: "Введите ключевые слова для фильтрации",
    expandAll: "Развернуть все",
    collapseAll: "Свернуть все",
    all: "Все"
  },
  proTable: {
    search: {
      reset: "Сброс",
      search: "Поиск",
      expand: "Развернуть",
      collapse: "Свернуть"
    },
    pagination: {
      total: "Всего {total} записей",
      pageSize: "записей/страница",
      goto: "Перейти к",
      page: "странице"
    },
    colSetting: {
      title: "Настройки столбцов",
      fixedLeft: "Показывать ли",
      fixedRight: "Сортировать ли",
      cancelFixed: "Отменить фиксацию",
      reset: "Восстановить по умолчанию",
      confirm: "Подтвердить",
      cancel: "Отмена"
    },
    table: {
      empty: "Нет данных"
    }
  },
  basicComponent: {
    title: "Базовые компоненты",
    line: "Линия",
    text: "Текст",
    rect: "Прямоугольник",
    circle: "Круг",
    ellipse: "Эллипс",
    triangle: "Треугольник",
    arc: "Дуга"
  }
};
