export default {
  common: {
    date: "Date",
    search: "Rechercher",
    save: "Enregistrer",
    clear: "Effacer",
    loading: "Chargement...",
    reportNo: "Numéro de rapport",
    time: "Heure",
    description: "Description",
    progress: "Progression",
    selectDateRange: "Veuillez sélectionner la plage de dates",
    noData: "Aucune donnée",
    saveSuccess: "Enregistrement réussi",
    saveFailed: "Échec de l'enregistrement"
  },
  date: "Date",
  search: "Rechercher",
  filter: "Filtrer",
  save: "Enregistrer",
  clearList: "Effacer la liste",
  loading: "Chargement...",
  reportNumber: "Numéro de rapport",
  time: "Heure",
  description: "Description",
  progress: "Progression",
  loadingText: "Chargement...",
  querying: "Requête en cours",
  selectCompleteTimeRange: "Veuillez sélectionner la plage de temps complète",
  noDataToSave: "Aucune donnée à enregistrer",
  saveSuccess: "Enregistrement réussi",
  saveReport: "Enregistrer le rapport",
  fileUploading: "Téléchargement de fichier en cours",
  fileUploadComplete: "Téléchargement de fichier terminé",
  autoRefresh: "Actualisation automatique",
  showHiddenItems: "Afficher les éléments masqués",
  hideHiddenItems: "Masquer les éléments masqués",
  name: "Nom",
  operationAddress: "Adresse d'opération",
  operationParams: "Paramètres d'opération",
  value: "Valeur",
  step: "Étape",
  source: "Source",
  sourceType: "Type de source",
  result: "Résultat",
  searchType: "Type de recherche",
  total: "Total {num} éléments",
  sameSearch: "Recherche de contenu identique",
  sameFilter: "Filtrage de contenu identique",
  showHideTime: "Afficher/masquer la colonne de temps",
  selectRowToOperate: "Veuillez d'abord sélectionner la ligne à opérer",
  trip: {
    autoRefresh: "Actualisation automatique"
  },
  operate: {
    name: "Nom",
    operateAddress: "Adresse d'opération",
    operateParam: "Paramètre d'opération",
    value: "Valeur",
    step: "Étape",
    source: "Source",
    sourceType: "Type de source",
    result: "Résultat"
  },
  group: {
    uploadWave: "Télécharger la forme d'onde",
    searchHistory: "Historique de recherche",
    saveResult: "Enregistrer le résultat",
    clearContent: "Effacer le contenu",
    contextMenu: {
      uploadWave: "Télécharger la forme d'onde",
      getHistoryReport: "Obtenir le rapport historique",
      saveResult: "Enregistrer le résultat",
      clearContent: "Effacer le contenu"
    },
    date: "Date",
    search: "Rechercher",
    save: "Enregistrer",
    clearList: "Effacer la liste",
    loading: "Chargement...",
    table: {
      reportId: "ID du rapport",
      time: "Heure",
      description: "Description"
    },
    progress: {
      title: "Progression",
      searching: "Recherche {type}",
      loading: "Chargement..."
    },
    refresh: {
      start: "Démarrer l'actualisation",
      stop: "Arrêter l'actualisation"
    },
    hiddenItems: {
      show: "Afficher les éléments masqués"
    },
    messages: {
      noFileToUpload: "Aucun fichier à télécharger",
      selectDateRange: "Veuillez sélectionner la plage de dates",
      noDataToSave: "Aucune donnée à enregistrer",
      saveReport: "Enregistrer le rapport",
      saveSuccess: "Enregistrement réussi"
    }
  },
  entryID: "Indice",
  module: "Nom du module",
  msg: "Contenu",
  level: "Niveau",
  type: "Type",
  origin: "Origine",
  user: "Nom d'utilisateur",
  exporting: "Exportation en cours...",
  stopRefresh: "Arrêter l'Actualisation",
  searchProgress: "Recherche {type}",
  pleaseSelectSavePath: "Veuillez sélectionner le chemin d'enregistrement...",
  saveFailed: "Échec de l'enregistrement",
  exportLogSuccess: "Exportation réussie : {path}",
  exportLogFailed: "Échec de l'exportation : {msg}",
  exportLogCancelled: "L'utilisateur a annulé l'opération d'exportation"
};
