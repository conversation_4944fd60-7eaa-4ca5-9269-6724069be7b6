export default {
  paramList: {
    title: "Lista de Parámetros",
    paramGroup: "Grupo de Parámetros",
    groupName: "Nombre de Grupo",
    paramName: "Nombre",
    paramDesc: "Descripción",
    exportDefaultPath: "Lista de Parámetros",
    exportTitle: "Exportar Lista de Parámetros",
    importTitle: "Importar Lista de Parámetros",
    buttons: {
      import: "Importar",
      export: "Exportar",
      clear: "Limpiar"
    },
    export: {
      defaultPath: "Lista de Parámetros",
      title: "Exportar Lista de Parámetros"
    },
    dialog: {
      title: "Aviso",
      confirm: "Confirmar"
    },
    messages: {
      exportSuccess: "Exportación exitosa",
      exportFailed: "Error en la exportación",
      importSuccess: "Importación exitosa",
      importFailed: "Error en la importación",
      clearSuccess: "Limpieza exitosa"
    },
    columns: {
      index: "Índice",
      groupName: "Nombre de Grupo",
      name: "Nombre",
      description: "Descripción",
      minValue: "<PERSON>or <PERSON>",
      maxValue: "Valor Máximo",
      step: "Paso",
      unit: "Unidad",
      value: "Valor"
    },
    search: {
      paramName: "Nombre",
      paramDesc: "Descripción"
    },
    searchParamName: "Nombre",
    searchParamDesc: "Descripción"
  }
};
