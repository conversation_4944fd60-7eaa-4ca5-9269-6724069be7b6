<template>
  <v-contextmenu ref="contextmenu">
    <v-contextmenu-item @click="sameSearch">
      <svg-icon icon="ant-design:search-outlined" class="menu-svg" />
      <span>{{ t("report.sameSearch") }}</span>
    </v-contextmenu-item>
    <v-contextmenu-item @click="sameFilter">
      <svg-icon icon="ant-design:filter-outlined" class="menu-svg" />
      <span>{{ t("report.sameFilter") }}</span>
    </v-contextmenu-item>
  </v-contextmenu>
  <div class="table-main report-page">
    <div class="flex flex-wrap header card button-group">
      <el-form :inline="true" class="report-query-form" style="width: 100%">
        <el-form-item>
          <el-checkbox v-model="isDateCheck">{{ t("report.date") }}：</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-date-picker v-model="dateRange" type="datetimerange"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-text type="primary">{{ t("report.total", { num: totalNum }) }}</el-text>
        </el-form-item>
      </el-form>
      <div class="header-button-ri" style="width: 100%; margin-top: 4px; text-align: right">
        <el-button type="primary" :icon="Search" :disabled="isButtonClick" @click="searchReport">{{ t("report.search") }}</el-button>
        <el-button type="success" :icon="Folder" :disabled="isButtonClick" @click="exportReport">{{ t("report.save") }}</el-button>
        <el-button type="danger" plain :icon="Delete" @click="clearList">{{ t("report.clearList") }}</el-button>
      </div>
    </div>
    <div class="card table-box report-table" v-contextmenu:contextmenu="contextmenu">
      <ProTable
        ref="proTable"
        :data="tableData"
        :columns="proTableColumns"
        :pagination="true"
        :tool-button="false"
        :border="true"
        :stripe="true"
        :loading="tableLoad"
        :pager-count="7"
        :request-auto="false"
        row-key="entryID"
        @cell-contextmenu="cellContextmenu"
      >
        <template #tableHeader>
          <span></span>
        </template>
      </ProTable>
    </div>
  </div>
  <el-dialog
    v-model="dialogShow.searchProgress"
    width="60%"
    class="hover"
    :align-center="true"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :show-close="false"
    draggable
    :title="t('report.progress')"
  >
    <div style="height: 120px; padding: 15px">
      <el-progress :percentage="dialogShow.percentage" :text-inside="false" striped striped-flow style="margin-top: 60px">
        <span>{{ dialogShow.progressText }}</span>
      </el-progress>
    </div>
  </el-dialog>
  <ProgressDialog ref="progressDialog" />
</template>
<script setup lang="ts">
import { Delete, Search, Folder } from "@element-plus/icons-vue";
import Message from "@/scripts/message";
import { ipc } from "@/api/request/ipcRenderer";
import { getDateZh } from "@/utils/index";
import { IECNotify, reportApi, ReportParam, ResultData } from "@/api";
import { useDebugStore } from "@/stores/modules/debug";
import { genRpcTimeParam } from "@/utils/iec/iecRpcUtils";
import { osControlApi } from "@/api/modules/biz/os";
import { ref, computed } from "vue";
import { ContextmenuInstance } from "v-contextmenu/es/types";
import { useI18n } from "vue-i18n";
import { ElMessageBox } from "element-plus";
import ProgressDialog from "../dialog/ProgressDialog.vue";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps } from "@/components/ProTable/interface";

const { t } = useI18n();
// 透传装置ID
const props = defineProps<{ deviceId: string }>();
const { report, addConsole } = useDebugStore();
const globalReport = report.get(props.deviceId);

// 初始化查询条件
const getDefaultDateRange = (): [Date, Date] => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return [yesterday, new Date()];
};

const dateRange = ref<[Date, Date]>(getDefaultDateRange());
const isDateCheck = ref<boolean>(false);
const isButtonClick = ref<boolean>(false);
const tableLoad = ref<boolean>(false);
const tableData = ref<ReportParam.IECRpcOperateReportRes[]>([]);
const totalNum = computed(() => {
  return tableData.value.length;
});

// 右键菜单相关
const contextmenu = ref<ContextmenuInstance>();
let currLine: any = undefined;
const cellContextmenu = (row): void => {
  currLine = row;
};
const searchInfo = ref("");
const searchType = ref(0);

// ProTable列配置
const proTableColumns: ColumnProps<ReportParam.IECRpcOperateReportRes>[] = [
  { prop: "entryID", label: t("report.reportNumber"), width: 110, sortable: true },
  { prop: "name", label: t("report.name"), sortable: true },
  { prop: "time", label: t("report.time"), width: 220 },
  { prop: "conID", label: t("report.operationAddress"), width: 150 },
  { prop: "para", label: t("report.operationParams") },
  { prop: "val", label: t("report.value") },
  { prop: "step", label: t("report.step") },
  { prop: "srcID", label: t("report.source") },
  { prop: "srcType", label: t("report.sourceType"), width: 80 },
  { prop: "cause", label: t("report.result") }
];

const proTable = ref();
const dialogShow = ref({
  searchProgress: false,
  percentage: 0,
  progressText: ""
});
const progressDialog = ref();
const getTableData = (): ReportParam.IECRpcOperateReportRes[] => {
  return globalReport!.operateReport.get(globalReport?.newname || globalReport?.currReportType || "") || [];
};

// 保存查询条件到缓存
const saveQueryConditions = () => {
  if (!globalReport) return;
  const reportKey = globalReport.newname || globalReport.currReportType || "";
  if (!reportKey) return;

  globalReport.queryConditions.set(reportKey, {
    isDateCheck: isDateCheck.value,
    dateRange: [new Date(dateRange.value[0]), new Date(dateRange.value[1])],
    searchInfo: "",
    searchType: 0
  });
};

// 从缓存恢复查询条件
const restoreQueryConditions = () => {
  if (!globalReport) return;
  const reportKey = globalReport.newname || globalReport.currReportType || "";
  if (!reportKey) return;

  const cached = globalReport.queryConditions.get(reportKey);
  if (cached) {
    isDateCheck.value = cached.isDateCheck;
    dateRange.value = [new Date(cached.dateRange[0]), new Date(cached.dateRange[1])];
  } else {
    // 如果没有缓存，使用默认值
    isDateCheck.value = false;
    dateRange.value = getDefaultDateRange();
  }
};
// let searchStartTime = 0; // 记录搜索开始时间（仅调试使用，移除以通过 eslint）
const clearList = (): void => {
  globalReport!.operateReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  tableData.value = [];
};

const searchReport = async (): Promise<void> => {
  if (isDateCheck.value && dateRange.value.length < 2) {
    Message.warning(t("report.selectCompleteTimeRange"));
    return;
  }

  // 保存查询条件到缓存
  saveQueryConditions();

  const arg: ReportParam.IECRpcReportSearchParam = {
    type: globalReport!.currReportType,
    startTime: isDateCheck.value ? genRpcTimeParam(dateRange.value[0]) : "",
    stopTime: isDateCheck.value ? genRpcTimeParam(dateRange.value[1]) : ""
  };
  globalReport!.operateReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  tableLoad.value = true;
  isButtonClick.value = true;
  dialogShow.value.percentage = 0;
  dialogShow.value.searchProgress = true; // 弹窗立即显示
  const reportDesc: string = globalReport!.currReportDesc;
  dialogShow.value.progressText = t("report.querying") + reportDesc;

  // 记录搜索开始时间（已移除searchStartTime变量以通过eslint）
  // const _start = Date.now();

  // 调用API，进度将通过回调通知更新
  const res: ResultData<any> = await reportApi.getOperateReportListByDevice(props.deviceId, arg);

  if (res.code != 1) {
    tableLoad.value = false;
    isButtonClick.value = false;
    dialogShow.value.searchProgress = false;
    Message.warning(res.msg);
    return;
  }
};

const exportReport = async (): Promise<void> => {
  if (getTableData().length == 0) {
    Message.warning(t("report.noDataToSave"));
    addConsole(t("report.exportLogFailed", { msg: t("report.noDataToSave") }));
    return;
  }

  // 第一步：先选择保存路径，不显示进度条
  const reportDesc: string = globalReport!.currReportDesc;
  const path = await osControlApi.openSaveFileDialogByParams({
    title: t("report.saveReport"),
    defaultPath: reportDesc + getDateZh(),
    filterList: [
      { name: "Rpt", extensions: ["rpt"] },
      { name: "Excel", extensions: ["xlsx"] }
    ]
  });

  // 如果用户取消选择路径，直接返回，不显示任何错误信息
  if (!path) {
    addConsole(t("report.exportLogCancelled"));
    return;
  }

  // 第二步：用户确认路径后，才显示进度条并开始导出
  progressDialog.value.show();
  progressDialog.value.setProgress(5, t("report.exporting"), false);
  let fakeProgress = setInterval(() => {
    if (progressDialog.value.progressDialog.percentage < 95) {
      progressDialog.value.setProgress(progressDialog.value.progressDialog.percentage + 5, t("report.exporting"));
    }
  }, 100);
  const exportList: any[] = [];
  getTableData().forEach(obj => {
    exportList.push({
      entryID: obj.entryID,
      name: obj.name,
      time: obj.time,
      conID: obj.conID,
      para: obj.para,
      val: obj.val,
      step: obj.step,
      srcID: obj.srcID,
      srcType: obj.srcType,
      cause: obj.cause
    });
  });
  const arg: ReportParam.IECRpcCommonReportExportParam = {
    type: globalReport!.currReportType,
    method: globalReport!.currReportMethod,
    path: path as unknown as string,
    items: exportList
  };
  const res: ResultData<any> = await reportApi.exportCommonReportByDevice(props.deviceId, arg);
  clearInterval(fakeProgress);
  progressDialog.value.setProgress(100, t("report.exporting"));
  setTimeout(() => progressDialog.value.hide(), 500);
  if (res.code != 1) {
    Message.warning(res.msg);
    addConsole(t("report.exportLogFailed", { msg: res.msg }));
    return;
  }
  Message.success(t("report.saveSuccess"));
  addConsole(t("report.exportLogSuccess", { path }));
  await ElMessageBox.alert(t("report.saveSuccess"), t("report.progress"), {
    confirmButtonText: t("common.confirm") || "确定",
    type: "success"
  });
};

// 右键菜单方法
const sameSearch = (): void => {
  if (!currLine) {
    Message.warning(t("report.selectRowToOperate"));
    return;
  }
  let sameDesc = currLine.name || "";
  searchType.value = 0;
  searchInfo.value = sameDesc;
};

const sameFilter = (): void => {
  if (!currLine) {
    Message.warning(t("report.selectRowToOperate"));
    return;
  }
  let sameDesc = currLine.name || "";
  searchType.value = 1;
  searchInfo.value = sameDesc;
};

const initTableData = (): void => {
  if (getTableData().length > 0) {
    dialogShow.value.percentage = 50;
    dialogShow.value.searchProgress = true;
    dialogShow.value.progressText = t("report.loadingText");
  }
  nextTick(() => {
    setTimeout(() => {
      tableData.value = getTableData();
      dialogShow.value.percentage = 100;
      dialogShow.value.searchProgress = false;
    }, 50);
  });
};

ipc.on("report_notify", (_event: unknown, notify: IECNotify) => {
  // 多装置过滤：仅处理当前组件对应的 deviceId 事件
  if (notify.deviceId && notify.deviceId !== props.deviceId) return;
  const reportData = notify.data as any;
  if (notify.type == "readOperateReport") {
    if (reportData.code != 1) {
      tableLoad.value = false;
      isButtonClick.value = false;
      dialogShow.value.percentage = 100;
      dialogShow.value.searchProgress = false;
      Message.warning(reportData.msg);
      return;
    }

    console.log(
      `[OperateReport UI] 处理操作报告通知 - isPartial: ${notify.isPartial}, 数据类型: ${reportData.data?.progress ? "进度" : "数据"}, 当前界面状态: tableLoad=${tableLoad.value}, searchProgress=${dialogShow.value.searchProgress}`
    );

    // 检查是否为进度更新
    if (reportData.data?.progress?.isProgress) {
      // 这是进度更新，只更新进度条，不更新表格数据
      const progressInfo = reportData.data.progress;
      const currentCount = progressInfo.currentCount;
      const callbackCount = progressInfo.callbackCount;

      // 基于实际数据量计算进度
      let progressPercent = 0;
      if (currentCount > 0) {
        // 使用对数函数计算进度，避免进度跳跃过快
        progressPercent = Math.min(85, Math.floor(Math.log(currentCount + 1) * 10 + callbackCount * 2));
      } else {
        progressPercent = Math.min(20, callbackCount * 3);
      }

      // 确保进度不倒退
      progressPercent = Math.max(dialogShow.value.percentage, progressPercent);

      dialogShow.value.percentage = progressPercent;
      dialogShow.value.progressText = t("report.querying") + globalReport!.currReportDesc + ` (${currentCount} ${t("report.items")})`;

      console.log(`[OperateReport UI] 进度更新完成 - 数据量: ${currentCount}条, 回调次数: ${callbackCount}, 进度: ${progressPercent}%`);
      return;
    }

    // 这是最终数据更新
    if (Array.isArray(reportData.data)) {
      console.log(`[OperateReport UI] 处理最终数据 - 原始数据量: ${reportData.data.length}条`);

      let filteredList = reportData.data;
      if (globalReport!.keyword && globalReport!.keyword.trim() !== "") {
        filteredList = reportData.data.filter(item => (item.name || "").toLowerCase().includes(globalReport!.keyword.toLowerCase()));
        console.log(`[OperateReport UI] 数据过滤完成 - 原始数据: ${reportData.data.length}条, 过滤后: ${filteredList.length}条`);
      }

      // 最终数据直接渲染到表格
      tableData.value = filteredList;
      globalReport!.operateReport.set(globalReport?.newname || globalReport?.currReportType || "", filteredList);

      // 完成加载
      tableLoad.value = false;
      isButtonClick.value = false;
      dialogShow.value.percentage = 100;
      dialogShow.value.searchProgress = false;

      console.log(`[OperateReport UI] 最终数据处理完成 - 最终数据量: ${filteredList.length}条`);
    } else {
      // 空数据情况
      console.log(`[OperateReport UI] 处理空数据情况`);
      globalReport!.operateReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
      tableData.value = [];

      tableLoad.value = false;
      isButtonClick.value = false;
      dialogShow.value.percentage = 100;
      dialogShow.value.searchProgress = false;

      console.log(`[OperateReport UI] 空数据处理完成`);
    }
  }
});

onMounted(() => {
  initTableData();
});

onBeforeUnmount(() => {
  ipc.removeAllListeners("report_notify");
});
watch(
  () => globalReport!.currReportDesc,
  () => {
    initTableData();
  }
);

watch(
  () => globalReport!.newname,
  () => {
    tableData.value = getTableData();
    isButtonClick.value = false;

    // 恢复查询条件而不是重置
    restoreQueryConditions();
  }
);
</script>
<style scoped lang="scss">
.report-page {
  margin-top: 5px;
  .button-group {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    .search-page {
      font-size: 14px;
    }
    .his-var {
      font-size: 14px;
    }
    .el-row {
      div {
        font-size: 14px;
        :deep(.el-date-editor) {
          width: 340px;
        }
      }
    }
  }
  .report-table {
    overflow-y: auto;
    scrollbar-width: none;
  }
}
.header {
  margin-bottom: 5px;
}
.no-warp-cell .cell {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}
</style>
