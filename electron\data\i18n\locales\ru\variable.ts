/**
 * Связанное с переменными - Русский
 */
export default {
  registerFailed: "Регистрация переменной не удалась, причина неудачи:",
  modifyFailed: "Изменение переменной не удалось, причина неудачи:",
  deleteFailed: "Удаление переменной не удалось, причина неудачи:",
  modifyError: "Изменение переменной не удалось",
  deleteError: "Удаление переменной не удалось",
  debugVariables: "Отладочные переменные устройства",
  variableNameExists: "Имя переменной {name} уже существует!",
  variableNameExistsImport: "Имя переменной {name} уже существует!",
  importKeyMapping: {
    variableName: "Имя переменной"
  },
  headers: {
    index: "Номер",
    name: "Имя переменной",
    description: "Описание переменной",
    type: "Тип переменной",
    value: "Значение переменной"
  }
};
